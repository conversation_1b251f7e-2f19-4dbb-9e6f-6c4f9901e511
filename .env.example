### .env.example - Environment Variables Template

# Database
DATABASE_URL=sqlite:///./ebooks.db

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI APIs
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# External APIs
GOOGLE_TRENDS_API_KEY=your-google-trends-key
AMAZON_API_KEY=your-amazon-api-key

# Storage
STORAGE_PATH=./storage

# KDP Credentials (will be encrypted in database)
KDP_EMAIL=<EMAIL>
KDP_PASSWORD=your-kdp-password

# Generation Settings
MIN_BOOK_LENGTH=5000
MAX_BOOK_LENGTH=15000
BOOKS_PER_TREND=3

# VERL Settings
ENABLE_VERL=true
VERL_MODEL_NAME=microsoft/DialoGPT-medium
VERL_BATCH_SIZE=8
VERL_LEARNING_RATE=1e-5
VERL_TRAINING_EPOCHS=3

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
CELERY_LOG_LEVEL=INFO
CELERY_CONCURRENCY=4
CELERY_QUEUES=default,manuscript,analysis,publishing,training

# Encryption Key for sensitive data (generate using: python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
ENCRYPTION_KEY=generate-this-using-fernet-generate-key

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production