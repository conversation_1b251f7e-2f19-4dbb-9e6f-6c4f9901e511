{"tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIManager::test_get_agent_status": true, "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIManager::test_execute_agent_manuscript_generator": true, "tests/test_agents/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_generate_manuscript_success": true, "tests/test_agents/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_generate_manuscript_with_custom_params": true, "tests/test_agents/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_manuscript_generation_error_handling": true, "tests/test_agents/test_pydantic_ai_agents.py::TestTrendAnalyzer::test_trend_analysis_with_depth": true, "tests/test_agents/test_pydantic_ai_agents.py::TestTrendAnalyzer::test_trend_analysis_error_handling": true, "tests/test_agents/test_pydantic_ai_agents.py::TestCoverDesigner::test_design_book_cover_success": true, "tests/test_agents/test_pydantic_ai_agents.py::TestKDPUploader::test_upload_to_kdp_success": true, "tests/test_agents/test_pydantic_ai_agents.py::TestAdditionalAgents::test_research_assistant_success": true, "tests/test_agents/test_pydantic_ai_agents.py::TestAdditionalAgents::test_personalization_engine_success": true, "tests/test_agents/test_pydantic_ai_agents.py::TestErrorHandling::test_agent_execution_timeout": true, "tests/test_agents/test_pydantic_ai_agents.py::TestErrorHandling::test_invalid_agent_name": true, "tests/test_agents/test_pydantic_ai_agents.py::TestDataValidation::test_input_sanitization": true, "tests/test_agents/test_pydantic_ai_agents.py::TestConfigurationAndSettings::test_agent_configuration": true, "tests/test_agents/test_pydantic_ai_agents.py::TestConfigurationAndSettings::test_dependency_injection": true, "tests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_success": true, "tests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_empty_result": true, "tests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_database_error": true, "tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_analyze_competitor_books_success": true, "tests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_analyze_content_quality_with_criteria": true, "tests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_research_market_trends_success": true, "tests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_validate_book_concept_success": true, "tests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_network_timeout_handling": true, "tests/test_agents/test_performance_load.py::TestScalabilityLimits::test_large_data_processing": true}