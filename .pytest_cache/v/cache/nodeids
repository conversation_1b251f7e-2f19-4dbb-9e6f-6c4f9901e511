["tests/test_agents/test_performance_load.py::TestMemoryUsage::test_memory_leak_detection", "tests/test_agents/test_performance_load.py::TestMemoryUsage::test_memory_usage_multiple_executions", "tests/test_agents/test_performance_load.py::TestMemoryUsage::test_memory_usage_single_execution", "tests/test_agents/test_performance_load.py::TestPerformanceMetrics::test_concurrent_agent_performance", "tests/test_agents/test_performance_load.py::TestPerformanceMetrics::test_single_agent_execution_time", "tests/test_agents/test_performance_load.py::TestPerformanceMetrics::test_workflow_execution_performance", "tests/test_agents/test_performance_load.py::TestResourceUtilization::test_cpu_utilization", "tests/test_agents/test_performance_load.py::TestResourceUtilization::test_thread_pool_efficiency", "tests/test_agents/test_performance_load.py::TestScalabilityLimits::test_high_concurrency_limits", "tests/test_agents/test_performance_load.py::TestScalabilityLimits::test_large_data_processing", "tests/test_agents/test_performance_load.py::TestScalabilityLimits::test_rapid_sequential_requests", "tests/test_agents/test_performance_load.py::TestStressTests::test_error_recovery_under_load", "tests/test_agents/test_performance_load.py::TestStressTests::test_sustained_load", "tests/test_agents/test_pydantic_ai_agents.py::TestAdditionalAgents::test_multimodal_generator_success", "tests/test_agents/test_pydantic_ai_agents.py::TestAdditionalAgents::test_personalization_engine_success", "tests/test_agents/test_pydantic_ai_agents.py::TestAdditionalAgents::test_research_assistant_success", "tests/test_agents/test_pydantic_ai_agents.py::TestConfigurationAndSettings::test_agent_configuration", "tests/test_agents/test_pydantic_ai_agents.py::TestConfigurationAndSettings::test_dependency_injection", "tests/test_agents/test_pydantic_ai_agents.py::TestCoverDesigner::test_design_book_cover_success", "tests/test_agents/test_pydantic_ai_agents.py::TestDataValidation::test_data_type_validation", "tests/test_agents/test_pydantic_ai_agents.py::TestDataValidation::test_empty_input_handling", "tests/test_agents/test_pydantic_ai_agents.py::TestDataValidation::test_input_sanitization", "tests/test_agents/test_pydantic_ai_agents.py::TestErrorHandling::test_agent_execution_timeout", "tests/test_agents/test_pydantic_ai_agents.py::TestErrorHandling::test_invalid_agent_name", "tests/test_agents/test_pydantic_ai_agents.py::TestErrorHandling::test_malformed_workflow", "tests/test_agents/test_pydantic_ai_agents.py::TestIntegration::test_agent_registry_consistency", "tests/test_agents/test_pydantic_ai_agents.py::TestIntegration::test_full_book_creation_workflow", "tests/test_agents/test_pydantic_ai_agents.py::TestKDPUploader::test_upload_to_kdp_success", "tests/test_agents/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_generate_manuscript_success", "tests/test_agents/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_generate_manuscript_with_custom_params", "tests/test_agents/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_manuscript_generation_error_handling", "tests/test_agents/test_pydantic_ai_agents.py::TestMonitoringAndLogging::test_error_logging", "tests/test_agents/test_pydantic_ai_agents.py::TestMonitoringAndLogging::test_execution_tracking", "tests/test_agents/test_pydantic_ai_agents.py::TestPerformanceAndScaling::test_memory_usage_optimization", "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIBase::test_agent_registry_initialization", "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIBase::test_all_agents_registered", "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIBase::test_dependency_classes", "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIManager::test_execute_agent_manuscript_generator", "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIManager::test_execute_workflow", "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIManager::test_get_agent_status", "tests/test_agents/test_pydantic_ai_agents.py::TestPydanticAIManager::test_manager_initialization", "tests/test_agents/test_pydantic_ai_agents.py::TestSalesMonitor::test_monitor_sales_performance_success", "tests/test_agents/test_pydantic_ai_agents.py::TestSalesMonitor::test_sales_monitoring_custom_params", "tests/test_agents/test_pydantic_ai_agents.py::TestTrendAnalyzer::test_analyze_market_trends_success", "tests/test_agents/test_pydantic_ai_agents.py::TestTrendAnalyzer::test_trend_analysis_error_handling", "tests/test_agents/test_pydantic_ai_agents.py::TestTrendAnalyzer::test_trend_analysis_with_depth", "tests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_analyze_content_quality_empty_content", "tests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_analyze_content_quality_success", "tests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_analyze_content_quality_with_criteria", "tests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_extract_keywords_custom_limit", "tests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_extract_keywords_short_content", "tests/test_agents/test_pydantic_ai_tools.py::TestContentAnalysisTools::test_extract_keywords_success", "tests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_database_error", "tests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_empty_result", "tests/test_agents/test_pydantic_ai_tools.py::TestDatabaseTools::test_get_user_books_success", "tests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_concurrent_tool_execution", "tests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_invalid_input_handling", "tests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_large_data_processing", "tests/test_agents/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases::test_network_timeout_handling", "tests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_research_market_trends_different_timeframes", "tests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_research_market_trends_success", "tests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_validate_book_concept_edge_cases", "tests/test_agents/test_pydantic_ai_tools.py::TestMarketResearchTools::test_validate_book_concept_success", "tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_analyze_competitor_books_error_handling", "tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_analyze_competitor_books_success", "tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_amazon_bestsellers_large_limit", "tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_amazon_bestsellers_success", "tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_reddit_trends_empty_subreddits", "tests/test_agents/test_pydantic_ai_tools.py::TestScrapingTools::test_scrape_reddit_trends_success", "tests/test_pydantic_ai_agents.py::TestPydanticAIBase::test_agent_registry_initialization"]