# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Backend Development
```bash
# Install dependencies
poetry install

# Run development server
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run with Docker Compose (full stack)
docker-compose up --build

# Code quality checks
poetry run black app/ tests/
poetry run isort app/ tests/
poetry run flake8 app/ tests/
poetry run mypy app/

# Run tests
poetry run pytest
poetry run pytest tests/test_agents/  # Run specific test directory
```

### Frontend Development
```bash
# Navigate to frontend directory
cd frontend/

# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Run linting
npm run lint
```

### Environment Setup
```bash
# Copy environment template
cp .env.example .env
# Then edit .env with your API keys and configuration
```

## Architecture Overview

This is an **AI-powered e-book generation and publishing system** with a sophisticated agent-based architecture that leverages reinforcement learning for continuous improvement.

### Core Components

**Agent-Based Architecture**: The system uses specialized AI agents that inherit from `BaseAgent` (app/agents/base_agent.py):
- `TrendAnalyzer`: Identifies trending topics and market opportunities
- `ManuscriptGenerator`: Creates book content using multi-model AI (OpenAI/Anthropic)
- `ResearchAssistant`: Gathers supporting research and references
- `CoverDesigner`: Generates book covers and visual assets
- `KDPUploader`: Handles automated publishing to Amazon KDP
- `SalesMonitor`: Tracks performance and sales data

**Data Flow**: Agents are chained together where each agent's output becomes the next agent's input:
```
User Request → TrendAnalyzer → ManuscriptGenerator → CoverDesigner → KDPUploader
```

**Multi-Model AI Integration**: 
- Supports both OpenAI and Anthropic APIs with automatic fallback
- Parallel chapter generation with rate limiting via semaphores
- Quality assurance through multi-step content generation (outline → chapters → compilation)

**VERL (Reinforcement Learning) Integration**:
- Collects real-time user feedback and sales performance data
- Trains custom reward models to improve generation quality
- Uses PPO (Proximal Policy Optimization) for continuous model improvement
- Falls back to traditional generation when insufficient training data

### Database Schema

**Core Models** (app/models/):
- `User`: User authentication and preferences
- `Book`: Generated manuscripts and metadata  
- `Publication`: Published book tracking and performance
- `TrendAnalysis`: Captured trend data and market insights
- `FeedbackMetrics`: User feedback and quality scores for VERL training

### API Structure

**RESTful Endpoints** (app/api/):
- `/api/auth`: User authentication and authorization
- `/api/books`: CRUD operations for manuscripts
- `/api/trends`: Trend analysis and market research
- `/api/publications`: Publishing workflow and status
- `/api/monitoring`: VERL training and system monitoring
- `/api/feedback`: User feedback collection for model improvement

### Configuration

**Environment Variables**: Copy `.env.example` to `.env` and configure:
- Database URL (SQLite default, PostgreSQL for production)
- Redis URL for background task queuing
- AI API keys (OpenAI, Anthropic)
- External API keys (Google Trends, Amazon)
- KDP credentials for automated publishing
- VERL training parameters and thresholds

**Key Settings** (app/config.py):
- `enable_verl`: Toggle reinforcement learning features
- `min_training_examples`: Minimum feedback needed before VERL training
- `verl_training_interval_hours`: How often to retrain models
- `books_per_trend`: Number of books to generate per identified trend

### Background Tasks

**Asynchronous Processing**: Long-running operations use background tasks:
- Manuscript generation (can take 10-30 minutes)
- Trend analysis and market research
- VERL model training and optimization
- Automated publishing workflows

**Task Files** (app/tasks/): Contains Celery task definitions for background processing.

### Development Patterns

**Agent Development**: When creating new agents:
1. Inherit from `BaseAgent` in app/agents/base_agent.py
2. Implement the `execute()` method returning `AgentResult`
3. Handle both success and error cases gracefully
4. Add appropriate logging and progress tracking

**API Development**: New endpoints should:
1. Use Pydantic schemas for request/response validation
2. Include proper user authentication and authorization
3. Return task IDs for long-running operations
4. Follow RESTful conventions and HTTP status codes

**Service Layer**: Business logic should be implemented in app/services/ (currently empty - needs implementation).

**Testing**: Use pytest for all new functionality. Test files should be created in tests/ directory following the pattern test_[module_name].py.

### Storage Structure

```
storage/
├── covers/          # Generated book covers
├── manuscripts/     # Generated book content
└── published/       # Final published versions
```

### Technology Stack

- **Backend**: FastAPI, SQLAlchemy, Celery, Redis
- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **AI**: OpenAI GPT models, Anthropic Claude, VERL framework
- **Database**: SQLite (dev), PostgreSQL (production)
- **Infrastructure**: Docker, Docker Compose

### Key Files

- `app/main.py`: FastAPI application entry point
- `app/config.py`: Centralized configuration management
- `app/database.py`: Database connection and session management
- `docker-compose.yml`: Full stack development environment
- `pyproject.toml`: Python dependencies and project metadata

⚠️ Critical Reminders:

Always use draft mode for KDP uploads initially
Manually review all content before publishing
Focus on quality over quantity - valuable content wins
Comply with Amazon KDP terms of service
Secure your API keys and credentials properly

🎯 Revenue Potential:

Passive income through automated book generation
Scalable business model with multiple niches
Data-driven decisions from trend analysis
Professional quality with AI assistance

✨ What You Now Have:
🎨 5 Professional Themes

Clean Professional - Modern business/self-help books
Modern Minimalist - Contemporary, clean design
Classic Literary - Traditional with ornate elements
Tech Business - Corporate-focused layouts
Romantic Elegant - Perfect for romance novels

📱 Multi-Format Excellence

DOCX - Professional manuscript format
EPUB - Industry-standard e-book format
PDF - Print-ready with proper pagination
HTML - Web-optimized with responsive design

📚 Professional Typography

Smart Font Pairing - Genre-appropriate combinations
Proper Spacing - Optimized line height and margins
Chapter Formatting - Professional breaks and headers
Decorative Elements - Tasteful ornaments and drop caps

🚀 Key Improvements:
Automatic Theme Selection
python# The system intelligently picks themes based on:
theme = layout_designer.select_optimal_theme(manuscript)
# - Book category (business → tech_business theme)
# - Content tone (formal vs casual)
# - Target audience analysis

🎯 Usage in Your System:
python# In your manuscript generator
formatted_files = layout_designer.create_professional_layout(
    manuscript=manuscript,
    theme_name="auto",  # Or specify: "clean_professional"
    output_formats=["docx", "epub", "pdf"]
)

# Results in professional files ready for:
# - KDP upload (EPUB/PDF)
# - Print publishing (PDF)
# - Review/editing (DOCX)
# - Web preview (HTML)

Content Quality Improvements:

Writing Style → Learns which tone/style gets approved most
Chapter Structure → Optimizes chapter length and organization
Topic Selection → Identifies trending vs declining niches
Prompt Engineering → Refines prompts for better output

Business Performance Optimization:

Sales Prediction → Learns to predict which books will sell
Price Optimization → Finds optimal pricing strategies
Category Targeting → Focuses on profitable niches
Layout Preferences → Learns which themes perform best

User Experience Enhancement:

Generation Speed → Optimizes for faster book creation
Approval Rates → Reduces books that get rejected
Quality Consistency → Maintains high standards automatically
Error Reduction → Learns from and prevents failures

🔄 The Learning Cycle:
mermaidgraph TD
    A[Generate Book] --> B[User Reviews]
    B --> C[Collect Feedback]
    C --> D[Sales Performance]
    D --> E[Calculate Rewards]
    E --> F[Update Model]
    F --> G[Optimize Parameters]
    G --> A
💡 Intelligent Adaptations:
Automatic A/B Testing:
python# System automatically tests variations:
experiment_results = {
    'prompt_variant_A': {'approval_rate': 87%, 'sales': $245},
    'prompt_variant_B': {'approval_rate': 91%, 'sales': $312},  # Winner!
    'theme_test_1': {'user_preference': 78%},
    'theme_test_2': {'user_preference': 85%}  # Auto-deploy this theme
}
Predictive Quality Scoring:
python# Predicts book success before user sees it:
predicted_success = reward_model.predict(
    prompt=book_prompt,
    config=generation_config,
    category=book_category
)
# If predicted_success < 0.7, regenerate with different parameters
Dynamic Trend Following:
python# Learns which trends actually convert to sales:
trend_accuracy = {
    'productivity_books': {'predicted_success': 78%, 'actual_sales': 82%},  # Good prediction
    'fantasy_romance': {'predicted_success': 65%, 'actual_sales': 23%}     # Poor - adjust model
}
📊 Analytics Dashboard Example:
typescript// Real-time performance metrics
const PerformanceMetrics = () => {
  return (
    <div className="analytics-dashboard">
      <MetricCard 
        title="Approval Rate Trend" 
        value="89.3%" 
        change="+5.2% this month"
        trend="improving" 
      />
      <MetricCard 
        title="Average Quality Score" 
        value="78.5/100" 
        change="+12.1 points"
        trend="improving" 
      />
      <MetricCard 
        title="Revenue per Book" 
        value="$247" 
        change="+$83 vs last month"
        trend="improving" 
      />
    </div>
  )
}
🎯 Integration with Your System:
Step 1: Add Feedback Tracking
python# Update your approval endpoint:
@router.post("/books/{book_id}/approve")
async def approve_book(book_id: int):
    # Existing approval logic...
    
    # NEW: Track this as positive feedback
    await feedback_collector.collect_user_approval_feedback(
        book_id=book_id,
        approved=True,
        approval_time=time_taken
    )
Step 2: Enable Auto-Learning
python# Add to your system startup:
@celery_app.task
def daily_system_improvement():
    trainer = ReinforcementTrainer()
    await trainer.auto_tune_system()  # Runs automatically every day
Step 3: Use Optimized Parameters
python# Your manuscript generator automatically uses learned parameters:
optimal_config = await get_optimized_config(category=book_category)
manuscript = await generate_with_config(optimal_config)
🚀 Expected Improvements:
Month 1:

✅ Basic feedback collection
✅ Simple parameter optimization
✅ 10-15% improvement in approval rates

Month 3:

✅ Advanced reward modeling
✅ Category-specific optimization
✅ 25-35% improvement in overall performance

Month 6:

✅ Predictive quality scoring
✅ Automatic trend adaptation
✅ 50%+ improvement in success metrics

Year 1:

✅ Fully autonomous optimization
✅ Industry-leading content quality
✅ Competitive advantage through AI learning

🎉 Bottom Line:
Your e-book generator will:

🧠 Learn from every user interaction
📈 Improve automatically over time
🎯 Optimize for maximum success
💰 Increase profitability through better performance
🚀 Stay ahead of competitors with continuous learning

🔄 Complete Data Flow:
User Action → Database → Collection → Processing → VERL Training
    ↓            ↓           ↓            ↓            ↓
[Approve] → [feedback_table] → [gather] → [tokenize] → [learn]
[Reject]  → [performance]    → [filter] → [format]   → [improve]
[Sales]   → [sales_data]     → [reward] → [batch]    → [update model]
📊 Data Sources & Access Points:
1. User Feedback (Primary Signal)
python# When user approves/rejects a book:
await LiveFeedbackCollector.capture_approval_feedback(
    book_id=123,
    approved=True,           # ✅ User approved this content
    approval_time=45.2,      # ⏱️ How long they took to decide
    rejection_reason=None    # 📝 Why they rejected (if applicable)
)
# → Stored in: feedback_metrics & model_performance tables
2. Sales Performance (Business Signal)
python# When sales data comes in:
await LiveFeedbackCollector.capture_sales_feedback(
    book_id=123,
    sales_data={
        'sales_units': 47,      # 📈 How many copies sold
        'revenue': 127.53,      # 💰 Total revenue
        'average_rating': 4.2,  # ⭐ Reader ratings
        'reviews_count': 12     # 💬 Number of reviews
    }
)
# → Stored in: sales_data table
3. Quality Metrics (Content Signal)
python# Automatically calculated quality scores:
quality_metrics = {
    'readability_score': 78,     # 📖 How readable the content is
    'engagement_factor': 85,     # 🎯 How engaging it is
    'actionability': 72,         # ⚡ How practical/useful
    'word_count_quality': 90     # 📏 Appropriate length
}
# → Stored in: book.quality_score
🧠 How Training Accesses This Data:
Step 1: Data Collection Query
python# VERL trainer queries database:
training_data = await data_accessor.get_training_data(days_back=60)

# This query gets:
books_with_feedback = db.query(Book).join(ModelPerformance).filter(
    Book.created_at >= cutoff_date,
    Book.status.in_(['approved', 'rejected', 'published']),
    ModelPerformance.reward_signal.isnot(None)  # Has feedback
).all()
Step 2: Reconstruct Training Examples
python# For each book, the system reconstructs:
training_example = {
    'prompt': "Write a chapter about productivity...",     # Original prompt
    'generated_content': "Chapter 1: Getting Started...", # AI output
    'reward': 0.75,                                       # Calculated reward
    'metadata': {
        'user_approved': True,      # ✅ User feedback
        'quality_score': 82,        # 📊 Quality metrics  
        'sales_units': 47,          # 📈 Sales performance
        'category': 'productivity'   # 🏷️ Book category
    }
}
Step 3: Reward Calculation
python# Multi-factor reward calculation:
def calculate_reward(performance, sales_data, quality):
    reward = 0.0
    
    # User approval (40% weight)
    reward += 1.0 if performance.user_approval else -0.5
    
    # Quality score (30% weight) 
    reward += (quality_score - 50) / 50 * 0.6
    
    # Sales performance (30% weight)
    if sales_data:
        sales_norm = min(sales_data.sales_units / 50, 1.0)
        reward += sales_norm * 0.6
    
    return math.tanh(reward)  # Normalize to [-1, 1]
⚡ Real-Time Data Capture:
Every User Interaction Becomes Training Data:
<EMAIL>("/books/{book_id}/approve")
async def approve_book(book_id: int):
    # 1. Update book status
    book.status = "approved"
    
    # 2. 🔥 IMMEDIATELY capture for training
    await capture_approval_feedback(book_id, approved=True)
    
    # 3. 🚀 Check if we should trigger training
    await check_training_trigger()  # Auto-trains if enough new data
Automatic Training Triggers:
python# System automatically decides when to train:
if new_feedback_count >= 10:          # Enough new data
    if approval_rate < 0.7:           # Performance declining  
        trigger_training()            # 🚀 Start learning!
    elif days_since_training > 7:     # Weekly updates
        trigger_training()            # 🔄 Keep improving!