"""
PydanticAI Base Infrastructure
Provides common dependency types, utilities, and base classes for PydanticAI agents
"""

from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel, Field
from dataclasses import dataclass
from datetime import datetime
import logging

from app.config import settings
from app.database import get_db_context
from app.models.user import User
from app.models.book import Book
from app.utils.scrapers import AmazonScraper, RedditScraper

logger = logging.getLogger(__name__)

# ============================================================================
# Common Dependency Types for Dependency Injection
# ============================================================================

@dataclass
class DatabaseDependencies:
    """Database connection dependencies"""
    user_id: Optional[int] = None
    
    def get_db(self):
        """Get database session"""
        return get_db_context()
    
    async def get_user(self) -> Optional[User]:
        """Get current user"""
        if not self.user_id:
            return None
        with self.get_db() as db:
            return db.query(User).filter(User.id == self.user_id).first()

@dataclass
class AIModelDependencies:
    """AI model configuration dependencies"""
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    preferred_model: str = "openai:gpt-4"
    temperature: float = 0.7
    max_tokens: int = 4000
    
    def __post_init__(self):
        # Use settings if not provided
        if not self.openai_api_key:
            self.openai_api_key = settings.openai_api_key
        if not self.anthropic_api_key:
            self.anthropic_api_key = settings.anthropic_api_key

@dataclass
class ScrapingDependencies:
    """Web scraping dependencies"""
    amazon_scraper: Optional[AmazonScraper] = None
    reddit_scraper: Optional[RedditScraper] = None
    headless: bool = True
    
    def __post_init__(self):
        if not self.amazon_scraper:
            self.amazon_scraper = AmazonScraper()
        if not self.reddit_scraper:
            self.reddit_scraper = RedditScraper()

@dataclass
class ManuscriptDependencies:
    """Dependencies for manuscript generation"""
    db_deps: DatabaseDependencies
    ai_deps: AIModelDependencies
    target_length: int = 8000
    style: str = "professional"
    target_audience: str = "general adults"
    output_formats: List[str] = None
    
    def __post_init__(self):
        if self.output_formats is None:
            self.output_formats = ['docx', 'epub', 'pdf']

@dataclass
class TrendAnalysisDependencies:
    """Dependencies for trend analysis"""
    db_deps: DatabaseDependencies
    scraping_deps: ScrapingDependencies
    categories: List[str] = None
    max_results: int = 50
    min_search_volume: int = 1000
    
    def __post_init__(self):
        if self.categories is None:
            self.categories = [
                'self-help', 'romance', 'mystery', 'fantasy', 'business',
                'health', 'cooking', 'parenting', 'finance', 'productivity'
            ]

@dataclass
class SalesMonitorDependencies:
    """Dependencies for sales monitoring"""
    db_deps: DatabaseDependencies
    scraping_deps: ScrapingDependencies
    kdp_email: Optional[str] = None
    kdp_password: Optional[str] = None
    
    def __post_init__(self):
        if not self.kdp_email:
            self.kdp_email = getattr(settings, 'kdp_email', None)
        if not self.kdp_password:
            self.kdp_password = getattr(settings, 'kdp_password', None)

@dataclass
class CoverDesignDependencies:
    """Dependencies for cover design"""
    db_deps: DatabaseDependencies
    ai_deps: AIModelDependencies
    style: str = "modern"
    dimensions: tuple = (1600, 2560)  # Standard e-book cover size
    
@dataclass
class KDPUploadDependencies:
    """Dependencies for KDP upload"""
    db_deps: DatabaseDependencies
    scraping_deps: ScrapingDependencies
    kdp_email: Optional[str] = None
    kdp_password: Optional[str] = None
    
    def __post_init__(self):
        if not self.kdp_email:
            self.kdp_email = getattr(settings, 'kdp_email', None)
        if not self.kdp_password:
            self.kdp_password = getattr(settings, 'kdp_password', None)

# ============================================================================
# Common Output Models for Structured Responses
# ============================================================================

class AgentExecutionResult(BaseModel):
    """Standard result format for all agents"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    agent_name: str
    execution_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class BookOutlineResult(BaseModel):
    """Structured output for book outline generation"""
    title: str = Field(description="The book title")
    description: str = Field(description="Book description/summary")
    category: str = Field(description="Book category")
    target_word_count: int = Field(description="Target word count for the book")
    chapter_outlines: List[Dict[str, Any]] = Field(description="List of chapter outlines")
    keywords: List[str] = Field(description="Relevant keywords for the book")
    estimated_completion_time: str = Field(description="Estimated time to complete")

class ChapterResult(BaseModel):
    """Structured output for chapter generation"""
    title: str = Field(description="Chapter title")
    content: str = Field(description="Chapter content")
    word_count: int = Field(description="Actual word count")
    quality_score: float = Field(description="Quality assessment score (0-100)", ge=0, le=100)
    key_points: List[str] = Field(description="Key points covered in the chapter")

class TrendAnalysisResult(BaseModel):
    """Structured output for trend analysis"""
    opportunities: List[Dict[str, Any]] = Field(description="List of identified opportunities")
    top_keywords: List[str] = Field(description="Top trending keywords")
    market_analysis: Dict[str, Any] = Field(description="Market analysis summary")
    confidence_score: float = Field(description="Confidence in analysis (0-100)", ge=0, le=100)
    recommendations: List[str] = Field(description="Actionable recommendations")

class SalesAnalysisResult(BaseModel):
    """Structured output for sales analysis"""
    total_sales: int = Field(description="Total units sold")
    total_revenue: float = Field(description="Total revenue generated")
    best_performers: List[Dict[str, Any]] = Field(description="Best performing books")
    growth_metrics: Dict[str, float] = Field(description="Growth metrics")
    insights: List[str] = Field(description="Key insights from sales data")
    recommendations: List[str] = Field(description="Recommendations for improvement")

class CoverDesignResult(BaseModel):
    """Structured output for cover design"""
    design_concept: str = Field(description="Description of the design concept")
    color_scheme: List[str] = Field(description="Primary colors used")
    typography: str = Field(description="Typography choices")
    image_elements: List[str] = Field(description="Key visual elements")
    file_paths: List[str] = Field(description="Paths to generated cover files")
    design_rationale: str = Field(description="Explanation of design choices")

class UploadResult(BaseModel):
    """Structured output for KDP upload"""
    upload_status: str = Field(description="Status of the upload (success/failed/pending)")
    book_id: Optional[str] = Field(description="KDP book ID if successful")
    publication_url: Optional[str] = Field(description="URL to the published book")
    errors: List[str] = Field(description="Any errors encountered during upload")
    warnings: List[str] = Field(description="Any warnings during upload")
    next_steps: List[str] = Field(description="Recommended next steps")

# ============================================================================
# Utility Functions
# ============================================================================

def create_standard_dependencies(
    user_id: Optional[int] = None,
    preferred_model: str = "openai:gpt-4",
    headless: bool = True
) -> Dict[str, Any]:
    """Create standard dependency set for most agents"""
    return {
        'db_deps': DatabaseDependencies(user_id=user_id),
        'ai_deps': AIModelDependencies(preferred_model=preferred_model),
        'scraping_deps': ScrapingDependencies(headless=headless)
    }

def validate_agent_config(config: Dict[str, Any]) -> bool:
    """Validate agent configuration"""
    required_keys = ['model', 'temperature', 'max_tokens']
    return all(key in config for key in required_keys)

def format_execution_time(start_time: datetime, end_time: datetime) -> float:
    """Calculate and format execution time"""
    return (end_time - start_time).total_seconds()

def create_error_result(
    agent_name: str,
    error_message: str,
    execution_time: Optional[float] = None
) -> AgentExecutionResult:
    """Create standardized error result"""
    return AgentExecutionResult(
        success=False,
        error=error_message,
        agent_name=agent_name,
        execution_time=execution_time
    )

def create_success_result(
    agent_name: str,
    data: Dict[str, Any],
    execution_time: Optional[float] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> AgentExecutionResult:
    """Create standardized success result"""
    return AgentExecutionResult(
        success=True,
        data=data,
        agent_name=agent_name,
        execution_time=execution_time,
        metadata=metadata
    )

# ============================================================================
# Agent Registry for Managing PydanticAI Agents
# ============================================================================

class AgentRegistry:
    """Registry for managing PydanticAI agents"""
    
    def __init__(self):
        self._agents = {}
        self.logger = logging.getLogger(__name__)
    
    def register_agent(self, name: str, agent):
        """Register a PydanticAI agent"""
        self._agents[name] = agent
        self.logger.info(f"Registered agent: {name}")
    
    def get_agent(self, name: str):
        """Get a registered agent"""
        return self._agents.get(name)
    
    def list_agents(self) -> List[str]:
        """List all registered agent names"""
        return list(self._agents.keys())
    
    def remove_agent(self, name: str):
        """Remove an agent from registry"""
        if name in self._agents:
            del self._agents[name]
            self.logger.info(f"Removed agent: {name}")

# Global agent registry instance
agent_registry = AgentRegistry()
