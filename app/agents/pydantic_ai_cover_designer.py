"""
PydanticAI Cover Designer Agent
Advanced cover design using PydanticAI with AI image generation and design tools
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from datetime import datetime
import logging

from .pydantic_ai_base import (
    DatabaseDependencies,
    AIModelDependencies,
    CoverDesignDependencies,
    AgentExecutionResult,
    create_success_result,
    create_error_result,
    agent_registry
)

logger = logging.getLogger(__name__)

# ============================================================================
# Output Models
# ============================================================================

class DesignConcept(BaseModel):
    """Design concept and rationale"""
    concept_name: str = Field(description="Name of the design concept")
    description: str = Field(description="Detailed description of the design")
    target_emotion: str = Field(description="Emotion the design should evoke")
    genre_alignment: str = Field(description="How well it aligns with genre expectations")
    market_appeal: float = Field(description="Estimated market appeal (0-100)", ge=0, le=100)
    uniqueness_score: float = Field(description="Design uniqueness score (0-100)", ge=0, le=100)

class ColorPalette(BaseModel):
    """Color palette specification"""
    primary_color: str = Field(description="Primary color (hex code)")
    secondary_color: str = Field(description="Secondary color (hex code)")
    accent_color: str = Field(description="Accent color (hex code)")
    background_color: str = Field(description="Background color (hex code)")
    text_color: str = Field(description="Text color (hex code)")
    palette_name: str = Field(description="Name of the color palette")
    mood: str = Field(description="Mood conveyed by the palette")

class TypographySpec(BaseModel):
    """Typography specifications"""
    title_font: str = Field(description="Font for the title")
    subtitle_font: str = Field(description="Font for subtitle/author name")
    title_size: int = Field(description="Title font size")
    subtitle_size: int = Field(description="Subtitle font size")
    font_weight: str = Field(description="Font weight (bold, normal, light)")
    text_effects: List[str] = Field(description="Text effects (shadow, outline, etc.)")

class LayoutComposition(BaseModel):
    """Layout and composition details"""
    layout_type: str = Field(description="Type of layout (centered, asymmetric, etc.)")
    title_position: str = Field(description="Position of title on cover")
    image_placement: str = Field(description="Placement of main image/graphics")
    visual_hierarchy: List[str] = Field(description="Visual hierarchy of elements")
    balance_score: float = Field(description="Visual balance score (0-100)", ge=0, le=100)

class ComprehensiveCoverDesign(BaseModel):
    """Complete cover design specification"""
    design_concept: DesignConcept = Field(description="Overall design concept")
    color_palette: ColorPalette = Field(description="Color scheme")
    typography: TypographySpec = Field(description="Typography specifications")
    layout: LayoutComposition = Field(description="Layout and composition")
    image_prompts: List[str] = Field(description="AI image generation prompts")
    design_variations: List[Dict[str, Any]] = Field(description="Alternative design variations")
    market_analysis: Dict[str, Any] = Field(description="Market positioning analysis")
    production_notes: List[str] = Field(description="Notes for production/implementation")

# ============================================================================
# PydanticAI Cover Designer Agent
# ============================================================================

# Main cover design agent
cover_designer = Agent(
    'openai:gpt-4',
    deps_type=CoverDesignDependencies,
    output_type=ComprehensiveCoverDesign,
    system_prompt="""You are an expert book cover designer with deep knowledge of visual design, 
    marketing psychology, and genre conventions. Your role is to create compelling cover designs 
    that attract readers and drive sales.
    
    Key expertise:
    - Visual design principles and composition
    - Color psychology and market appeal
    - Typography and readability
    - Genre-specific design conventions
    - Market trends and competitive analysis
    - Print and digital format optimization
    - A/B testing and conversion optimization
    
    Always consider both artistic merit and commercial viability in your designs."""
)

# Specialized market analysis agent
market_analyzer = Agent(
    'anthropic:claude-3-sonnet',
    deps_type=CoverDesignDependencies,
    output_type=Dict[str, Any],
    system_prompt="""You are a book cover market analyst specializing in design trends, 
    competitive analysis, and consumer psychology in book cover design."""
)

# ============================================================================
# Tools for Cover Design
# ============================================================================

@cover_designer.tool
async def analyze_genre_conventions(
    _ctx: RunContext[CoverDesignDependencies],
    genre: str,
    subgenre: Optional[str] = None
) -> Dict[str, Any]:
    """Analyze design conventions for a specific genre"""
    try:
        # Genre-specific design conventions database
        genre_conventions = {
            'romance': {
                'color_schemes': ['warm pastels', 'deep reds', 'gold accents'],
                'typography': ['script fonts', 'elegant serif', 'flowing text'],
                'imagery': ['couples', 'flowers', 'romantic settings'],
                'layout': ['centered', 'romantic imagery dominant'],
                'market_expectations': 'emotional appeal, relationship focus'
            },
            'mystery': {
                'color_schemes': ['dark blues', 'blacks', 'muted colors'],
                'typography': ['bold sans-serif', 'dramatic fonts'],
                'imagery': ['shadows', 'urban settings', 'mysterious objects'],
                'layout': ['asymmetric', 'dramatic angles'],
                'market_expectations': 'intrigue, suspense, professional look'
            },
            'fantasy': {
                'color_schemes': ['rich purples', 'mystical blues', 'gold'],
                'typography': ['ornate fonts', 'medieval style', 'decorative'],
                'imagery': ['magical elements', 'landscapes', 'creatures'],
                'layout': ['epic compositions', 'detailed artwork'],
                'market_expectations': 'wonder, adventure, otherworldly'
            },
            'self-help': {
                'color_schemes': ['bright blues', 'energetic oranges', 'clean whites'],
                'typography': ['clean sans-serif', 'modern fonts', 'readable'],
                'imagery': ['upward arrows', 'light imagery', 'success symbols'],
                'layout': ['clean', 'professional', 'clear hierarchy'],
                'market_expectations': 'credibility, positivity, results-focused'
            },
            'business': {
                'color_schemes': ['professional blues', 'corporate grays', 'success gold'],
                'typography': ['strong sans-serif', 'authoritative fonts'],
                'imagery': ['charts', 'cityscapes', 'success imagery'],
                'layout': ['structured', 'professional', 'clear'],
                'market_expectations': 'authority, expertise, results'
            }
        }
        
        conventions = genre_conventions.get(genre.lower(), {
            'color_schemes': ['neutral colors'],
            'typography': ['readable fonts'],
            'imagery': ['genre-appropriate'],
            'layout': ['balanced'],
            'market_expectations': 'genre-appropriate design'
        })
        
        return {
            'genre': genre,
            'subgenre': subgenre,
            'conventions': conventions,
            'design_guidelines': [
                f"Use {', '.join(conventions['color_schemes'])} for color schemes",
                f"Typography should be {', '.join(conventions['typography'])}",
                f"Include imagery like {', '.join(conventions['imagery'])}",
                f"Layout should be {', '.join(conventions['layout'])}",
                f"Market expects: {conventions['market_expectations']}"
            ]
        }
        
    except Exception as e:
        logger.error(f"Error analyzing genre conventions: {str(e)}")
        return {"error": str(e)}

@cover_designer.tool
async def generate_color_palettes(
    _ctx: RunContext[CoverDesignDependencies],
    _genre: str,
    mood: str,
    _target_audience: str
) -> List[ColorPalette]:
    """Generate appropriate color palettes for the cover"""
    try:
        # Color psychology and genre mapping
        mood_colors = {
            'energetic': ['#FF6B35', '#F7931E', '#FFD23F'],
            'calm': ['#4A90A4', '#7FB069', '#B8D4E3'],
            'mysterious': ['#2C3E50', '#8E44AD', '#34495E'],
            'romantic': ['#E74C3C', '#F39C12', '#F8C471'],
            'professional': ['#2980B9', '#34495E', '#95A5A6'],
            'luxurious': ['#8E44AD', '#F39C12', '#2C3E50']
        }
        
        base_colors = mood_colors.get(mood.lower(), ['#3498DB', '#E74C3C', '#F39C12'])
        
        palettes = []
        
        # Generate 3 different palette variations
        for i, base_color in enumerate(base_colors[:3]):
            palette = ColorPalette(
                primary_color=base_color,
                secondary_color=_generate_complementary_color(base_color),
                accent_color=_generate_accent_color(base_color),
                background_color='#FFFFFF' if i % 2 == 0 else '#F8F9FA',
                text_color='#2C3E50' if i % 2 == 0 else '#FFFFFF',
                palette_name=f"{mood.title()} Palette {i+1}",
                mood=mood
            )
            palettes.append(palette)
        
        return palettes
        
    except Exception as e:
        logger.error(f"Error generating color palettes: {str(e)}")
        return []

@cover_designer.tool
async def suggest_typography(
    _ctx: RunContext[CoverDesignDependencies],
    genre: str,
    title: str,
    _style_preference: str
) -> TypographySpec:
    """Suggest appropriate typography for the cover"""
    try:
        # Genre-based font recommendations
        genre_fonts = {
            'romance': {
                'title_font': 'Playfair Display',
                'subtitle_font': 'Lato',
                'style': 'elegant'
            },
            'mystery': {
                'title_font': 'Oswald',
                'subtitle_font': 'Open Sans',
                'style': 'bold'
            },
            'fantasy': {
                'title_font': 'Cinzel',
                'subtitle_font': 'Crimson Text',
                'style': 'ornate'
            },
            'self-help': {
                'title_font': 'Montserrat',
                'subtitle_font': 'Source Sans Pro',
                'style': 'clean'
            },
            'business': {
                'title_font': 'Roboto Slab',
                'subtitle_font': 'Roboto',
                'style': 'professional'
            }
        }
        
        font_spec = genre_fonts.get(genre.lower(), {
            'title_font': 'Open Sans',
            'subtitle_font': 'Open Sans',
            'style': 'neutral'
        })
        
        # Calculate font sizes based on title length
        title_length = len(title)
        if title_length < 20:
            title_size = 48
        elif title_length < 40:
            title_size = 36
        else:
            title_size = 28
        
        typography = TypographySpec(
            title_font=font_spec['title_font'],
            subtitle_font=font_spec['subtitle_font'],
            title_size=title_size,
            subtitle_size=int(title_size * 0.6),
            font_weight='bold' if font_spec['style'] in ['bold', 'professional'] else 'normal',
            text_effects=['shadow'] if genre.lower() in ['mystery', 'fantasy'] else []
        )
        
        return typography
        
    except Exception as e:
        logger.error(f"Error suggesting typography: {str(e)}")
        return TypographySpec(
            title_font='Open Sans',
            subtitle_font='Open Sans',
            title_size=36,
            subtitle_size=24,
            font_weight='normal',
            text_effects=[]
        )

@cover_designer.tool
async def create_image_prompts(
    _ctx: RunContext[CoverDesignDependencies],
    title: str,
    genre: str,
    concept: str,
    style: str
) -> List[str]:
    """Create AI image generation prompts for cover artwork"""
    try:
        base_prompt = f"Book cover design for '{title}', {genre} genre, {concept} concept, {style} style"
        
        prompts = [
            f"{base_prompt}, professional book cover, high quality, detailed artwork, commercial appeal",
            f"{base_prompt}, minimalist design, clean composition, modern aesthetic",
            f"{base_prompt}, dramatic lighting, compelling visual narrative, eye-catching",
            f"{base_prompt}, symbolic imagery, metaphorical elements, artistic interpretation"
        ]
        
        # Add genre-specific elements
        genre_elements = {
            'romance': 'romantic atmosphere, warm lighting, emotional connection',
            'mystery': 'mysterious shadows, noir aesthetic, suspenseful mood',
            'fantasy': 'magical elements, fantastical creatures, otherworldly landscapes',
            'self-help': 'uplifting imagery, success symbols, positive energy',
            'business': 'professional setting, success imagery, corporate aesthetic'
        }
        
        if genre.lower() in genre_elements:
            element = genre_elements[genre.lower()]
            prompts = [f"{prompt}, {element}" for prompt in prompts]
        
        return prompts
        
    except Exception as e:
        logger.error(f"Error creating image prompts: {str(e)}")
        return [f"Book cover for {title}, {genre} genre"]

@cover_designer.tool
async def analyze_market_positioning(
    _ctx: RunContext[CoverDesignDependencies],
    genre: str,
    _target_audience: str,
    price_point: float
) -> Dict[str, Any]:
    """Analyze market positioning for the cover design"""
    try:
        # Mock market analysis (in real implementation, analyze actual market data)
        positioning_analysis = {
            'target_demographic': {
                'primary_age_group': '25-45' if genre in ['romance', 'self-help'] else '18-65',
                'gender_skew': 'female' if genre == 'romance' else 'neutral',
                'reading_preferences': 'digital-first' if price_point < 5.0 else 'mixed'
            },
            'competitive_landscape': {
                'saturation_level': 'high' if genre in ['romance', 'mystery'] else 'medium',
                'design_trends': ['minimalist', 'bold typography', 'symbolic imagery'],
                'differentiation_opportunities': ['unique color palette', 'innovative layout', 'genre-bending elements']
            },
            'market_expectations': {
                'visual_quality': 'high',
                'genre_adherence': 'moderate to high',
                'innovation_tolerance': 'medium',
                'price_sensitivity': 'high' if price_point < 3.0 else 'medium'
            },
            'success_factors': [
                'Clear genre identification',
                'Professional execution',
                'Emotional appeal',
                'Thumbnail readability',
                'Competitive differentiation'
            ]
        }
        
        return positioning_analysis
        
    except Exception as e:
        logger.error(f"Error analyzing market positioning: {str(e)}")
        return {"error": str(e)}

# ============================================================================
# Helper Functions
# ============================================================================

def _generate_complementary_color(base_color: str) -> str:
    """Generate a complementary color"""
    # Simple complementary color generation (in real implementation, use proper color theory)
    color_map = {
        '#FF6B35': '#35A8FF',
        '#F7931E': '#1E5CF7',
        '#FFD23F': '#3F4DFF',
        '#4A90A4': '#A4644A',
        '#E74C3C': '#3CE7B4',
        '#2980B9': '#B92980'
    }
    return color_map.get(base_color, '#95A5A6')

def _generate_accent_color(base_color: str) -> str:
    """Generate an accent color"""
    # Simple accent color generation
    accent_map = {
        '#FF6B35': '#FFD700',
        '#F7931E': '#FF69B4',
        '#FFD23F': '#32CD32',
        '#4A90A4': '#FFA500',
        '#E74C3C': '#FFD700',
        '#2980B9': '#FF6347'
    }
    return accent_map.get(base_color, '#F39C12')

# ============================================================================
# Main Design Function
# ============================================================================

async def design_book_cover(
    title: str,
    author: str,
    genre: str,
    style: str = "modern",
    target_audience: str = "general",
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    """
    Design a comprehensive book cover
    
    Args:
        title: Book title
        author: Author name
        genre: Book genre
        style: Design style preference
        target_audience: Target audience
        user_id: User requesting the design
    """
    start_time = datetime.now()
    
    try:
        # Create dependencies
        deps = CoverDesignDependencies(
            db_deps=DatabaseDependencies(user_id=user_id),
            ai_deps=AIModelDependencies(),
            style=style
        )
        
        # Run the cover design process
        result = await cover_designer.run(
            f"""Design a compelling book cover for:
            
            Title: "{title}"
            Author: {author}
            Genre: {genre}
            Style: {style}
            Target Audience: {target_audience}
            
            Requirements:
            - Create a comprehensive design specification
            - Ensure genre appropriateness and market appeal
            - Provide multiple design variations
            - Include detailed production notes
            - Consider both print and digital formats
            - Optimize for thumbnail visibility
            """,
            deps=deps
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return create_success_result(
            agent_name="pydantic_ai_cover_designer",
            data=result.output.model_dump(),
            execution_time=execution_time,
            metadata={
                'title': title,
                'author': author,
                'genre': genre,
                'style': style,
                'target_audience': target_audience
            }
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Cover design failed: {str(e)}")
        return create_error_result(
            agent_name="pydantic_ai_cover_designer",
            error_message=str(e),
            execution_time=execution_time
        )

# Register the agents
agent_registry.register_agent("cover_designer", cover_designer)
agent_registry.register_agent("cover_market_analyzer", market_analyzer)
