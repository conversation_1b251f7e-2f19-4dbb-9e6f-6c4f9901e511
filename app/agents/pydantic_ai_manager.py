"""
PydanticAI Agent Manager
Central manager for all PydanticAI agents with unified interface
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

from .pydantic_ai_base import (
    AgentExecutionResult,
    create_error_result,
    agent_registry
)

# Import all PydanticAI agents to register them
from .pydantic_ai_manuscript_generator import generate_manuscript
from .pydantic_ai_trend_analyzer import analyze_market_trends, analyze_keyword_opportunities
from .pydantic_ai_sales_monitor import monitor_sales_performance
from .pydantic_ai_cover_designer import design_book_cover
from .pydantic_ai_kdp_uploader import upload_to_kdp
from .pydantic_ai_additional_agents import research_topic, personalize_content, generate_multimodal_content

logger = logging.getLogger(__name__)

class PydanticAIAgentManager:
    """
    Central manager for all PydanticAI agents
    Provides a unified interface for agent execution and management
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._execution_history = []
    
    async def execute_agent(
        self,
        agent_name: str,
        task_data: Dict[str, Any],
        user_id: Optional[int] = None
    ) -> AgentExecutionResult:
        """
        Execute a specific agent with given task data
        
        Args:
            agent_name: Name of the agent to execute
            task_data: Task-specific data and parameters
            user_id: User requesting the execution
        """
        start_time = datetime.now()
        
        try:
            # Route to appropriate agent function
            if agent_name == "manuscript_generator":
                result = await self._execute_manuscript_generator(task_data, user_id)
            elif agent_name == "trend_analyzer":
                result = await self._execute_trend_analyzer(task_data, user_id)
            elif agent_name == "sales_monitor":
                result = await self._execute_sales_monitor(task_data, user_id)
            elif agent_name == "cover_designer":
                result = await self._execute_cover_designer(task_data, user_id)
            elif agent_name == "kdp_uploader":
                result = await self._execute_kdp_uploader(task_data, user_id)
            elif agent_name == "research_assistant":
                result = await self._execute_research_assistant(task_data, user_id)
            elif agent_name == "personalization_engine":
                result = await self._execute_personalization_engine(task_data, user_id)
            elif agent_name == "multimodal_generator":
                result = await self._execute_multimodal_generator(task_data, user_id)
            else:
                raise ValueError(f"Unknown agent: {agent_name}")
            
            # Log execution
            self._log_execution(agent_name, result, user_id)
            
            return result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            error_result = create_error_result(
                agent_name=agent_name,
                error_message=str(e),
                execution_time=execution_time
            )
            self._log_execution(agent_name, error_result, user_id)
            return error_result
    
    async def execute_workflow(
        self,
        workflow_steps: List[Dict[str, Any]],
        user_id: Optional[int] = None
    ) -> List[AgentExecutionResult]:
        """
        Execute a workflow consisting of multiple agent steps
        
        Args:
            workflow_steps: List of workflow steps, each containing agent_name and task_data
            user_id: User requesting the workflow execution
        """
        results = []
        workflow_context = {}
        
        for step in workflow_steps:
            agent_name = step.get('agent_name')
            task_data = step.get('task_data', {})

            # Validate agent_name
            if not agent_name:
                continue  # Skip steps without agent_name

            # Add workflow context to task data
            task_data['workflow_context'] = workflow_context

            # Execute the step
            result = await self.execute_agent(agent_name, task_data, user_id)
            results.append(result)
            
            # Update workflow context with results
            if result.success:
                workflow_context[agent_name] = result.data
            else:
                # Stop workflow on error if not configured to continue
                if not step.get('continue_on_error', False):
                    break
        
        return results
    
    async def _execute_manuscript_generator(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute manuscript generator agent"""
        return await generate_manuscript(
            trend_data=task_data.get('trend_data', {}),
            user_id=user_id,
            style=task_data.get('style', 'professional'),
            target_audience=task_data.get('target_audience', 'general adults'),
            target_length=task_data.get('target_length', 8000),
            output_formats=task_data.get('output_formats', ['docx', 'epub', 'pdf'])
        )
    
    async def _execute_trend_analyzer(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute trend analyzer agent"""
        analysis_type = task_data.get('analysis_type', 'comprehensive')
        
        if analysis_type == 'keyword_research':
            return await analyze_keyword_opportunities(
                keywords=task_data.get('keywords', []),
                categories=task_data.get('categories', []),
                user_id=user_id
            )
        else:
            return await analyze_market_trends(
                categories=task_data.get('categories'),
                analysis_type=analysis_type,
                user_id=user_id,
                max_results=task_data.get('max_results', 50)
            )
    
    async def _execute_sales_monitor(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute sales monitor agent"""
        return await monitor_sales_performance(
            date_range=task_data.get('date_range', 'last_30_days'),
            include_page_reads=task_data.get('include_page_reads', True),
            generate_insights=task_data.get('generate_insights', True),
            user_id=user_id
        )
    
    async def _execute_cover_designer(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute cover designer agent"""
        return await design_book_cover(
            title=task_data.get('title', ''),
            author=task_data.get('author', ''),
            genre=task_data.get('genre', ''),
            style=task_data.get('style', 'modern'),
            target_audience=task_data.get('target_audience', 'general'),
            user_id=user_id
        )
    
    async def _execute_kdp_uploader(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute KDP uploader agent"""
        return await upload_to_kdp(
            title=task_data.get('title', ''),
            author=task_data.get('author', ''),
            description=task_data.get('description', ''),
            genre=task_data.get('genre', ''),
            keywords=task_data.get('keywords', []),
            manuscript_file=task_data.get('manuscript_file', ''),
            cover_file=task_data.get('cover_file', ''),
            user_id=user_id
        )
    
    async def _execute_research_assistant(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute research assistant agent"""
        return await research_topic(
            topic=task_data.get('topic', ''),
            research_depth=task_data.get('research_depth', 'comprehensive'),
            user_id=user_id
        )
    
    async def _execute_personalization_engine(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute personalization engine agent"""
        return await personalize_content(
            user_data=task_data.get('user_data', {}),
            content_type=task_data.get('content_type', 'book'),
            user_id=user_id
        )
    
    async def _execute_multimodal_generator(
        self,
        task_data: Dict[str, Any],
        user_id: Optional[int]
    ) -> AgentExecutionResult:
        """Execute multimodal generator agent"""
        return await generate_multimodal_content(
            content_brief=task_data.get('content_brief', ''),
            output_formats=task_data.get('output_formats'),
            user_id=user_id
        )
    
    def _log_execution(
        self,
        agent_name: str,
        result: AgentExecutionResult,
        user_id: Optional[int]
    ):
        """Log agent execution for monitoring and debugging"""
        execution_record = {
            'timestamp': datetime.now(),
            'agent_name': agent_name,
            'user_id': user_id,
            'success': result.success,
            'execution_time': result.execution_time,
            'error': result.error if not result.success else None
        }
        
        self._execution_history.append(execution_record)
        
        # Log to standard logger
        if result.success:
            self.logger.info(f"Agent {agent_name} executed successfully in {result.execution_time:.2f}s")
        else:
            self.logger.error(f"Agent {agent_name} failed: {result.error}")
    
    def get_execution_history(
        self,
        agent_name: Optional[str] = None,
        user_id: Optional[int] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get execution history with optional filtering"""
        history = self._execution_history
        
        if agent_name:
            history = [h for h in history if h['agent_name'] == agent_name]
        
        if user_id:
            history = [h for h in history if h['user_id'] == user_id]
        
        return history[-limit:]
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all registered agents"""
        return {
            'registered_agents': agent_registry.list_agents(),
            'total_executions': len(self._execution_history),
            'recent_executions': len([h for h in self._execution_history 
                                    if (datetime.now() - h['timestamp']).seconds < 3600]),
            'success_rate': self._calculate_success_rate()
        }
    
    def _calculate_success_rate(self) -> float:
        """Calculate overall success rate"""
        if not self._execution_history:
            return 0.0
        
        successful = sum(1 for h in self._execution_history if h['success'])
        return (successful / len(self._execution_history)) * 100

# Global agent manager instance
agent_manager = PydanticAIAgentManager()

# Convenience functions for direct agent access
async def execute_agent(
    agent_name: str,
    task_data: Dict[str, Any],
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    """Execute a specific agent"""
    return await agent_manager.execute_agent(agent_name, task_data, user_id)

async def execute_workflow(
    workflow_steps: List[Dict[str, Any]],
    user_id: Optional[int] = None
) -> List[AgentExecutionResult]:
    """Execute a workflow of multiple agents"""
    return await agent_manager.execute_workflow(workflow_steps, user_id)

def get_agent_status() -> Dict[str, Any]:
    """Get status of all agents"""
    return agent_manager.get_agent_status()
