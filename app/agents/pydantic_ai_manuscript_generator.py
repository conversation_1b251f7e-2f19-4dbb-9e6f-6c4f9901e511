"""
PydanticAI Manuscript Generator Agent
Advanced manuscript generation using PydanticAI with structured outputs and tools
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from datetime import datetime
import asyncio
import logging

from .pydantic_ai_base import (
    DatabaseDependencies,
    AIModelDependencies,
    ManuscriptDependencies,
    BookOutlineResult,
    ChapterResult,
    AgentExecutionResult,
    create_success_result,
    create_error_result,
    agent_registry
)
from .pydantic_ai_tools import (
    save_book_draft,
    analyze_content_quality,
    extract_keywords,
    validate_book_concept
)
from app.schemas.book import Manuscript
from app.utils.layout_designer import ProfessionalLayoutDesigner

logger = logging.getLogger(__name__)

# ============================================================================
# Output Models
# ============================================================================

class ManuscriptGenerationResult(BaseModel):
    """Complete manuscript generation result"""
    manuscript: Dict[str, Any] = Field(description="Generated manuscript data")
    outline: BookOutlineResult = Field(description="Book outline used for generation")
    chapters: List[ChapterResult] = Field(description="Generated chapters")
    quality_metrics: Dict[str, float] = Field(description="Quality assessment metrics")
    file_paths: List[str] = Field(description="Paths to generated files")
    generation_metadata: Dict[str, Any] = Field(description="Generation configuration and metadata")
    recommendations: List[str] = Field(description="Recommendations for improvement")

class ContentExpansionResult(BaseModel):
    """Result for content expansion"""
    expanded_content: str = Field(description="Expanded content")
    original_word_count: int = Field(description="Original word count")
    new_word_count: int = Field(description="New word count")
    expansion_quality: float = Field(description="Quality of expansion (0-100)", ge=0, le=100)

# ============================================================================
# PydanticAI Manuscript Generator Agent
# ============================================================================

# Create the PydanticAI agent
manuscript_agent = Agent(
    'openai:gpt-4',
    output_type=ManuscriptGenerationResult,
    deps_type=ManuscriptDependencies,
    system_prompt="""You are an expert e-book manuscript generator specializing in creating high-quality,
    engaging content across various genres. Your goal is to produce well-structured, valuable manuscripts
    that provide real benefit to readers.

    Key principles:
    - Create original, valuable content
    - Maintain consistent tone and style
    - Structure content logically with clear flow
    - Include practical examples and actionable advice
    - Ensure proper chapter organization
    - Focus on reader engagement and value delivery
    """
)

# Create outline generation agent
outline_agent = Agent(
    'openai:gpt-4',
    output_type=BookOutlineResult,
    deps_type=ManuscriptDependencies,
    system_prompt="""You are a book outline specialist. Create detailed, well-structured outlines
    for e-books that will guide the writing process. Focus on logical flow, comprehensive coverage
    of the topic, and reader value."""
)

# Create chapter generation agent
chapter_agent = Agent(
    'anthropic:claude-3-5-sonnet-latest',
    output_type=ChapterResult,
    deps_type=ManuscriptDependencies,
    system_prompt="""You are a chapter writing specialist. Write engaging, informative chapters
    that provide real value to readers. Ensure each chapter has clear structure, practical content,
    and smooth transitions."""
)

# ============================================================================
# Tools for Manuscript Generation
# ============================================================================

@manuscript_agent.tool
async def generate_book_outline(
    ctx: RunContext[ManuscriptDependencies],
    topic: str,
    target_audience: str,
    style: str,
    target_word_count: int
) -> BookOutlineResult:
    """Generate a detailed book outline for the given topic"""
    try:
        # Validate the book concept first
        concept_validation = await validate_book_concept(
            RunContext(deps=ctx.deps.db_deps), 
            topic, 
            "general", 
            target_audience
        )
        
        # Use the outline agent to generate structured outline
        outline_result = await outline_agent.run(
            f"""Create a detailed outline for an e-book on "{topic}" targeting {target_audience}.
            
            Requirements:
            - Target word count: {target_word_count} words
            - Writing style: {style}
            - 8-12 chapters with logical progression
            - Each chapter should be approximately {target_word_count // 10} words
            - Include practical, actionable content
            - Ensure comprehensive topic coverage
            
            Market validation shows: {concept_validation.get('viability_score', 'N/A')}% viability
            """,
            deps=ctx.deps
        )
        
        return outline_result.output
    except Exception as e:
        logger.error(f"Error generating book outline: {str(e)}")
        raise

@manuscript_agent.tool
async def generate_chapter_content(
    ctx: RunContext[ManuscriptDependencies],
    chapter_title: str,
    chapter_outline: Dict[str, Any],
    book_context: Dict[str, Any],
    target_words: int
) -> ChapterResult:
    """Generate content for a specific chapter"""
    try:
        chapter_result = await chapter_agent.run(
            f"""Write a complete chapter titled "{chapter_title}" for the book "{book_context.get('title', 'Unknown')}".
            
            Chapter outline: {chapter_outline}
            Book context: {book_context}
            Target word count: {target_words}
            Writing style: {ctx.deps.style}
            Target audience: {ctx.deps.target_audience}
            
            Requirements:
            - Provide practical, actionable content
            - Use clear headings and subheadings
            - Include specific examples and tips
            - Maintain engaging tone throughout
            - End with key takeaways
            - Ensure smooth flow and transitions
            """,
            deps=ctx.deps
        )
        
        return chapter_result.output
    except Exception as e:
        logger.error(f"Error generating chapter content: {str(e)}")
        raise

@manuscript_agent.tool
async def expand_content(
    ctx: RunContext[ManuscriptDependencies],
    content: str,
    target_additional_words: int,
    focus_areas: List[str]
) -> ContentExpansionResult:
    """Expand existing content to meet word count requirements"""
    try:
        original_word_count = len(content.split())
        
        expansion_prompt = f"""Expand the following content by approximately {target_additional_words} words.
        
        Original content:
        {content}
        
        Focus on expanding these areas: {', '.join(focus_areas)}
        
        Requirements:
        - Add more detailed explanations
        - Include additional examples and case studies
        - Provide more actionable tips
        - Maintain the original tone and style
        - Ensure natural flow and integration
        """
        
        # Use the main agent for expansion
        expansion_result = await manuscript_agent.run(expansion_prompt, deps=ctx.deps)
        
        expanded_content = expansion_result.output.get('expanded_content', content)
        new_word_count = len(expanded_content.split())
        
        # Assess expansion quality
        quality_analysis = await analyze_content_quality(
            RunContext(deps=ctx.deps.ai_deps),
            expanded_content
        )
        
        return ContentExpansionResult(
            expanded_content=expanded_content,
            original_word_count=original_word_count,
            new_word_count=new_word_count,
            expansion_quality=quality_analysis.get('overall_score', 75.0)
        )
    except Exception as e:
        logger.error(f"Error expanding content: {str(e)}")
        raise

@manuscript_agent.tool
async def assess_manuscript_quality(
    ctx: RunContext[ManuscriptDependencies],
    manuscript_content: str
) -> Dict[str, Any]:
    """Assess the overall quality of the manuscript"""
    try:
        quality_analysis = await analyze_content_quality(
            RunContext(deps=ctx.deps.ai_deps),
            manuscript_content,
            criteria=['readability', 'engagement', 'value', 'structure', 'originality']
        )
        
        # Extract keywords for SEO
        keywords = await extract_keywords(
            RunContext(deps=ctx.deps.ai_deps),
            manuscript_content,
            max_keywords=15
        )
        
        return {
            'quality_scores': quality_analysis.get('criteria_scores', {}),
            'overall_score': quality_analysis.get('overall_score', 0),
            'word_count': quality_analysis.get('word_count', 0),
            'keywords': keywords,
            'recommendations': quality_analysis.get('recommendations', []),
            'readability_level': 'intermediate',  # Mock assessment
            'target_audience_fit': 85.0  # Mock score
        }
    except Exception as e:
        logger.error(f"Error assessing manuscript quality: {str(e)}")
        return {"error": str(e)}

@manuscript_agent.tool
async def save_manuscript_draft(
    ctx: RunContext[ManuscriptDependencies],
    title: str,
    content: str,
    metadata: Dict[str, Any]
) -> Dict[str, Any]:
    """Save the manuscript as a draft"""
    try:
        result = await save_book_draft(
            RunContext(deps=ctx.deps.db_deps),
            title=title,
            content=content,
            category=metadata.get('category', 'general'),
            metadata=metadata
        )
        return result
    except Exception as e:
        logger.error(f"Error saving manuscript draft: {str(e)}")
        return {"error": str(e)}

# ============================================================================
# Main Generation Function
# ============================================================================

async def generate_manuscript(
    trend_data: Dict[str, Any],
    user_id: Optional[int] = None,
    style: str = "professional",
    target_audience: str = "general adults",
    target_length: int = 8000,
    output_formats: List[str] = None
) -> AgentExecutionResult:
    """
    Generate a complete manuscript using PydanticAI
    
    Args:
        trend_data: Market trend data to base the manuscript on
        user_id: ID of the user requesting generation
        style: Writing style preference
        target_audience: Target audience for the book
        target_length: Target word count
        output_formats: Desired output formats
    """
    start_time = datetime.now()
    
    try:
        # Create dependencies
        deps = ManuscriptDependencies(
            db_deps=DatabaseDependencies(user_id=user_id),
            ai_deps=AIModelDependencies(),
            target_length=target_length,
            style=style,
            target_audience=target_audience,
            output_formats=output_formats or ['docx', 'epub', 'pdf']
        )
        
        # Extract best opportunity from trend data
        opportunities = trend_data.get('opportunities', [])
        if not opportunities:
            raise ValueError("No opportunities found in trend data")
        
        best_opportunity = opportunities[0]  # Take the highest-ranked opportunity
        topic = best_opportunity.get('title_suggestion', 'Unknown Topic')
        
        # Generate manuscript using the agent
        result = await manuscript_agent.run(
            f"""Generate a complete manuscript for the topic: "{topic}"
            
            Market opportunity data: {best_opportunity}
            Additional trend insights: {trend_data.get('analysis_summary', {})}
            
            Please create a comprehensive, high-quality manuscript that addresses this market opportunity.
            """,
            deps=deps
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return create_success_result(
            agent_name="pydantic_ai_manuscript_generator",
            data=result.output.model_dump(),
            execution_time=execution_time,
            metadata={
                'trend_data': trend_data,
                'generation_config': {
                    'style': style,
                    'target_audience': target_audience,
                    'target_length': target_length,
                    'output_formats': output_formats
                }
            }
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Manuscript generation failed: {str(e)}")
        return create_error_result(
            agent_name="pydantic_ai_manuscript_generator",
            error_message=str(e),
            execution_time=execution_time
        )

# Register the agent
agent_registry.register_agent("manuscript_generator", manuscript_agent)
agent_registry.register_agent("outline_generator", outline_agent)
agent_registry.register_agent("chapter_generator", chapter_agent)
