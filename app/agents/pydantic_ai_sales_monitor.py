"""
PydanticAI Sales Monitor Agent
Advanced sales monitoring and analytics using PydanticAI with KDP integration
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from datetime import datetime
import asyncio
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

from .pydantic_ai_base import (
    DatabaseDependencies,
    ScrapingDependencies,
    SalesMonitorDependencies,
    AgentExecutionResult,
    create_success_result,
    create_error_result,
    agent_registry
)

logger = logging.getLogger(__name__)

# ============================================================================
# Output Models
# ============================================================================

class BookSalesData(BaseModel):
    """Individual book sales data"""
    book_id: str = Field(description="Book identifier")
    title: str = Field(description="Book title")
    units_sold: int = Field(description="Units sold in period")
    revenue: float = Field(description="Revenue generated")
    royalties: float = Field(description="Royalties earned")
    page_reads: int = Field(description="Kindle Unlimited page reads")
    ranking: Optional[int] = Field(description="Current Amazon ranking")
    rating: Optional[float] = Field(description="Current rating")
    review_count: int = Field(description="Number of reviews")

class PerformanceMetrics(BaseModel):
    """Performance metrics and insights"""
    total_sales: int = Field(description="Total units sold")
    total_revenue: float = Field(description="Total revenue")
    total_royalties: float = Field(description="Total royalties")
    average_price: float = Field(description="Average selling price")
    conversion_rate: float = Field(description="Sales conversion rate")
    growth_rate: float = Field(description="Period-over-period growth rate")
    best_performer: Optional[BookSalesData] = Field(description="Best performing book")
    worst_performer: Optional[BookSalesData] = Field(description="Worst performing book")

class SalesInsights(BaseModel):
    """AI-generated sales insights and recommendations"""
    key_insights: List[str] = Field(description="Key insights from sales data")
    performance_trends: List[str] = Field(description="Identified performance trends")
    recommendations: List[str] = Field(description="Actionable recommendations")
    risk_factors: List[str] = Field(description="Potential risk factors")
    opportunities: List[str] = Field(description="Growth opportunities")
    confidence_score: float = Field(description="Confidence in analysis (0-100)", ge=0, le=100)

class ComprehensiveSalesReport(BaseModel):
    """Complete sales monitoring report"""
    reporting_period: str = Field(description="Period covered by the report")
    books_data: List[BookSalesData] = Field(description="Individual book performance data")
    performance_metrics: PerformanceMetrics = Field(description="Overall performance metrics")
    insights: SalesInsights = Field(description="AI-generated insights and recommendations")
    market_comparison: Dict[str, Any] = Field(description="Comparison with market benchmarks")
    forecast: Dict[str, Any] = Field(description="Performance forecast")

# ============================================================================
# PydanticAI Sales Monitor Agent
# ============================================================================

# Main sales monitoring agent
sales_monitor = Agent(
    'openai:gpt-4',
    deps_type=SalesMonitorDependencies,
    output_type=ComprehensiveSalesReport,
    system_prompt="""You are an expert sales analytics specialist for e-book publishing. 
    Your role is to analyze sales data, identify trends, and provide actionable insights 
    to help authors optimize their publishing strategy.
    
    Key capabilities:
    - Analyze sales performance across multiple books and time periods
    - Identify trends and patterns in sales data
    - Compare performance against market benchmarks
    - Generate actionable recommendations for improvement
    - Forecast future performance based on current trends
    - Assess market positioning and competitive landscape
    
    Always provide data-driven insights with specific, actionable recommendations."""
)

# Specialized insights generator
insights_generator = Agent(
    'anthropic:claude-3-sonnet',
    deps_type=SalesMonitorDependencies,
    output_type=SalesInsights,
    system_prompt="""You are a sales insights specialist focused on e-book publishing analytics. 
    Analyze sales data to generate deep insights and strategic recommendations for authors."""
)

# ============================================================================
# Tools for Sales Monitoring
# ============================================================================

@sales_monitor.tool
async def collect_kdp_sales_data(
    ctx: RunContext[SalesMonitorDependencies],
    date_range: str = "last_30_days",
    include_page_reads: bool = True
) -> List[BookSalesData]:
    """Collect sales data from KDP dashboard"""
    try:
        # Validate credentials
        if not ctx.deps.kdp_email or not ctx.deps.kdp_password:
            raise ValueError("KDP credentials not configured")
        
        # Setup Chrome driver
        chrome_options = Options()
        if ctx.deps.scraping_deps.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        
        try:
            # Login to KDP
            await _login_to_kdp(driver, ctx.deps.kdp_email, ctx.deps.kdp_password)
            
            # Navigate to reports
            driver.get("https://kdp.amazon.com/en_US/reports")
            await asyncio.sleep(3)
            
            # Set date range
            await _set_date_range(driver, date_range)
            
            # Extract sales data
            sales_data = await _extract_sales_data(driver, include_page_reads)
            
            return sales_data
            
        finally:
            driver.quit()
            
    except Exception as e:
        logger.error(f"Error collecting KDP sales data: {str(e)}")
        return []

@sales_monitor.tool
async def analyze_performance_trends(
    _ctx: RunContext[SalesMonitorDependencies],
    sales_data: List[BookSalesData],
    _comparison_period: str = "previous_month"
) -> Dict[str, Any]:
    """Analyze performance trends and changes"""
    try:
        if not sales_data:
            return {"error": "No sales data available for analysis"}
        
        # Calculate current period metrics
        total_sales = sum(book.units_sold for book in sales_data)
        total_revenue = sum(book.revenue for book in sales_data)
        total_royalties = sum(book.royalties for book in sales_data)
        
        # Mock comparison data (in real implementation, fetch historical data)
        previous_sales = int(total_sales * 0.85)  # Mock 15% growth
        previous_revenue = total_revenue * 0.82   # Mock 18% revenue growth
        
        # Calculate growth rates
        sales_growth = ((total_sales - previous_sales) / previous_sales * 100) if previous_sales > 0 else 0
        revenue_growth = ((total_revenue - previous_revenue) / previous_revenue * 100) if previous_revenue > 0 else 0
        
        # Identify best and worst performers
        best_performer = max(sales_data, key=lambda x: x.units_sold) if sales_data else None
        worst_performer = min(sales_data, key=lambda x: x.units_sold) if sales_data else None
        
        return {
            'current_period': {
                'total_sales': total_sales,
                'total_revenue': total_revenue,
                'total_royalties': total_royalties
            },
            'growth_metrics': {
                'sales_growth_percent': round(sales_growth, 2),
                'revenue_growth_percent': round(revenue_growth, 2),
                'trend_direction': 'up' if sales_growth > 0 else 'down'
            },
            'performance_leaders': {
                'best_performer': best_performer.model_dump() if best_performer else None,
                'worst_performer': worst_performer.model_dump() if worst_performer else None
            },
            'market_position': 'strong' if sales_growth > 10 else 'stable' if sales_growth > 0 else 'declining'
        }
        
    except Exception as e:
        logger.error(f"Error analyzing performance trends: {str(e)}")
        return {"error": str(e)}

@sales_monitor.tool
async def generate_market_comparison(
    _ctx: RunContext[SalesMonitorDependencies],
    sales_data: List[BookSalesData]
) -> Dict[str, Any]:
    """Compare performance against market benchmarks"""
    try:
        if not sales_data:
            return {"error": "No sales data for comparison"}
        
        # Calculate average metrics
        avg_units = sum(book.units_sold for book in sales_data) / len(sales_data)
        avg_revenue = sum(book.revenue for book in sales_data) / len(sales_data)
        avg_rating = sum(book.rating for book in sales_data if book.rating) / len([b for b in sales_data if b.rating])
        
        # Mock market benchmarks (in real implementation, use actual market data)
        market_benchmarks = {
            'average_monthly_sales': 150,
            'average_monthly_revenue': 450.0,
            'average_rating': 4.2,
            'top_10_percent_sales': 500,
            'median_sales': 75
        }
        
        # Calculate performance vs benchmarks
        sales_percentile = min(100, (avg_units / market_benchmarks['top_10_percent_sales']) * 100)
        revenue_vs_market = (avg_revenue / market_benchmarks['average_monthly_revenue']) * 100
        rating_vs_market = (avg_rating / market_benchmarks['average_rating']) * 100
        
        return {
            'your_performance': {
                'average_sales': round(avg_units, 2),
                'average_revenue': round(avg_revenue, 2),
                'average_rating': round(avg_rating, 2)
            },
            'market_benchmarks': market_benchmarks,
            'performance_comparison': {
                'sales_percentile': round(sales_percentile, 1),
                'revenue_vs_market': round(revenue_vs_market, 1),
                'rating_vs_market': round(rating_vs_market, 1),
                'overall_ranking': 'above_average' if sales_percentile > 60 else 'average' if sales_percentile > 30 else 'below_average'
            }
        }
        
    except Exception as e:
        logger.error(f"Error generating market comparison: {str(e)}")
        return {"error": str(e)}

@sales_monitor.tool
async def forecast_performance(
    _ctx: RunContext[SalesMonitorDependencies],
    sales_data: List[BookSalesData],
    forecast_period: str = "next_30_days"
) -> Dict[str, Any]:
    """Generate performance forecast based on current trends"""
    try:
        if not sales_data:
            return {"error": "No sales data for forecasting"}
        
        # Calculate current metrics
        current_total_sales = sum(book.units_sold for book in sales_data)
        current_total_revenue = sum(book.revenue for book in sales_data)
        
        # Simple trend-based forecasting (in real implementation, use more sophisticated models)
        # Assume current data represents last 30 days
        daily_avg_sales = current_total_sales / 30
        daily_avg_revenue = current_total_revenue / 30
        
        # Apply growth factor based on recent performance
        growth_factor = 1.05  # Mock 5% growth
        
        # Forecast for next period
        if forecast_period == "next_30_days":
            forecast_days = 30
        elif forecast_period == "next_7_days":
            forecast_days = 7
        else:
            forecast_days = 30
        
        forecasted_sales = int(daily_avg_sales * forecast_days * growth_factor)
        forecasted_revenue = daily_avg_revenue * forecast_days * growth_factor
        
        return {
            'forecast_period': forecast_period,
            'current_performance': {
                'total_sales': current_total_sales,
                'total_revenue': round(current_total_revenue, 2),
                'daily_average_sales': round(daily_avg_sales, 2)
            },
            'forecast': {
                'expected_sales': forecasted_sales,
                'expected_revenue': round(forecasted_revenue, 2),
                'confidence_level': 75,  # Mock confidence
                'growth_assumption': '5% growth rate'
            },
            'scenarios': {
                'optimistic': {
                    'sales': int(forecasted_sales * 1.2),
                    'revenue': round(forecasted_revenue * 1.2, 2)
                },
                'pessimistic': {
                    'sales': int(forecasted_sales * 0.8),
                    'revenue': round(forecasted_revenue * 0.8, 2)
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error forecasting performance: {str(e)}")
        return {"error": str(e)}

# ============================================================================
# Helper Functions
# ============================================================================

async def _login_to_kdp(driver: webdriver.Chrome, email: str, password: str):
    """Login to KDP dashboard"""
    driver.get("https://kdp.amazon.com/en_US/signin")
    await asyncio.sleep(2)
    
    # Enter email
    email_field = driver.find_element("id", "ap_email")
    email_field.send_keys(email)
    
    continue_button = driver.find_element("id", "continue")
    continue_button.click()
    await asyncio.sleep(2)
    
    # Enter password
    password_field = driver.find_element("id", "ap_password")
    password_field.send_keys(password)
    
    signin_button = driver.find_element("id", "signInSubmit")
    signin_button.click()
    await asyncio.sleep(3)

async def _set_date_range(_driver: webdriver.Chrome, _date_range: str):
    """Set the date range for reports"""
    # This would implement the actual date range selection
    # For now, just wait for page to load
    await asyncio.sleep(2)

async def _extract_sales_data(_driver: webdriver.Chrome, _include_page_reads: bool) -> List[BookSalesData]:
    """Extract sales data from KDP reports page"""
    # Mock sales data extraction (in real implementation, parse actual KDP data)
    mock_sales_data = [
        BookSalesData(
            book_id="B001",
            title="Sample Book 1",
            units_sold=45,
            revenue=135.0,
            royalties=94.5,
            page_reads=1250,
            ranking=15000,
            rating=4.3,
            review_count=23
        ),
        BookSalesData(
            book_id="B002",
            title="Sample Book 2",
            units_sold=32,
            revenue=96.0,
            royalties=67.2,
            page_reads=890,
            ranking=25000,
            rating=4.1,
            review_count=18
        )
    ]
    
    return mock_sales_data

# ============================================================================
# Main Monitoring Function
# ============================================================================

async def monitor_sales_performance(
    date_range: str = "last_30_days",
    include_page_reads: bool = True,
    generate_insights: bool = True,
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    """
    Perform comprehensive sales monitoring and analysis
    
    Args:
        date_range: Period to analyze (last_7_days, last_30_days, last_90_days)
        include_page_reads: Whether to include Kindle Unlimited page reads
        generate_insights: Whether to generate AI insights
        user_id: User requesting the analysis
    """
    start_time = datetime.now()
    
    try:
        # Create dependencies
        deps = SalesMonitorDependencies(
            db_deps=DatabaseDependencies(user_id=user_id),
            scraping_deps=ScrapingDependencies(headless=True)
        )
        
        # Run the sales monitoring
        result = await sales_monitor.run(
            f"""Perform comprehensive sales monitoring and analysis for the period: {date_range}
            
            Analysis requirements:
            - Collect current sales data from KDP
            - Analyze performance trends and growth
            - Compare against market benchmarks
            - Generate performance forecast
            - Provide actionable insights and recommendations
            
            Include page reads: {include_page_reads}
            Generate insights: {generate_insights}
            """,
            deps=deps
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return create_success_result(
            agent_name="pydantic_ai_sales_monitor",
            data=result.output.model_dump(),
            execution_time=execution_time,
            metadata={
                'date_range': date_range,
                'include_page_reads': include_page_reads,
                'generate_insights': generate_insights
            }
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Sales monitoring failed: {str(e)}")
        return create_error_result(
            agent_name="pydantic_ai_sales_monitor",
            error_message=str(e),
            execution_time=execution_time
        )

# Register the agents
agent_registry.register_agent("sales_monitor", sales_monitor)
agent_registry.register_agent("sales_insights_generator", insights_generator)
