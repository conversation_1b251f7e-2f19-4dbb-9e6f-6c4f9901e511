"""
Common PydanticAI Tools
Reusable tools that can be used across different PydanticAI agents
"""

from typing import Dict, Any, List, Optional
from pydantic_ai import RunContext
import asyncio
import logging
from datetime import datetime, timedelta

from .pydantic_ai_base import (
    DatabaseDependencies, 
    ScrapingDependencies, 
    AIModelDependencies,
    TrendAnalysisDependencies,
    SalesMonitorDependencies
)
from app.models.book import Book
from app.models.publication import Publication
from app.models.feedback import ModelPerformance

logger = logging.getLogger(__name__)

# ============================================================================
# Database Tools
# ============================================================================

async def get_user_books(ctx: RunContext[DatabaseDependencies]) -> List[Dict[str, Any]]:
    """Get all books for the current user"""
    try:
        user = await ctx.deps.get_user()
        if not user:
            return []
        
        with ctx.deps.get_db() as db:
            books = db.query(Book).filter(Book.user_id == user.id).all()
            return [
                {
                    'id': book.id,
                    'title': book.title,
                    'status': book.status,
                    'category': book.category,
                    'word_count': book.word_count,
                    'created_at': book.created_at.isoformat() if book.created_at else None
                }
                for book in books
            ]
    except Exception as e:
        logger.error(f"Error getting user books: {str(e)}")
        return []

async def get_book_performance(
    ctx: RunContext[DatabaseDependencies], 
    book_id: int
) -> Dict[str, Any]:
    """Get performance metrics for a specific book"""
    try:
        with ctx.deps.get_db() as db:
            book = db.query(Book).filter(Book.id == book_id).first()
            if not book:
                return {"error": "Book not found"}
            
            performance = db.query(ModelPerformance).filter(
                ModelPerformance.book_id == book_id
            ).first()
            
            publication = db.query(Publication).filter(
                Publication.book_id == book_id
            ).first()
            
            return {
                'book_title': book.title,
                'status': book.status,
                'user_approval': performance.user_approval if performance else None,
                'quality_score': performance.quality_score if performance else None,
                'publication_status': publication.status if publication else None,
                'sales_data': publication.sales_data if publication else None
            }
    except Exception as e:
        logger.error(f"Error getting book performance: {str(e)}")
        return {"error": str(e)}

async def save_book_draft(
    ctx: RunContext[DatabaseDependencies],
    title: str,
    content: str,
    category: str,
    metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Save a book draft to the database"""
    try:
        user = await ctx.deps.get_user()
        if not user:
            return {"error": "User not found"}
        
        with ctx.deps.get_db() as db:
            book = Book(
                title=title,
                content=content,
                category=category,
                user_id=user.id,
                status="draft",
                word_count=len(content.split()),
                generation_config=metadata or {}
            )
            db.add(book)
            db.commit()
            db.refresh(book)
            
            return {
                'book_id': book.id,
                'title': book.title,
                'status': book.status,
                'word_count': book.word_count
            }
    except Exception as e:
        logger.error(f"Error saving book draft: {str(e)}")
        return {"error": str(e)}

# ============================================================================
# Web Scraping Tools
# ============================================================================

async def scrape_amazon_bestsellers(
    ctx: RunContext[ScrapingDependencies],
    category: str,
    limit: int = 20
) -> List[Dict[str, Any]]:
    """Scrape Amazon bestsellers for a category"""
    try:
        bestsellers = await ctx.deps.amazon_scraper.get_kindle_bestsellers(
            category, limit=limit
        )
        return bestsellers
    except Exception as e:
        logger.error(f"Error scraping Amazon bestsellers: {str(e)}")
        return []

async def scrape_reddit_trends(
    ctx: RunContext[ScrapingDependencies],
    subreddits: List[str],
    limit: int = 10
) -> List[Dict[str, Any]]:
    """Scrape trending posts from Reddit subreddits"""
    try:
        trends = await ctx.deps.reddit_scraper.get_trending_posts(
            subreddits, limit=limit
        )
        return trends
    except Exception as e:
        logger.error(f"Error scraping Reddit trends: {str(e)}")
        return []

async def analyze_competitor_books(
    ctx: RunContext[ScrapingDependencies],
    category: str,
    keywords: List[str]
) -> Dict[str, Any]:
    """Analyze competitor books in a category"""
    try:
        # Get bestsellers and new releases
        bestsellers = await ctx.deps.amazon_scraper.get_kindle_bestsellers(category, limit=50)
        new_releases = await ctx.deps.amazon_scraper.get_kindle_new_releases(category, limit=50)
        
        all_books = bestsellers + new_releases
        
        # Analyze pricing
        prices = [book.get('price', 0) for book in all_books if book.get('price')]
        avg_price = sum(prices) / len(prices) if prices else 0
        
        # Analyze ratings
        ratings = [book.get('rating', 0) for book in all_books if book.get('rating')]
        avg_rating = sum(ratings) / len(ratings) if ratings else 0
        
        # Analyze review counts
        review_counts = [book.get('review_count', 0) for book in all_books if book.get('review_count')]
        avg_reviews = sum(review_counts) / len(review_counts) if review_counts else 0
        
        return {
            'total_books_analyzed': len(all_books),
            'average_price': round(avg_price, 2),
            'average_rating': round(avg_rating, 2),
            'average_review_count': round(avg_reviews, 2),
            'price_range': {
                'min': min(prices) if prices else 0,
                'max': max(prices) if prices else 0
            },
            'top_performers': bestsellers[:5],
            'market_saturation': 'high' if avg_reviews > 500 else 'medium' if avg_reviews > 100 else 'low'
        }
    except Exception as e:
        logger.error(f"Error analyzing competitor books: {str(e)}")
        return {"error": str(e)}

# ============================================================================
# Content Analysis Tools
# ============================================================================

async def analyze_content_quality(
    ctx: RunContext[AIModelDependencies],
    content: str,
    criteria: List[str] = None
) -> Dict[str, Any]:
    """Analyze content quality using AI"""
    if criteria is None:
        criteria = ['readability', 'engagement', 'value', 'structure']
    
    try:
        # This would use the AI model to analyze content
        # For now, return a mock analysis
        word_count = len(content.split())
        
        # Simple heuristics for quality scoring
        readability_score = min(100, max(0, 100 - (word_count / 100)))  # Prefer moderate length
        engagement_score = 75  # Mock score
        value_score = 80  # Mock score
        structure_score = 85  # Mock score
        
        overall_score = (readability_score + engagement_score + value_score + structure_score) / 4
        
        return {
            'overall_score': round(overall_score, 2),
            'criteria_scores': {
                'readability': readability_score,
                'engagement': engagement_score,
                'value': value_score,
                'structure': structure_score
            },
            'word_count': word_count,
            'recommendations': [
                'Consider adding more examples',
                'Improve paragraph structure',
                'Add more engaging headlines'
            ]
        }
    except Exception as e:
        logger.error(f"Error analyzing content quality: {str(e)}")
        return {"error": str(e)}

async def extract_keywords(
    ctx: RunContext[AIModelDependencies],
    content: str,
    max_keywords: int = 10
) -> List[str]:
    """Extract keywords from content using AI"""
    try:
        # Simple keyword extraction (in real implementation, use AI)
        words = content.lower().split()
        word_freq = {}
        
        # Filter out common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
        
        for word in words:
            if len(word) > 3 and word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top keywords
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:max_keywords]]
    except Exception as e:
        logger.error(f"Error extracting keywords: {str(e)}")
        return []

# ============================================================================
# Market Research Tools
# ============================================================================

async def research_market_trends(
    ctx: RunContext[TrendAnalysisDependencies],
    category: str,
    timeframe_days: int = 30
) -> Dict[str, Any]:
    """Research market trends for a category"""
    try:
        # Combine multiple data sources
        amazon_data = await scrape_amazon_bestsellers(
            RunContext(deps=ctx.deps.scraping_deps), category, limit=50
        )
        
        # Analyze trends
        cutoff_date = datetime.now() - timedelta(days=timeframe_days)
        
        # Mock trend analysis (in real implementation, use actual trend data)
        trend_score = 75  # Mock score
        growth_rate = 15.5  # Mock growth rate
        
        return {
            'category': category,
            'trend_score': trend_score,
            'growth_rate': growth_rate,
            'market_size': len(amazon_data),
            'top_books': amazon_data[:5],
            'insights': [
                f'{category} market is showing positive growth',
                'High demand for practical guides',
                'Price point sweet spot is $2.99-$4.99'
            ],
            'opportunities': [
                'Underserved subcategories exist',
                'Room for innovation in format',
                'Growing audience segment identified'
            ]
        }
    except Exception as e:
        logger.error(f"Error researching market trends: {str(e)}")
        return {"error": str(e)}

async def validate_book_concept(
    ctx: RunContext[TrendAnalysisDependencies],
    title: str,
    category: str,
    target_audience: str
) -> Dict[str, Any]:
    """Validate a book concept against market data"""
    try:
        # Research similar books
        competitor_analysis = await analyze_competitor_books(
            RunContext(deps=ctx.deps.scraping_deps), category, [title]
        )
        
        # Calculate viability score
        market_saturation = competitor_analysis.get('market_saturation', 'medium')
        avg_rating = competitor_analysis.get('average_rating', 0)
        
        viability_score = 100
        if market_saturation == 'high':
            viability_score -= 30
        elif market_saturation == 'medium':
            viability_score -= 15
        
        if avg_rating > 4.5:
            viability_score -= 20  # High competition
        
        return {
            'title': title,
            'category': category,
            'viability_score': max(0, viability_score),
            'market_saturation': market_saturation,
            'competition_level': 'high' if avg_rating > 4.3 else 'medium' if avg_rating > 4.0 else 'low',
            'recommendations': [
                'Consider unique angle or niche',
                'Focus on specific target audience',
                'Ensure high-quality content'
            ] if viability_score < 70 else [
                'Good market opportunity',
                'Proceed with development',
                'Monitor competitor releases'
            ]
        }
    except Exception as e:
        logger.error(f"Error validating book concept: {str(e)}")
        return {"error": str(e)}
