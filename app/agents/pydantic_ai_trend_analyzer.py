"""
PydanticAI Trend Analyzer Agent
Advanced trend analysis using PydanticAI with multiple data sources and structured insights
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from datetime import datetime, timedelta
import asyncio
import logging

from .pydantic_ai_base import (
    TrendAnalysisDependencies,
    TrendAnalysisResult,
    AgentExecutionResult,
    create_success_result,
    create_error_result,
    agent_registry
)
from .pydantic_ai_tools import (
    scrape_amazon_bestsellers,
    scrape_reddit_trends,
    analyze_competitor_books,
    research_market_trends
)

logger = logging.getLogger(__name__)

# ============================================================================
# Output Models
# ============================================================================

class MarketOpportunity(BaseModel):
    """Individual market opportunity"""
    title_suggestion: str = Field(description="Suggested book title")
    category: str = Field(description="Book category")
    opportunity_score: float = Field(description="Opportunity score (0-100)", ge=0, le=100)
    market_size: int = Field(description="Estimated market size")
    competition_level: str = Field(description="Competition level (low/medium/high)")
    keywords: List[str] = Field(description="Relevant keywords")
    target_audience: str = Field(description="Primary target audience")
    price_recommendation: float = Field(description="Recommended price point")
    rationale: str = Field(description="Why this is a good opportunity")

class ComprehensiveTrendAnalysis(BaseModel):
    """Complete trend analysis result"""
    opportunities: List[MarketOpportunity] = Field(description="Identified market opportunities")
    market_insights: Dict[str, Any] = Field(description="Overall market insights")
    category_analysis: Dict[str, Dict[str, Any]] = Field(description="Per-category analysis")
    trending_keywords: List[str] = Field(description="Currently trending keywords")
    competitive_landscape: Dict[str, Any] = Field(description="Competitive analysis")
    recommendations: List[str] = Field(description="Strategic recommendations")
    confidence_score: float = Field(description="Analysis confidence (0-100)", ge=0, le=100)

class KeywordAnalysis(BaseModel):
    """Keyword opportunity analysis"""
    keyword: str = Field(description="The keyword")
    search_volume: int = Field(description="Estimated monthly search volume")
    competition_score: float = Field(description="Competition score (0-100)", ge=0, le=100)
    opportunity_score: float = Field(description="Overall opportunity score (0-100)", ge=0, le=100)
    related_keywords: List[str] = Field(description="Related keywords")
    suggested_titles: List[str] = Field(description="Suggested book titles")
    market_potential: str = Field(description="Market potential assessment")

# ============================================================================
# PydanticAI Trend Analyzer Agent
# ============================================================================

# Main trend analysis agent
trend_analyzer = Agent(
    'openai:gpt-4',
    deps_type=TrendAnalysisDependencies,
    output_type=ComprehensiveTrendAnalysis,
    system_prompt="""You are an expert market research analyst specializing in e-book trends and opportunities. 
    Your role is to analyze market data from multiple sources and identify profitable publishing opportunities.
    
    Key capabilities:
    - Analyze Amazon bestseller data and market trends
    - Identify underserved niches and emerging opportunities
    - Assess competition levels and market saturation
    - Provide data-driven recommendations for book topics
    - Evaluate keyword opportunities and search trends
    - Predict market potential and pricing strategies
    
    Always provide actionable insights backed by data analysis."""
)

# Specialized keyword analysis agent
keyword_analyzer = Agent(
    'anthropic:claude-3-sonnet',
    deps_type=TrendAnalysisDependencies,
    output_type=KeywordAnalysis,
    system_prompt="""You are a keyword research specialist focused on book publishing opportunities. 
    Analyze keyword data to identify high-potential, low-competition opportunities for e-book authors."""
)

# ============================================================================
# Tools for Trend Analysis
# ============================================================================

@trend_analyzer.tool
async def analyze_amazon_market(
    ctx: RunContext[TrendAnalysisDependencies],
    categories: List[str],
    depth: str = "comprehensive"
) -> Dict[str, Any]:
    """Analyze Amazon Kindle market across multiple categories"""
    try:
        market_data = {}
        
        for category in categories:
            # Get bestsellers and analyze competition
            bestsellers = await scrape_amazon_bestsellers(
                RunContext(deps=ctx.deps.scraping_deps),
                category,
                limit=50 if depth == "comprehensive" else 20
            )
            
            competitor_analysis = await analyze_competitor_books(
                RunContext(deps=ctx.deps.scraping_deps),
                category,
                []  # No specific keywords for general analysis
            )
            
            market_data[category] = {
                'bestsellers': bestsellers,
                'competitor_analysis': competitor_analysis,
                'opportunity_indicators': {
                    'market_saturation': competitor_analysis.get('market_saturation', 'unknown'),
                    'average_price': competitor_analysis.get('average_price', 0),
                    'average_rating': competitor_analysis.get('average_rating', 0),
                    'entry_difficulty': 'low' if competitor_analysis.get('average_rating', 0) < 4.0 else 'high'
                }
            }
            
            # Rate limiting
            await asyncio.sleep(1)
        
        return market_data
    except Exception as e:
        logger.error(f"Error analyzing Amazon market: {str(e)}")
        return {"error": str(e)}

@trend_analyzer.tool
async def analyze_social_trends(
    ctx: RunContext[TrendAnalysisDependencies],
    categories: List[str]
) -> Dict[str, Any]:
    """Analyze social media trends and discussions"""
    try:
        social_data = {}
        
        # Map categories to relevant subreddits
        category_subreddits = {
            'self-help': ['getmotivated', 'selfimprovement', 'decidingtobebetter'],
            'romance': ['romance', 'romancebooks', 'relationships'],
            'mystery': ['mystery', 'crime', 'thrillerbooks'],
            'fantasy': ['fantasy', 'fantasybooks', 'worldbuilding'],
            'business': ['entrepreneur', 'business', 'startups'],
            'health': ['health', 'fitness', 'nutrition'],
            'cooking': ['cooking', 'recipes', 'mealprep'],
            'parenting': ['parenting', 'daddit', 'mommit'],
            'finance': ['personalfinance', 'investing', 'financialindependence'],
            'productivity': ['productivity', 'getmotivated', 'organization']
        }
        
        for category in categories:
            if category in category_subreddits:
                subreddits = category_subreddits[category]
                
                reddit_trends = await scrape_reddit_trends(
                    RunContext(deps=ctx.deps.scraping_deps),
                    subreddits,
                    limit=20
                )
                
                # Analyze trending topics
                trending_topics = []
                engagement_scores = []
                
                for post in reddit_trends:
                    trending_topics.append(post.get('title', ''))
                    engagement_scores.append(post.get('score', 0) + post.get('num_comments', 0))
                
                avg_engagement = sum(engagement_scores) / len(engagement_scores) if engagement_scores else 0
                
                social_data[category] = {
                    'trending_topics': trending_topics[:10],
                    'average_engagement': avg_engagement,
                    'hot_discussions': reddit_trends[:5],
                    'community_size': 'large' if avg_engagement > 100 else 'medium' if avg_engagement > 50 else 'small'
                }
        
        return social_data
    except Exception as e:
        logger.error(f"Error analyzing social trends: {str(e)}")
        return {"error": str(e)}

@trend_analyzer.tool
async def identify_market_gaps(
    ctx: RunContext[TrendAnalysisDependencies],
    amazon_data: Dict[str, Any],
    social_data: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """Identify market gaps and opportunities"""
    try:
        opportunities = []
        
        for category in amazon_data.keys():
            amazon_info = amazon_data[category]
            social_info = social_data.get(category, {})
            
            # Analyze market saturation
            saturation = amazon_info.get('competitor_analysis', {}).get('market_saturation', 'medium')
            avg_rating = amazon_info.get('competitor_analysis', {}).get('average_rating', 0)
            social_engagement = social_info.get('average_engagement', 0)
            
            # Calculate opportunity score
            opportunity_score = 50  # Base score
            
            if saturation == 'low':
                opportunity_score += 30
            elif saturation == 'medium':
                opportunity_score += 15
            
            if avg_rating < 4.0:
                opportunity_score += 20  # Room for improvement
            
            if social_engagement > 100:
                opportunity_score += 15  # High interest
            
            # Generate opportunity
            if opportunity_score > 60:
                trending_topics = social_info.get('trending_topics', [])
                main_topic = trending_topics[0] if trending_topics else f"{category} guide"
                
                opportunities.append({
                    'category': category,
                    'title_suggestion': f"The Complete Guide to {main_topic.title()}",
                    'opportunity_score': min(100, opportunity_score),
                    'market_saturation': saturation,
                    'social_interest': social_engagement,
                    'competition_rating': avg_rating,
                    'rationale': f"Low competition ({saturation} saturation) with high social interest"
                })
        
        # Sort by opportunity score
        opportunities.sort(key=lambda x: x['opportunity_score'], reverse=True)
        return opportunities[:10]  # Top 10 opportunities
        
    except Exception as e:
        logger.error(f"Error identifying market gaps: {str(e)}")
        return []

@trend_analyzer.tool
async def generate_strategic_recommendations(
    ctx: RunContext[TrendAnalysisDependencies],
    opportunities: List[Dict[str, Any]],
    market_data: Dict[str, Any]
) -> List[str]:
    """Generate strategic recommendations based on analysis"""
    try:
        recommendations = []
        
        if not opportunities:
            recommendations.append("No clear opportunities identified - consider expanding category analysis")
            return recommendations
        
        top_opportunity = opportunities[0]
        
        # Category-specific recommendations
        category = top_opportunity.get('category', 'unknown')
        opportunity_score = top_opportunity.get('opportunity_score', 0)
        
        if opportunity_score > 80:
            recommendations.append(f"High-priority opportunity in {category} - proceed with development immediately")
        elif opportunity_score > 60:
            recommendations.append(f"Good opportunity in {category} - validate concept and proceed")
        else:
            recommendations.append(f"Moderate opportunity in {category} - consider niche targeting")
        
        # Pricing recommendations
        avg_prices = []
        for cat_data in market_data.values():
            if 'competitor_analysis' in cat_data:
                avg_price = cat_data['competitor_analysis'].get('average_price', 0)
                if avg_price > 0:
                    avg_prices.append(avg_price)
        
        if avg_prices:
            avg_market_price = sum(avg_prices) / len(avg_prices)
            recommendations.append(f"Recommended price point: ${avg_market_price:.2f} (market average)")
        
        # Content recommendations
        recommendations.extend([
            "Focus on practical, actionable content to differentiate from competitors",
            "Consider creating a series if the topic has multiple subtopics",
            "Leverage social media trends for content inspiration and marketing"
        ])
        
        return recommendations
        
    except Exception as e:
        logger.error(f"Error generating recommendations: {str(e)}")
        return ["Error generating recommendations - manual analysis recommended"]

# ============================================================================
# Main Analysis Functions
# ============================================================================

async def analyze_market_trends(
    categories: Optional[List[str]] = None,
    analysis_type: str = "comprehensive",
    user_id: Optional[int] = None,
    max_results: int = 50
) -> AgentExecutionResult:
    """
    Perform comprehensive market trend analysis
    
    Args:
        categories: List of categories to analyze
        analysis_type: Type of analysis (comprehensive, quick, keyword_focused)
        user_id: User requesting the analysis
        max_results: Maximum number of results to return
    """
    start_time = datetime.now()
    
    try:
        # Set default categories if none provided
        if not categories:
            categories = [
                'self-help', 'romance', 'mystery', 'fantasy', 'business',
                'health', 'cooking', 'parenting', 'finance', 'productivity'
            ]
        
        # Create dependencies
        deps = TrendAnalysisDependencies(
            db_deps=DatabaseDependencies(user_id=user_id),
            scraping_deps=ScrapingDependencies(headless=True),
            categories=categories,
            max_results=max_results
        )
        
        # Run the analysis
        result = await trend_analyzer.run(
            f"""Perform a {analysis_type} market trend analysis for the following categories: {', '.join(categories)}
            
            Analysis requirements:
            - Identify top market opportunities
            - Assess competition levels and market saturation
            - Analyze pricing trends and strategies
            - Provide actionable recommendations
            - Focus on profitable, underserved niches
            
            Maximum results: {max_results}
            """,
            deps=deps
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return create_success_result(
            agent_name="pydantic_ai_trend_analyzer",
            data=result.data.model_dump(),
            execution_time=execution_time,
            metadata={
                'categories_analyzed': categories,
                'analysis_type': analysis_type,
                'max_results': max_results
            }
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Trend analysis failed: {str(e)}")
        return create_error_result(
            agent_name="pydantic_ai_trend_analyzer",
            error_message=str(e),
            execution_time=execution_time
        )

async def analyze_keyword_opportunities(
    keywords: List[str],
    categories: List[str],
    user_id: Optional[int] = None
) -> AgentExecutionResult:
    """Analyze keyword opportunities for book publishing"""
    start_time = datetime.now()
    
    try:
        deps = TrendAnalysisDependencies(
            db_deps=DatabaseDependencies(user_id=user_id),
            scraping_deps=ScrapingDependencies(headless=True),
            categories=categories
        )
        
        keyword_results = []
        
        for keyword in keywords[:10]:  # Limit to 10 keywords
            result = await keyword_analyzer.run(
                f"""Analyze the keyword "{keyword}" for e-book publishing opportunities.
                
                Consider:
                - Search volume and trends
                - Competition analysis
                - Market potential
                - Related keyword opportunities
                - Suggested book titles and angles
                """,
                deps=deps
            )
            
            keyword_results.append(result.data.model_dump())
            await asyncio.sleep(0.5)  # Rate limiting
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return create_success_result(
            agent_name="pydantic_ai_keyword_analyzer",
            data={'keyword_analyses': keyword_results},
            execution_time=execution_time
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Keyword analysis failed: {str(e)}")
        return create_error_result(
            agent_name="pydantic_ai_keyword_analyzer",
            error_message=str(e),
            execution_time=execution_time
        )

# Register the agents
agent_registry.register_agent("trend_analyzer", trend_analyzer)
agent_registry.register_agent("keyword_analyzer", keyword_analyzer)
