### app/api/__init__.py - API Module Exports

# Import all routers to make them available
try:
    from . import auth
except ImportError:
    auth = None

try:
    from . import books  
except ImportError:
    books = None

try:
    from . import trends
except ImportError:
    trends = None

try:
    from . import publications
except ImportError:
    publications = None

try:
    from . import monitoring
except ImportError:
    monitoring = None

__all__ = ["auth", "books", "trends", "publications", "monitoring"]