### app/api/analytics.py - Analytics API

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from typing import Dict, Any

from app.database import get_db
from app.models.user import User
from app.api.auth import get_current_user
from app.ml.data_access import VERLDataAccessor
from app.services.training_scheduler import TrainingScheduler

router = APIRouter()

@router.get("/data-flow")
async def get_data_flow_status(current_user: User = Depends(get_current_user)):
    """Get current data flow and training readiness status"""
    
    data_accessor = VERLDataAccessor()
    
    # Get data statistics
    stats = await data_accessor.get_recent_feedback_stats(days=30)
    category_stats = await data_accessor.get_category_performance(days=60)
    
    # Check training readiness
    scheduler = TrainingScheduler()
    training_conditions = await scheduler.check_training_conditions()
    
    return {
        'data_overview': {
            'total_feedback_points': stats.get('total_books', 0),
            'approval_rate': stats.get('approval_rate', 0),
            'average_quality': stats.get('average_quality', 0),
            'categories_with_data': len(category_stats)
        },
        'training_status': {
            'ready_for_training': training_conditions['should_train'],
            'reason': training_conditions['reason'],
            'data_points': training_conditions['data_points']
        },
        'data_quality': {
            'sufficient_data': stats.get('total_books', 0) >= 20,
            'performance_stable': stats.get('approval_rate', 0) >= 0.7,
            'categories_covered': list(category_stats.keys())
        }
    }