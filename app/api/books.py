### app/api/books.py - Books API

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
import json
from datetime import datetime # Import datetime

from app.database import get_db
from app.models.user import User
from app.models.book import Book
from app.schemas.book import BookCreate, BookResponse, ManuscriptGenerationRequest
from app.api.auth import get_current_user
from app.agents.pydantic_ai_manager import execute_agent
from app.ml.feedback_integration import LiveFeedbackCollector # Add import

router = APIRouter()

@router.get("/", response_model=List[BookResponse])
async def get_user_books(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all books for the current user"""
    books = db.query(Book).filter(Book.user_id == current_user.id).all()
    return books

@router.get("/{book_id}", response_model=BookResponse)
async def get_book(
    book_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific book"""
    book = db.query(Book).filter(
        Book.id == book_id,
        Book.user_id == current_user.id
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    return book

@router.post("/generate", response_model=dict)
async def generate_manuscript(
    request: ManuscriptGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a new manuscript based on trend data"""
    
    # Create database record
    book = Book(
        user_id=current_user.id,
        title=request.title or "Generating...",
        status="generating",
        generation_config=request.model_dump()
    )
    db.add(book)
    db.commit()
    db.refresh(book)
    
    # Start background task using PydanticAI agent
    async def generate_manuscript_with_agent():
        task_data = {
            'trend_data': {},  # Could be populated from trend analysis
            'style': request.style,
            'target_audience': request.target_audience,
            'target_length': request.target_length
        }
        result = await execute_agent("manuscript_generator", task_data, current_user.id)
        # Handle result and update book status

    background_tasks.add_task(generate_manuscript_with_agent)
    
    return {
        "message": "Manuscript generation started",
        "book_id": book.id,
        "status": "generating"
    }

@router.post("/{book_id}/approve")
async def approve_manuscript(
    book_id: int,
    approval_time: float = 0.0,  # Time taken to approve
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Approve manuscript - NOW WITH DATA CAPTURE"""
    
    book = db.query(Book).filter(
        Book.id == book_id,
        Book.user_id == current_user.id
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    if book.status != "awaiting_approval":
        raise HTTPException(status_code=400, detail="Book is not ready for approval")

    # Update book status using proper SQLAlchemy update
    from sqlalchemy import update
    db.execute(
        update(Book)
        .where(Book.id == book_id)
        .values(status="approved", approved_at=datetime.now())
    )
    db.commit()
    
    # 🔥 NEW: Capture feedback for VERL training
    feedback_collector = LiveFeedbackCollector()
    await feedback_collector.capture_approval_feedback(
        book_id=book_id,
        approved=True,
        approval_time_seconds=approval_time
    )
    
    return {"message": "Manuscript approved", "book_id": book_id}

@router.post("/{book_id}/reject")
async def reject_manuscript(
    book_id: int,
    reason: Optional[str] = None,
    rejection_time: float = 0.0, # Add rejection_time parameter
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Reject manuscript - NOW WITH DATA CAPTURE""" # Update docstring
    
    book = db.query(Book).filter(
        Book.id == book_id,
        Book.user_id == current_user.id
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    # Update book status using proper SQLAlchemy update
    from sqlalchemy import update
    update_values = {"status": "rejected"}
    if reason:
        update_values["rejection_reason"] = reason

    db.execute(
        update(Book)
        .where(Book.id == book_id)
        .values(**update_values)
    )
    db.commit()
    
    # 🔥 NEW: Capture rejection feedback # Add feedback capture
    feedback_collector = LiveFeedbackCollector()
    await feedback_collector.capture_approval_feedback(
        book_id=book_id,
        approved=False,
        approval_time_seconds=rejection_time,
        rejection_reason=reason
    )
    
    return {"message": "Manuscript rejected", "book_id": book_id}

@router.post("/{book_id}/covers/generate")
async def generate_covers(
    book_id: int,
    background_tasks: BackgroundTasks,
    style: str = "modern",
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate covers for an approved manuscript"""
    book = db.query(Book).filter(
        Book.id == book_id,
        Book.user_id == current_user.id
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    if book.status != "approved":
        raise HTTPException(status_code=400, detail="Book must be approved first")
    
    # Start cover generation using PydanticAI agent
    async def generate_cover_with_agent():
        task_data = {
            'title': book.title,
            'author': current_user.name if hasattr(current_user, 'name') else 'Author',
            'genre': book.category or 'general',
            'style': style,
            'target_audience': 'general'
        }
        result = await execute_agent("cover_designer", task_data, current_user.id)
        # Handle result and update book status

    background_tasks.add_task(generate_cover_with_agent)
    
    return {
        "message": "Cover generation started",
        "book_id": book_id,
        "style": style
    }

@router.delete("/{book_id}")
async def delete_book(
    book_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a book"""
    book = db.query(Book).filter(
        Book.id == book_id,
        Book.user_id == current_user.id
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    db.delete(book)
    db.commit()
    
    return {"message": "Book deleted successfully"}