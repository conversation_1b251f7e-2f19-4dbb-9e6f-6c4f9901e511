### app/api/feedback.py - Feedback API

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from app.database import get_db
from app.models.user import User
from app.models.feedback import FeedbackMetric, ModelPerformance
from app.api.auth import get_current_user
from app.ml.feedback_collector import FeedbackCollector, MetricType
from app.ml.model_trainer import ReinforcementTrainer
from app.tasks.model_training import auto_tune_system_task

router = APIRouter()
feedback_collector = FeedbackCollector()
trainer = ReinforcementTrainer()

@router.post("/books/{book_id}/approval")
async def record_approval_feedback(
    book_id: int,
    approved: bool,
    approval_time: float,
    rejection_reason: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Record user approval/rejection feedback"""
    
    await feedback_collector.collect_user_approval_feedback(
        book_id=book_id,
        approved=approved,
        approval_time=approval_time,
        rejection_reason=rejection_reason
    )
    
    return {"message": "Feedback recorded", "book_id": book_id}

@router.post("/books/{book_id}/quality")
async def record_quality_feedback(
    book_id: int,
    quality_metrics: Dict[str, float],
    current_user: User = Depends(get_current_user)
):
    """Record quality assessment feedback"""
    
    await feedback_collector.collect_quality_feedback(book_id, quality_metrics)
    
    return {"message": "Quality feedback recorded"}

@router.get("/performance/trends/{metric_type}")
async def get_performance_trends(
    metric_type: str,
    days: int = 30,
    current_user: User = Depends(get_current_user)
):
    """Get performance trends for a specific metric"""
    
    try:
        metric_enum = MetricType(metric_type)
        feedback_collector = FeedbackCollector()
        trends = await feedback_collector.get_performance_trends(metric_type, days)
        return trends
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid metric type")

@router.get("/insights/suggestions")
async def get_improvement_suggestions(
    current_user: User = Depends(get_current_user)
):
    """Get AI-generated improvement suggestions"""
    
    suggestions = await trainer.generate_improvement_suggestions()
    return {"suggestions": suggestions}

@router.post("/training/auto-tune")
async def trigger_auto_tuning(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Trigger automatic system tuning based on feedback"""
    
    # Only allow admin users to trigger auto-tuning
    if not current_user.is_premium:  # Use your admin check here
        raise HTTPException(status_code=403, detail="Admin access required")
    
    # Create a mock auto-tune task for now
    async def auto_tune_system():
        # This would implement actual auto-tuning logic
        pass

    background_tasks.add_task(auto_tune_system)
    
    return {"message": "Auto-tuning started"}

@router.get("/analytics/dashboard")
async def get_analytics_dashboard(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive analytics dashboard data"""
    
    # Get recent performance metrics
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    metrics = db.query(FeedbackMetric).filter(
        FeedbackMetric.timestamp >= start_date
    ).all()
    
    # Aggregate by metric type
    dashboard_data = {}
    
    for metric in metrics:
        metric_type = metric.metric_type
        if metric_type not in dashboard_data:
            dashboard_data[metric_type] = {
                'values': [],
                'timestamps': [],
                'average': 0,
                'trend': 'stable'
            }
        
        dashboard_data[metric_type]['values'].append(metric.value)
        dashboard_data[metric_type]['timestamps'].append(metric.timestamp)
    
    # Calculate averages and trends
    for metric_type, data in dashboard_data.items():
        data['average'] = sum(data['values']) / len(data['values']) if data['values'] else 0
        
        # Simple trend calculation
        if len(data['values']) >= 10:
            recent_avg = sum(data['values'][-5:]) / 5
            older_avg = sum(data['values'][:5]) / 5
            
            if recent_avg > older_avg * 1.05:
                data['trend'] = 'improving'
            elif recent_avg < older_avg * 0.95:
                data['trend'] = 'declining'
            else:
                data['trend'] = 'stable'
    
    return dashboard_data