### app/api/monitoring.py - Monitoring API

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
import json
import asyncio
from typing import Dict, Any
from dataclasses import asdict
from datetime import datetime, timedelta

from app.database import get_db
from app.models.user import User
from app.models.book import Book
from app.models.feedback import ModelPerformance
from app.api.auth import get_current_user

# Mock monitoring classes - these should be implemented properly
class VERLMonitor:
    async def get_data_flow_analysis(self, hours_back: int):
        return {"status": "active", "data_points": 100, "hours_back": hours_back}

    async def get_current_status(self):
        return {"status": "running", "last_update": datetime.now().isoformat()}

    async def start_monitoring(self, interval_seconds: int):
        pass

    def stop_monitoring(self):
        pass

class TrainingTracker:
    async def get_training_progress(self):
        return {"progress": 0.75, "status": "training"}

    async def get_wandb_metrics(self):
        return {"loss": 0.1, "accuracy": 0.95}

# Initialize monitoring instances
verl_monitor = VERLMonitor()
training_tracker = TrainingTracker()

router = APIRouter()

@router.get("/status")
async def get_monitoring_status(current_user: User = Depends(get_current_user)):
    """Get overall system monitoring status"""
    
    db = next(get_db())
    try:
        # Get basic statistics
        total_books = db.query(Book).count()
        total_performance_records = db.query(ModelPerformance).count()
        
        return {
            "system_status": "healthy",
            "stats": {
                "total_books": total_books,
                "total_performance_records": total_performance_records,
            },
            "timestamp": datetime.now().isoformat()
        }
    finally:
        db.close()

@router.get("/data-flow")
async def get_data_flow_metrics(
    hours: int = 24,
    current_user: User = Depends(get_current_user)
):
    """Get data flow analysis"""
    
    analysis = await verl_monitor.get_data_flow_analysis(hours_back=hours)
    return analysis

@router.get("/training/progress")
async def get_training_progress(current_user: User = Depends(get_current_user)):
    """Get current training progress"""
    
    progress = await training_tracker.get_training_progress()
    wandb_metrics = await training_tracker.get_wandb_metrics()
    
    return {
        "progress": progress,
        "metrics": wandb_metrics
    }

@router.get("/live-stream")
async def live_metrics_stream(current_user: User = Depends(get_current_user)):
    """Stream live metrics (Server-Sent Events)"""
    
    async def generate_metrics():
        while True:
            try:
                # Get current metrics
                status = await verl_monitor.get_current_status()
                training_progress = await training_tracker.get_training_progress()
                
                # Combine data
                data = {
                    "timestamp": datetime.now().isoformat(),
                    "monitoring": status,
                    "training": training_progress,
                    "type": "metrics_update"
                }
                
                # Send as SSE
                yield f"data: {json.dumps(data)}\n\n"
                
                # Wait 5 seconds
                await asyncio.sleep(5)
                
            except Exception as e:
                error_data = {
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e),
                    "type": "error"
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                await asyncio.sleep(10)
    
    return StreamingResponse(
        generate_metrics(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*"
        }
    )

@router.post("/start")
async def start_monitoring(current_user: User = Depends(get_current_user)):
    """Start VERL monitoring"""
    
    if not current_user.is_premium:
        raise HTTPException(status_code=403, detail="Premium access required")
    
    await verl_monitor.start_monitoring(interval_seconds=60)
    
    return {"message": "VERL monitoring started", "status": "active"}

@router.post("/stop")
async def stop_monitoring(current_user: User = Depends(get_current_user)):
    """Stop VERL monitoring"""
    
    verl_monitor.stop_monitoring()
    
    return {"message": "VERL monitoring stopped", "status": "inactive"}

@router.get("/diagnostics")
async def get_system_diagnostics(current_user: User = Depends(get_current_user)):
    """Get detailed system diagnostics"""
    
    db = next(get_db())
    try:
        # Database connectivity
        db_health = db.execute(text("SELECT 1")).scalar() == 1
        
        # Recent data availability
        recent_books = db.query(Book).filter(
            Book.created_at >= datetime.now() - timedelta(days=7)
        ).count()
        
        recent_feedback = db.query(ModelPerformance).filter(
            ModelPerformance.created_at >= datetime.now() - timedelta(days=7)
        ).count()
        
        return {
            "database": {
                "healthy": db_health,
                "recent_books": recent_books,
                "recent_feedback": recent_feedback
            },
            "monitoring": {
                "active": True,
                "metrics_collected": recent_feedback
            },
            "training": {
                "verl_available": True,  # Would check actual VERL availability
                "gpu_available": False,  # Would check torch.cuda.is_available() if torch installed
                "last_training": None  # Would get from actual training tracker
            }
        }
        
    finally:
        db.close()