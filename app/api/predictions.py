### app/api/predictions.py - Predictions API

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional

from app.database import get_db
from app.models.user import User
from app.models.book import Book
from app.api.auth import get_current_user
from app.prediction.sales_predictor import sales_predictor
from app.prediction.models import SalesPrediction
from app.tasks.prediction_tasks import train_prediction_models_task, update_prediction_accuracy_task

router = APIRouter()

@router.post("/books/{book_id}/predict")
async def predict_book_performance(
    book_id: int,
    target_price: Optional[float] = 4.99,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate sales prediction for a book"""
    
    # Get the book
    book = db.query(Book).filter(
        Book.id == book_id,
        Book.user_id == current_user.id
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    # Convert book to manuscript for prediction
    manuscript = sales_predictor._book_to_manuscript(book)
    
    # Generate prediction
    prediction_result = await sales_predictor.predict_book_performance(
        manuscript, target_price
    )
    
    # Save prediction to database
    prediction = SalesPrediction(
        book_id=book_id,
        predicted_sales_30d=prediction_result['predictions']['sales_30d'],
        predicted_revenue_30d=prediction_result['predictions']['revenue_30d'],
        predicted_sales_90d=prediction_result['predictions']['sales_90d'],
        predicted_revenue_90d=prediction_result['predictions']['revenue_90d'],
        confidence_score=prediction_result['confidence_score'],
        success_probability=prediction_result['predictions']['success_probability'],
        recommended_price=prediction_result['price_optimization']['recommended_price'],
        market_opportunity_score=prediction_result['market_analysis']['market_opportunity_score'],
        competition_level=prediction_result['market_analysis']['competition_density'],
        risk_level=prediction_result['risk_assessment']['risk_level'],
        features_used=prediction_result['feature_importance']
    )
    
    db.add(prediction)
    db.commit()
    db.refresh(prediction)
    
    return {
        "prediction_id": prediction.id,
        "predictions": prediction_result['predictions'],
        "confidence": prediction_result['confidence_score'],
        "risk_assessment": prediction_result['risk_assessment'],
        "recommendations": prediction_result['recommendations'],
        "price_optimization": prediction_result['price_optimization'],
        "market_analysis": prediction_result['market_analysis']
    }

@router.get("/books/{book_id}/predictions")
async def get_book_predictions(
    book_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all predictions for a book"""
    
    # Verify book ownership
    book = db.query(Book).filter(
        Book.id == book_id,
        Book.user_id == current_user.id
    ).first()
    
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    # Get predictions
    predictions = db.query(SalesPrediction).filter(
        SalesPrediction.book_id == book_id
    ).order_by(SalesPrediction.created_at.desc()).all()
    
    return {
        "book_id": book_id,
        "predictions": [
            {
                "id": p.id,
                "created_at": p.created_at,
                "predicted_sales_30d": p.predicted_sales_30d,
                "predicted_revenue_30d": p.predicted_revenue_30d,
                "confidence_score": p.confidence_score,
                "success_probability": p.success_probability,
                "recommended_price": p.recommended_price,
                "risk_level": p.risk_level
            }
            for p in predictions
        ]
    }

@router.post("/train-models")
async def train_prediction_models(
    background_tasks: BackgroundTasks,
    retrain: bool = False,
    current_user: User = Depends(get_current_user)
):
    """Train/retrain prediction models"""
    
    if not current_user.is_premium:
        raise HTTPException(status_code=403, detail="Premium access required")
    
    # Start training in background
    background_tasks.add_task(train_prediction_models_task, retrain)
    
    return {
        "message": "Model training started",
        "status": "training",
        "retrain": retrain
    }

@router.get("/model-status")
async def get_model_status(current_user: User = Depends(get_current_user)):
    """Get current model training status and performance"""
    
    status = {
        "models_trained": sales_predictor.sales_model is not None,
        "last_trained": sales_predictor.last_trained.isoformat() if sales_predictor.last_trained else None,
        "model_version": sales_predictor.model_version,
        "feature_count": len(sales_predictor.feature_names)
    }
    
    return status

@router.get("/dashboard")
async def get_prediction_dashboard(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get prediction dashboard data"""
    
    # Get user's recent predictions
    recent_predictions = db.query(SalesPrediction).join(Book).filter(
        Book.user_id == current_user.id
    ).order_by(SalesPrediction.created_at.desc()).limit(10).all()
    
    # Calculate statistics
    if recent_predictions:
        avg_confidence = sum(p.confidence_score for p in recent_predictions) / len(recent_predictions)
        avg_success_prob = sum(p.success_probability for p in recent_predictions) / len(recent_predictions)
        high_potential_books = sum(1 for p in recent_predictions if p.success_probability > 0.7)
    else:
        avg_confidence = 0
        avg_success_prob = 0
        high_potential_books = 0
    
    return {
        "total_predictions": len(recent_predictions),
        "average_confidence": avg_confidence,
        "average_success_probability": avg_success_prob,
        "high_potential_books": high_potential_books,
        "recent_predictions": [
            {
                "book_title": p.book.title,
                "predicted_sales": p.predicted_sales_30d,
                "predicted_revenue": p.predicted_revenue_30d,
                "success_probability": p.success_probability,
                "confidence": p.confidence_score,
                "created_at": p.created_at
            }
            for p in recent_predictions
        ]
    }

@router.post("/accuracy/update")
async def update_prediction_accuracy(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Update prediction accuracy based on actual results"""
    
    background_tasks.add_task(update_prediction_accuracy_task)
    
    return {"message": "Accuracy update started"}