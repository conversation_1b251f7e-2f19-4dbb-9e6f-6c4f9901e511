### app/api/trends.py - Trends API

from fastapi import APIRouter, Depends, BackgroundTasks, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional

from app.database import get_db
from app.models.user import User
from app.models.trend import TrendAnalysis
from app.schemas.trend import TrendAnalysisRequest, TrendAnalysisResponse
from app.api.auth import get_current_user
from app.agents.pydantic_ai_manager import execute_agent
from app.tasks.trend_analysis import analyze_trends_task

router = APIRouter()

@router.post("/analyze", response_model=dict)
async def analyze_trends(
    request: TrendAnalysisRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Start trend analysis for specified categories"""
    
    # Create analysis record
    analysis = TrendAnalysis(
        user_id=current_user.id,
        categories=request.categories,
        status="analyzing",
        config=request.dict()
    )
    db.add(analysis)
    db.commit()
    db.refresh(analysis)
    
    # Start background task using PydanticAI agent
    async def analyze_trends_with_agent():
        task_data = {
            "categories": request.categories,
            "analysis_type": "comprehensive",
            "max_results": 50
        }
        result = await execute_agent("trend_analyzer", task_data, current_user.id)
        # Update analysis record with results
        if result.success:
            analysis.status = "completed"
            analysis.results = result.data
        else:
            analysis.status = "failed"
            analysis.error_message = result.error
        db.commit()

    background_tasks.add_task(analyze_trends_with_agent)
    
    return {
        "message": "Trend analysis started",
        "analysis_id": analysis.id,
        "categories": request.categories
    }

@router.get("/", response_model=List[TrendAnalysisResponse])
async def get_trend_analyses(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all trend analyses for the current user"""
    analyses = db.query(TrendAnalysis).filter(
        TrendAnalysis.user_id == current_user.id
    ).order_by(TrendAnalysis.created_at.desc()).all()
    
    return analyses

@router.get("/{analysis_id}", response_model=TrendAnalysisResponse)
async def get_trend_analysis(
    analysis_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific trend analysis"""
    analysis = db.query(TrendAnalysis).filter(
        TrendAnalysis.id == analysis_id,
        TrendAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    return analysis

@router.get("/{analysis_id}/opportunities")
async def get_opportunities(
    analysis_id: int,
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get top opportunities from a trend analysis"""
    analysis = db.query(TrendAnalysis).filter(
        TrendAnalysis.id == analysis_id,
        TrendAnalysis.user_id == current_user.id
    ).first()
    
    if not analysis:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    if analysis.status != "completed":
        return {"message": "Analysis not completed yet", "opportunities": []}
    
    # Extract top opportunities from results
    results = analysis.results or {}
    trends = results.get('trends', [])
    
    return {
        "opportunities": trends[:limit],
        "total_found": len(trends)
    }