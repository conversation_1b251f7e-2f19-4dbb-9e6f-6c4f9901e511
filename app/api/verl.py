### app/api/verl.py - VERL API

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any
from app.database import get_db
from app.models.user import User
from app.api.auth import get_current_user
from app.ml.verl_trainer import VERLEbookTrainer, EbookRLConfig
from app.tasks.verl_training import train_verl_model_task

router = APIRouter()

@router.post("/train")
async def start_verl_training(
    background_tasks: BackgroundTasks,
    config: Dict[str, Any] = {},
    current_user: User = Depends(get_current_user)
):
    """Start VERL model training"""
    
    # Only allow premium users to trigger VERL training
    if not current_user.is_premium:
        raise HTTPException(status_code=403, detail="Premium access required")
    
    background_tasks.add_task(train_verl_model_task, config or {})
    
    return {"message": "VERL training started", "status": "initiated"}

@router.get("/performance")
async def get_verl_performance(
    current_user: User = Depends(get_current_user)
):
    """Get VERL model performance metrics"""
    
    trainer = VERLEbookTrainer(EbookRLConfig())
    performance = await trainer.evaluate_model_performance()
    
    return performance

@router.post("/generate")
async def generate_with_verl(
    prompt: str,
    category: str,
    target_length: int = 1000,
    current_user: User = Depends(get_current_user)
):
    """Generate content using trained VERL model"""
    
    trainer = VERLEbookTrainer(EbookRLConfig())
    
    try:
        content = await trainer.generate_optimized_content(
            prompt=prompt,
            category=category,
            target_length=target_length
        )
        
        return {
            "content": content,
            "method": "verl",
            "word_count": len(content.split())
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(e)}")