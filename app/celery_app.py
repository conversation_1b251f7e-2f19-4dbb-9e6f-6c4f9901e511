### app/celery_app.py - Celery Application Configuration

from celery import Celery
import os
from app.config import settings

def create_celery_app():
    """Create and configure Celery app"""
    
    # Redis/RabbitMQ broker URL
    broker_url = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
    result_backend = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/0")
    
    celery_app = Celery(
        "publish_ai",
        broker=broker_url,
        backend=result_backend,
        include=[
            "app.tasks.manuscript_generation",
            "app.tasks.trend_analysis", 
            "app.tasks.publication",
            "app.tasks.verl_training",
            "app.tasks.model_training"
        ]
    )
    
    # Celery configuration
    celery_app.conf.update(
        # Task routing
        task_routes={
            "app.tasks.manuscript_generation.*": {"queue": "manuscript"},
            "app.tasks.trend_analysis.*": {"queue": "analysis"},
            "app.tasks.publication.*": {"queue": "publishing"},
            "app.tasks.verl_training.*": {"queue": "training"},
            "app.tasks.model_training.*": {"queue": "training"}
        },
        
        # Task execution settings
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        enable_utc=True,
        
        # Worker settings
        worker_prefetch_multiplier=1,
        task_acks_late=True,
        worker_max_tasks_per_child=1000,
        
        # Task time limits
        task_soft_time_limit=1800,  # 30 minutes
        task_time_limit=3600,       # 1 hour
        
        # Retry settings
        task_retry_backoff=True,
        task_retry_backoff_max=600,
        task_max_retries=3,
        
        # Beat schedule for periodic tasks
        beat_schedule={
            "daily-trend-analysis": {
                "task": "app.tasks.trend_analysis.daily_trend_analysis",
                "schedule": 86400.0,  # Every 24 hours
            },
            "weekly-verl-training": {
                "task": "app.tasks.verl_training.train_verl_model_task",
                "schedule": 604800.0,  # Every 7 days
                "kwargs": {"days_back": 30}
            },
            "daily-verl-evaluation": {
                "task": "app.tasks.verl_training.daily_verl_evaluation",
                "schedule": 86400.0,  # Every 24 hours
            },
            "hourly-publication-monitor": {
                "task": "app.tasks.publication.monitor_publications",
                "schedule": 3600.0,  # Every hour
            }
        },
        
        # Result expiration
        result_expires=3600,
        
        # Task result persistence
        result_persistent=True,
        
        # Queue settings
        task_default_queue="default",
        task_create_missing_queues=True
    )
    
    return celery_app

# Create the app instance
celery = create_celery_app()

# Auto-discover tasks
celery.autodiscover_tasks()

if __name__ == "__main__":
    celery.start()