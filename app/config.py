### app/config.py - Configuration Management

from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database
    database_url: str = "sqlite:///./ebooks.db"
    
    # Redis for Celery
    redis_url: str = "redis://localhost:6379"
    
    # Security
    secret_key: str = "your-secret-key-change-this"
    access_token_expire_minutes: int = 30
    
    # AI APIs
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    
    # External APIs
    google_trends_api_key: Optional[str] = None
    amazon_api_key: Optional[str] = None
    
    # Storage
    storage_path: str = "./storage"
    
    # KDP Settings (encrypted)
    kdp_email: Optional[str] = None
    kdp_password: Optional[str] = None
    
    # Generation Settings
    min_book_length: int = 5000
    max_book_length: int = 15000
    books_per_trend: int = 3
    
    # VERL settings
    enable_verl: bool = True
    verl_model_name: str = "microsoft/DialoGPT-medium"
    verl_batch_size: int = 8
    verl_learning_rate: float = 1e-5
    verl_training_epochs: int = 3
    
    # Training thresholds
    min_training_examples: int = 50
    verl_training_interval_hours: int = 2
    
    class Config:
        env_file = ".env"

settings = Settings()