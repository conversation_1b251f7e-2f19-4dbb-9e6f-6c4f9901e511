### app/database.py - Database Configuration and Session Management

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import logging
from contextlib import contextmanager
from typing import Generator

from app.config import settings

logger = logging.getLogger(__name__)

# Create SQLAlchemy engine
if settings.database_url.startswith("sqlite"):
    # SQLite configuration with additional settings for development
    engine = create_engine(
        settings.database_url,
        connect_args={
            "check_same_thread": False,
            "timeout": 20
        },
        poolclass=StaticPool,
        echo=False  # Set to True for SQL query logging
    )
else:
    # PostgreSQL or other databases
    engine = create_engine(
        settings.database_url,
        pool_pre_ping=True,
        pool_recycle=300,
        echo=False
    )

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for models
Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    """
    Database dependency for FastAPI routes
    
    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

@contextmanager
def get_db_context():
    """
    Context manager for database sessions outside of FastAPI
    
    Usage:
        with get_db_context() as db:
            # Use db session here
            pass
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        logger.error(f"Database context error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def init_db():
    """
    Initialize database tables
    
    This should be called when the application starts
    """
    try:
        # Import all models to ensure they are registered with SQLAlchemy
        from app.models import user, book, trend, publication, feedback
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        raise

def reset_db():
    """
    Drop and recreate all database tables
    
    WARNING: This will delete all data!
    Only use in development/testing
    """
    try:
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        logger.warning("Database reset completed - ALL DATA DELETED")
        
    except Exception as e:
        logger.error(f"Failed to reset database: {str(e)}")
        raise

def check_db_connection() -> bool:
    """
    Check if database connection is working
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        logger.info("Database connection check successful")
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {str(e)}")
        return False

# Health check function for monitoring
async def get_db_health() -> dict:
    """
    Get database health status for monitoring endpoints
    
    Returns:
        dict: Database health information
    """
    try:
        is_connected = check_db_connection()
        
        if is_connected:
            # Get basic stats
            with get_db_context() as db:
                # You can add more detailed health checks here
                # For example, count of records in key tables
                from app.models.user import User
                from app.models.book import Book
                
                user_count = db.query(User).count()
                book_count = db.query(Book).count()
                
                return {
                    "status": "healthy",
                    "connected": True,
                    "users": user_count,
                    "books": book_count,
                    "database_url": settings.database_url.split('@')[-1] if '@' in settings.database_url else "sqlite"
                }
        else:
            return {
                "status": "unhealthy",
                "connected": False,
                "error": "Cannot connect to database"
            }
            
    except Exception as e:
        return {
            "status": "unhealthy",
            "connected": False,
            "error": str(e)
        }