### app/main.py - FastAPI Application Entry Point

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.database import engine, Base
from app.api import auth, books, trends, publications, monitoring

# Create tables
Base.metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    yield
    # Shutdown

app = FastAPI(
    title="E-book Generator API",
    description="AI-powered e-book generation and publishing system",
    version="0.1.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Frontend URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers (with None checks)
if auth and hasattr(auth, 'router'):
    app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
if books and hasattr(books, 'router'):
    app.include_router(books.router, prefix="/api/books", tags=["books"])
if trends and hasattr(trends, 'router'):
    app.include_router(trends.router, prefix="/api/trends", tags=["trends"])
if publications and hasattr(publications, 'router'):
    app.include_router(publications.router, prefix="/api/publications", tags=["publications"])
if monitoring and hasattr(monitoring, 'router'):
    app.include_router(monitoring.router, prefix="/api/monitoring", tags=["monitoring"])

@app.get("/")
async def root():
    return {"message": "E-book Generator API", "version": "0.1.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

# Startup and shutdown events are now handled by the lifespan context manager above