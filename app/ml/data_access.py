### app/ml/data_access.py - Data Access for Machine Learning 
#
import asyncio
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from datetime import datetime, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc

from app.database import get_db
from app.models.book import Book
from app.models.feedback import FeedbackMetric, ModelPerformance
from app.models.publication import Publication, SalesData
from app.models.user import User

class VERLDataAccessor:
    """Centralized data access for VERL training"""
    
    def __init__(self):
        self.db_session = None
    
    async def get_training_data(self, days_back: int = 60, min_examples: int = 20) -> List[Dict[str, Any]]:
        """
        Main entry point for VERL training data collection
        
        Returns structured training examples with:
        - Original prompts (reconstructed)
        - Generated content
        - User feedback (approval/rejection)
        - Quality scores
        - Sales performance (if available)
        - Calculated rewards
        """
        
        db = next(get_db())
        try:
            # Get cutoff date
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            # Query books with sufficient feedback
            books_with_feedback = await self._get_books_with_feedback(db, cutoff_date)
            
            if len(books_with_feedback) < min_examples:
                raise ValueError(f"Insufficient training data: {len(books_with_feedback)} examples (need {min_examples})")
            
            # Process each book into training examples
            training_examples = []
            
            for book_data in books_with_feedback:
                examples = await self._process_book_to_training_examples(book_data)
                training_examples.extend(examples)
            
            return training_examples
            
        finally:
            db.close()
    
    async def _get_books_with_feedback(self, db: Session, cutoff_date: datetime) -> List[Dict[str, Any]]:
        """Get books that have sufficient feedback for training"""
        
        # Query books with performance feedback
        query = db.query(Book).join(ModelPerformance).filter(
            and_(
                Book.created_at >= cutoff_date,
                Book.status.in_(['approved', 'rejected', 'published']),
                ModelPerformance.reward_signal.isnot(None)
            )
        ).options(
            joinedload(Book.chapters),
            joinedload(Book.publications),
            joinedload(Book.sales_data)
        ).order_by(desc(Book.created_at))
        
        books = query.all()
        
        # Convert to dict format with all related data
        books_data = []
        for book in books:
            book_data = await self._extract_book_data(db, book)
            books_data.append(book_data)
        
        return books_data
    
    async def _extract_book_data(self, db: Session, book: Book) -> Dict[str, Any]:
        """Extract comprehensive data for a single book"""
        
        # Get performance feedback
        performance = db.query(ModelPerformance).filter(
            ModelPerformance.book_id == book.id
        ).first()
        
        # Get sales data (if available)
        latest_sales = db.query(SalesData).filter(
            SalesData.book_id == book.id
        ).order_by(desc(SalesData.report_date)).first()
        
        # Get individual feedback metrics
        feedback_metrics = db.query(FeedbackMetric).filter(
            FeedbackMetric.book_id == book.id
        ).all()
        
        return {
            'book': book,
            'performance': performance,
            'sales_data': latest_sales,
            'feedback_metrics': feedback_metrics,
            'chapters': book.chapters,
            'generation_config': book.generation_config or {}
        }
    
    async def _process_book_to_training_examples(self, book_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Convert book data to training examples"""
        
        book = book_data['book']
        performance = book_data['performance']
        sales_data = book_data['sales_data']
        
        training_examples = []
        
        # Create training example for each chapter
        for chapter in book_data['chapters']:
            # Reconstruct the original prompt
            prompt = self._reconstruct_generation_prompt(book, chapter)
            
            # Calculate comprehensive reward
            reward = self._calculate_training_reward(performance, sales_data, chapter)
            
            # Create training example
            example = {
                'book_id': book.id,
                'chapter_id': chapter.id,
                'prompt': prompt,
                'generated_content': chapter.content,
                'reward': reward,
                'metadata': {
                    'category': book.category,
                    'word_count': chapter.word_count,
                    'user_approval': performance.user_approval if performance else False,
                    'quality_score': performance.quality_score if performance else 50,
                    'sales_units': sales_data.sales_units if sales_data else 0,
                    'average_rating': sales_data.average_rating if sales_data else 0,
                    'generation_config': book_data['generation_config'],
                    'created_at': book.created_at.isoformat()
                }
            }
            
            training_examples.append(example)
        
        return training_examples
    
    def _reconstruct_generation_prompt(self, book: Book, chapter) -> str:
        """Reconstruct the original prompt used for generation"""
        
        # This reconstructs the likely prompt based on our manuscript generator
        prompt = f"""Write a compelling chapter for an e-book with the following specifications:

Title: "{book.title}"
Category: {book.category}
Chapter: {chapter.title}
Target Audience: general adults
Writing Style: professional

Requirements:
- Provide practical, actionable content that delivers real value to readers
- Use engaging, professional writing style
- Include specific examples, tips, and insights
- Target approximately {chapter.word_count} words

Write the complete chapter content:
"""
        return prompt.strip()
    
    def _calculate_training_reward(
        self,
        performance: ModelPerformance,
        sales_data: Optional[SalesData],
        chapter
    ) -> float:
        """Calculate reward signal for training"""
        
        if performance and performance.reward_signal is not None:
            # Use pre-calculated reward if available
            return float(performance.reward_signal)
        
        # Calculate reward from available data
        reward = 0.0
        
        # User approval (primary signal)
        if performance:
            reward += 1.0 if performance.user_approval else -0.5
            
            # Quality score component
            quality_norm = (performance.quality_score - 50) / 50  # Normalize to [-1, 1]
            reward += quality_norm * 0.5
        
        # Sales performance (if available)
        if sales_data:
            # Normalize sales units (assume 50 units = good performance)
            sales_norm = min(sales_data.sales_units / 50, 1.0)
            reward += sales_norm * 0.3
            
            # Review rating component
            if sales_data.average_rating > 0:
                rating_norm = (sales_data.average_rating - 3.0) / 2.0  # 1-5 scale to [-1, 1]
                reward += rating_norm * 0.2
        
        # Length quality (penalize too short or too long content)
        if chapter.word_count:
            if 800 <= chapter.word_count <= 1500:  # Ideal range
                reward += 0.1
            elif chapter.word_count < 500:  # Too short
                reward -= 0.2
            elif chapter.word_count > 2500:  # Too long
                reward -= 0.1
        
        # Squash to [-1, 1] range
        import math
        return math.tanh(reward)

    async def get_recent_feedback_stats(self, days: int = 30) -> Dict[str, Any]:
        """Get recent feedback statistics for monitoring"""
        
        db = next(get_db())
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Query recent feedback
            recent_performance = db.query(ModelPerformance).filter(
                ModelPerformance.created_at >= cutoff_date
            ).all()
            
            if not recent_performance:
                return {"error": "No recent feedback data"}
            
            # Calculate statistics
            total_books = len(recent_performance)
            approved_books = sum(1 for p in recent_performance if p.user_approval)
            avg_quality = sum(p.quality_score for p in recent_performance) / total_books
            avg_reward = sum(p.reward_signal or 0 for p in recent_performance) / total_books
            
            return {
                'total_books': total_books,
                'approval_rate': approved_books / total_books,
                'average_quality': avg_quality,
                'average_reward': avg_reward,
                'data_points': total_books
            }
            
        finally:
            db.close()
    
    async def get_category_performance(self, days: int = 60) -> Dict[str, Dict[str, float]]:
        """Get performance breakdown by category"""
        
        db = next(get_db())
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Query performance by category
            category_stats = {}
            
            books_with_performance = db.query(Book, ModelPerformance).join(
                ModelPerformance
            ).filter(
                Book.created_at >= cutoff_date
            ).all()
            
            for book, performance in books_with_performance:
                category = book.category
                
                if category not in category_stats:
                    category_stats[category] = {
                        'approval_count': 0,
                        'total_count': 0,
                        'quality_scores': [],
                        'reward_signals': []
                    }
                
                stats = category_stats[category]
                stats['total_count'] += 1
                
                if performance.user_approval:
                    stats['approval_count'] += 1
                
                stats['quality_scores'].append(performance.quality_score)
                if performance.reward_signal is not None:
                    stats['reward_signals'].append(performance.reward_signal)
            
            # Calculate final statistics
            result = {}
            for category, stats in category_stats.items():
                if stats['total_count'] >= 3:  # Minimum sample size
                    result[category] = {
                        'approval_rate': stats['approval_count'] / stats['total_count'],
                        'average_quality': sum(stats['quality_scores']) / len(stats['quality_scores']),
                        'average_reward': sum(stats['reward_signals']) / len(stats['reward_signals']) if stats['reward_signals'] else 0,
                        'sample_size': stats['total_count']
                    }
            
            return result
            
        finally:
            db.close()