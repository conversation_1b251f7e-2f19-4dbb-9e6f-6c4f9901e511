### app/ml/feedback_integration.py - Live Feedback Integration

from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import asyncio

from app.database import get_db_context
from app.models.feedback import FeedbackMetric, ModelPerformance

class LiveFeedbackCollector:
    """Captures feedback in real-time as users interact with the system"""
    
    async def capture_approval_feedback(
        self,
        book_id: int,
        approved: bool,
        approval_time_seconds: float,
        rejection_reason: Optional[str] = None
    ):
        """Capture user approval/rejection immediately"""
        
        with get_db_context() as db:
            # Get the book to fill in generation config
            from app.models.book import Book
            book = db.query(Book).filter(Book.id == book_id).first()
            generation_config = book.generation_config if book else {}
            
            # Store the feedback
            feedback = ModelPerformance(
                book_id=book_id,
                user_approval=approved,
                quality_score=85 if approved else 35,  # Initial estimate
                reward_signal=1.0 if approved else -0.5,
                generation_config=generation_config,
                created_at=datetime.now()
            )
            
            db.add(feedback)
            
            # Also store as individual metric
            metric = FeedbackMetric(
                book_id=book_id,
                metric_type="user_approval",
                value=1.0 if approved else 0.0,
                timestamp=datetime.now(),
                context={
                    'approval_time': approval_time_seconds,
                    'rejection_reason': rejection_reason
                }
            )
            
            db.add(metric)
            
            # Trigger training if we have enough new data
            await self._check_training_trigger()
    
    async def capture_sales_feedback(self, book_id: int, sales_data: Dict[str, Any]):
        """Capture sales performance data"""
        
        with get_db_context() as db:
            # Update existing performance record with sales data
            performance = db.query(ModelPerformance).filter(
                ModelPerformance.book_id == book_id
            ).first()
            
            if performance:
                # Calculate sales-based reward component
                sales_reward = min(sales_data.get('sales_units', 0) / 50, 1.0)
                rating_reward = (sales_data.get('average_rating', 3.0) - 3.0) / 2.0
                
                # Update performance record
                performance.sales_performance = sales_reward * 100
                performance.review_rating = sales_data.get('average_rating', 0)
                
                # Recalculate total reward
                total_reward = 0.0
                total_reward += 1.0 if performance.user_approval else -0.5
                total_reward += (performance.quality_score - 50) / 50
                total_reward += sales_reward * 0.5
                total_reward += rating_reward * 0.3
                
                performance.reward_signal = total_reward
    
    async def _check_training_trigger(self):
        """Check if we should trigger new training"""
        
        with get_db_context() as db:
            # Count new feedback since last training
            recent_feedback = db.query(ModelPerformance).filter(
                ModelPerformance.created_at >= datetime.now() - timedelta(days=1)
            ).count()
            
            # Trigger training if we have enough new data
            if recent_feedback >= 10:  # Threshold for retraining
                try:
                    from app.tasks.verl_training import train_verl_model_task
                    train_verl_model_task.delay(days_back=30)
                except ImportError:
                    # Task system not yet implemented
                    pass