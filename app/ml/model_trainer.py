### app/ml/model_trainer.py - Model Trainer 
import asyncio
import numpy as np
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass

import openai
import anthropic
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, Trainer, TrainingArguments
import torch
from torch.utils.data import Dataset

from app.config import settings
from app.ml.feedback_collector import FeedbackCollector, MetricType, ModelFeedback
from app.models.book import Book
from app.models.feedback import ModelPerformance
from app.database import get_db

logger = logging.getLogger(__name__)

@dataclass
class TrainingExample:
    """Single training example for model improvement"""
    prompt: str
    generation_config: Dict[str, Any]
    output: str
    reward: float
    context: Dict[str, Any]

class ReinforcementTrainer:
    """Reinforcement learning trainer for improving e-book generation"""
    
    def __init__(self):
        self.feedback_collector = FeedbackCollector()
        self.training_examples: List[TrainingExample] = []
        
        # Model performance tracking
        self.baseline_performance = {}
        self.current_performance = {}
        
    async def collect_training_data(self, days_back: int = 30) -> List[TrainingExample]:
        """Collect training data from feedback over specified period"""
        
        db = next(get_db())
        try:
            start_date = datetime.now() - timedelta(days=days_back)
            
            # Get all feedback with sufficient data
            feedback_records = db.query(ModelPerformance).filter(
                ModelPerformance.created_at >= start_date,
                ModelPerformance.reward_signal.isnot(None)
            ).all()
            
            training_examples = []
            
            for feedback in feedback_records:
                # Get the book content for this feedback
                book = db.query(Book).filter(Book.id == feedback.book_id).first()
                if not book:
                    continue
                
                # Create training example
                example = await self._create_training_example(book, feedback)
                if example:
                    training_examples.append(example)
            
            logger.info(f"Collected {len(training_examples)} training examples")
            return training_examples
            
        finally:
            db.close()
    
    async def _create_training_example(
        self,
        book: Book,
        feedback: ModelPerformance
    ) -> Optional[TrainingExample]:
        """Create a training example from book and feedback"""
        
        try:
            # Reconstruct the prompt that was likely used
            prompt = self._reconstruct_generation_prompt(book)
            
            # Get the generated content (first chapter as example)
            output = ""
            if book.chapters:
                output = book.chapters[0].content[:1000]  # First 1000 chars
            
            return TrainingExample(
                prompt=prompt,
                generation_config=feedback.generation_config,
                output=output,
                reward=feedback.reward_signal,
                context={
                    "category": book.category,
                    "word_count": book.word_count,
                    "user_approval": feedback.user_approval,
                    "quality_score": feedback.quality_score
                }
            )
            
        except Exception as e:
            logger.error(f"Error creating training example: {str(e)}")
            return None
    
    def _reconstruct_generation_prompt(self, book: Book) -> str:
        """Reconstruct the prompt used for generation"""
        
        # This reconstructs the likely prompt based on book metadata
        base_prompt = f"""
        Write a compelling chapter for an e-book with the following specifications:
        
        Title: "{book.title}"
        Category: {book.category}
        Target Audience: general adults
        Writing Style: professional
        
        The chapter should provide practical, actionable content that delivers real value to readers.
        """
        
        return base_prompt.strip()
    
    async def train_reward_model(self, training_examples: List[TrainingExample]):
        """Train a reward model to predict feedback quality"""
        
        # Prepare training data
        X = []  # Features: prompt, config, output characteristics
        y = []  # Labels: reward signals
        
        for example in training_examples:
            features = self._extract_features(example)
            X.append(features)
            y.append(example.reward)
        
        if len(X) < 10:  # Need minimum data
            logger.warning("Insufficient training data for reward model")
            return
        
        # Train a simple neural network to predict rewards
        reward_model = self._train_reward_predictor(X, y)
        
        # Save the model
        await self._save_reward_model(reward_model)
        
        logger.info(f"Trained reward model on {len(X)} examples")
    
    def _extract_features(self, example: TrainingExample) -> List[float]:
        """Extract numerical features from training example"""
        
        features = []
        
        # Text-based features
        features.append(len(example.prompt))
        features.append(len(example.output))
        features.append(len(example.output.split()))  # Word count
        
        # Content quality indicators
        features.append(example.output.count('.'))  # Sentence count proxy
        features.append(example.output.count('!'))  # Excitement
        features.append(example.output.count('?'))  # Questions
        
        # Configuration features
        config = example.generation_config
        features.append(1.0 if config.get('provider') == 'openai' else 0.0)
        features.append(config.get('temperature', 0.7))
        features.append(config.get('max_tokens', 1000) / 1000)  # Normalized
        
        # Context features
        context = example.context
        features.append(context.get('word_count', 0) / 10000)  # Normalized
        features.append(context.get('quality_score', 50) / 100)  # Normalized
        
        # Category encoding (simple)
        categories = ['self-help', 'business', 'health', 'romance', 'mystery']
        category_encoding = [1.0 if context.get('category') == cat else 0.0 for cat in categories]
        features.extend(category_encoding)
        
        return features
    
    def _train_reward_predictor(self, X: List[List[float]], y: List[float]):
        """Train a simple neural network to predict rewards"""
        
        # Convert to numpy arrays
        X = np.array(X)
        y = np.array(y)
        
        # Simple linear regression for now (could upgrade to neural network)
        from sklearn.linear_model import LinearRegression
        from sklearn.metrics import mean_squared_error
        
        model = LinearRegression()
        model.fit(X, y)
        
        # Evaluate
        predictions = model.predict(X)
        mse = mean_squared_error(y, predictions)
        logger.info(f"Reward model MSE: {mse:.4f}")
        
        return model
    
    async def optimize_generation_parameters(self) -> Dict[str, Any]:
        """Use feedback to optimize generation parameters"""
        
        # Analyze which parameters lead to better outcomes
        training_examples = await self.collect_training_data(days_back=60)
        
        if len(training_examples) < 20:
            logger.warning("Insufficient data for parameter optimization")
            return {}
        
        # Group by configuration parameters
        parameter_performance = {}
        
        for example in training_examples:
            config = example.generation_config
            provider = config.get('provider', 'openai')
            temperature = config.get('temperature', 0.7)
            
            key = f"{provider}_{temperature}"
            if key not in parameter_performance:
                parameter_performance[key] = []
            parameter_performance[key].append(example.reward)
        
        # Find best performing configurations
        best_configs = {}
        for key, rewards in parameter_performance.items():
            if len(rewards) >= 5:  # Minimum sample size
                avg_reward = np.mean(rewards)
                best_configs[key] = {
                    'average_reward': avg_reward,
                    'sample_count': len(rewards),
                    'std': np.std(rewards)
                }
        
        # Select optimal parameters
        if best_configs:
            best_key = max(best_configs.keys(), key=lambda k: best_configs[k]['average_reward'])
            provider, temperature = best_key.split('_')
            
            optimal_config = {
                'provider': provider,
                'temperature': float(temperature),
                'confidence': best_configs[best_key]['average_reward']
            }
            
            logger.info(f"Optimal config: {optimal_config}")
            return optimal_config
        
        return {}
    
    async def generate_improvement_suggestions(self) -> List[Dict[str, Any]]:
        """Generate specific suggestions for improving the system"""
        
        suggestions = []
        
        # Analyze recent performance trends
        approval_trends = await self.feedback_collector.get_performance_trends(
            MetricType.USER_APPROVAL, days=30
        )
        
        quality_trends = await self.feedback_collector.get_performance_trends(
            MetricType.QUALITY_SCORE, days=30
        )
        
        # Calculate trend direction
        if len(approval_trends) >= 10:
            recent_approval = np.mean([t['value'] for t in approval_trends[-5:]])
            older_approval = np.mean([t['value'] for t in approval_trends[:5]])
            
            if recent_approval < older_approval:
                suggestions.append({
                    'type': 'approval_decline',
                    'priority': 'high',
                    'description': 'User approval rate is declining',
                    'recommendation': 'Review recent generation parameters and content quality',
                    'metric_change': recent_approval - older_approval
                })
        
        # Category-specific analysis
        category_performance = await self._analyze_category_performance()
        
        for category, performance in category_performance.items():
            if performance['avg_reward'] < 0.2:
                suggestions.append({
                    'type': 'category_underperform',
                    'priority': 'medium',
                    'description': f'Category "{category}" is underperforming',
                    'recommendation': f'Consider adjusting prompts or parameters for {category} books',
                    'category': category,
                    'performance': performance
                })
        
        # Generation parameter suggestions
        optimal_params = await self.optimize_generation_parameters()
        if optimal_params:
            suggestions.append({
                'type': 'parameter_optimization',
                'priority': 'medium',
                'description': 'Optimal generation parameters identified',
                'recommendation': f"Use {optimal_params['provider']} with temperature {optimal_params['temperature']}",
                'optimal_config': optimal_params
            })
        
        return suggestions
    
    async def _analyze_category_performance(self) -> Dict[str, Dict[str, float]]:
        """Analyze performance by book category"""
        
        db = next(get_db())
        try:
            feedback_records = db.query(ModelPerformance).filter(
                ModelPerformance.created_at >= datetime.now() - timedelta(days=60)
            ).all()
            
            category_stats = {}
            
            for feedback in feedback_records:
                book = db.query(Book).filter(Book.id == feedback.book_id).first()
                if not book:
                    continue
                
                category = book.category
                if category not in category_stats:
                    category_stats[category] = []
                
                category_stats[category].append(feedback.reward_signal)
            
            # Calculate statistics
            result = {}
            for category, rewards in category_stats.items():
                if len(rewards) >= 3:  # Minimum sample size
                    result[category] = {
                        'avg_reward': np.mean(rewards),
                        'std_reward': np.std(rewards),
                        'sample_count': len(rewards)
                    }
            
            return result
            
        finally:
            db.close()
    
    async def auto_tune_system(self):
        """Automatically tune system parameters based on feedback"""
        
        logger.info("Starting automatic system tuning...")
        
        # Collect recent training data
        training_examples = await self.collect_training_data(days_back=30)
        
        if len(training_examples) < 10:
            logger.warning("Insufficient data for auto-tuning")
            return
        
        # Train reward model
        await self.train_reward_model(training_examples)
        
        # Optimize parameters
        optimal_config = await self.optimize_generation_parameters()
        
        if optimal_config:
            # Update system configuration
            await self._update_system_config(optimal_config)
        
        # Generate and log improvement suggestions
        suggestions = await self.generate_improvement_suggestions()
        
        if suggestions:
            logger.info(f"Generated {len(suggestions)} improvement suggestions")
            for suggestion in suggestions[:3]:  # Log top 3
                logger.info(f"Suggestion: {suggestion['description']} - {suggestion['recommendation']}")
        
        logger.info("Auto-tuning completed")
    
    async def _update_system_config(self, optimal_config: Dict[str, Any]):
        """Update system configuration with optimal parameters"""
        
        # This would update your system's default configuration
        # You could store this in database, config file, or environment variables
        
        config_updates = {
            'default_ai_provider': optimal_config.get('provider'),
            'default_temperature': optimal_config.get('temperature'),
            'updated_at': datetime.now().isoformat()
        }
        
        # Save to configuration storage
        # For example, update settings or database configuration
        logger.info(f"Updated system config: {config_updates}")
    
    async def _save_reward_model(self, model):
        """Save trained reward model"""
        
        import joblib
        from pathlib import Path
        
        model_path = Path("storage/models")
        model_path.mkdir(parents=True, exist_ok=True)
        
        filename = f"reward_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        joblib.dump(model, model_path / filename)
        
        logger.info(f"Saved reward model: {filename}")
