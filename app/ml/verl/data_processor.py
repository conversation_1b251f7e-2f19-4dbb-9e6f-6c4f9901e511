### app/ml/verl/data_processor.py - VERL Data Processor`

import torch
from transformers import AutoTokenizer
from datasets import Dataset
from typing import List, Dict, Any
import logging

from app.ml.data_access import VERLDataAccessor

logger = logging.getLogger(__name__)

class VERLDataProcessor:
    """Processes raw data for VERL training"""
    
    def __init__(self, config):
        self.config = config
        self.tokenizer = AutoTokenizer.from_pretrained(config.model_name)
        self.data_accessor = VERLDataAccessor()
        
        # Ensure tokenizer has pad token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    async def prepare_training_dataset(self, days_back: int = 60) -> Dataset:
        """Main method to prepare training dataset"""
        
        logger.info(f"Collecting training data from last {days_back} days...")
        
        # Get raw training data
        raw_examples = await self.data_accessor.get_training_data(days_back)
        
        logger.info(f"Processing {len(raw_examples)} training examples...")
        
        # Process examples for VERL
        processed_examples = []
        
        for example in raw_examples:
            processed = self._process_single_example(example)
            if processed:
                processed_examples.append(processed)
        
        if len(processed_examples) < 10:
            raise ValueError(f"Insufficient processed examples: {len(processed_examples)}")
        
        # Create HuggingFace dataset
        dataset = Dataset.from_list(processed_examples)
        
        # Add tokenization
        dataset = dataset.map(
            self._tokenize_example,
            batched=True,
            remove_columns=['prompt', 'generated_content']  # Keep metadata
        )
        
        logger.info(f"Created dataset with {len(dataset)} examples")
        return dataset
    
    def _process_single_example(self, example: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single training example"""
        
        try:
            prompt = example['prompt']
            content = example['generated_content']
            reward = example['reward']
            metadata = example['metadata']
            
            # Quality checks
            if len(prompt) < 50 or len(content) < 200:
                return None  # Skip too short examples
            
            if abs(reward) < 0.01:  # Skip examples with no clear signal
                return None
            
            # Create training text (prompt + response)
            full_text = prompt + "\n\n" + content
            
            return {
                'text': full_text,
                'prompt': prompt,
                'generated_content': content,
                'reward': float(reward),
                'book_id': example['book_id'],
                'category': metadata.get('category', 'unknown'),
                'quality_score': metadata.get('quality_score', 50),
                'user_approval': metadata.get('user_approval', False),
                'word_count': metadata.get('word_count', 0)
            }
            
        except Exception as e:
            logger.warning(f"Failed to process example: {str(e)}")
            return None
    
    def _tokenize_example(self, examples):
        """Tokenize examples for training"""
        
        # Tokenize the full text
        tokenized = self.tokenizer(
            examples['text'],
            truncation=True,
            padding='max_length',
            max_length=self.config.max_seq_length,
            return_tensors='pt'
        )
        
        # Add rewards as labels
        tokenized['rewards'] = examples['reward']
        tokenized['metadata'] = examples
        
        return tokenized
    
    async def get_data_statistics(self) -> Dict[str, Any]:
        """Get statistics about available training data"""
        
        # Get recent stats
        stats = await self.data_accessor.get_recent_feedback_stats(days=30)
        category_stats = await self.data_accessor.get_category_performance(days=60)
        
        return {
            'recent_feedback': stats,
            'category_performance': category_stats,
            'data_quality': 'good' if stats.get('total_books', 0) > 20 else 'limited'
        }