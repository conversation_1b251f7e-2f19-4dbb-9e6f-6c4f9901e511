### app/ml/verl_trainer.py - VERL Trainer

import asyncio
import torch
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
import logging
from dataclasses import dataclass
from pathlib import Path

# VERL imports
from verl import PPO, GRPO, DataCollator
from verl.trainer import PPOTrainer
from verl.models import ActorModel, CriticModel, RewardModel
from verl.utils import init_distributed
from verl.protocol import RolloutRequest, RolloutResponse

from app.config import settings
from app.ml.feedback_collector import FeedbackCollector, ModelFeedback
from app.models.book import Book
from app.schemas.book import Manuscript

logger = logging.getLogger(__name__)

@dataclass
class EbookRLConfig:
    """Configuration for e-book generation RL training"""
    model_name: str = "microsoft/DialoGPT-medium"  # Base model
    max_length: int = 2048
    batch_size: int = 8
    learning_rate: float = 1e-5
    num_epochs: int = 3
    ppo_epochs: int = 4
    clip_ratio: float = 0.2
    value_clip: float = 0.2
    entropy_coef: float = 0.01
    value_coef: float = 0.5
    max_grad_norm: float = 1.0
    
    # E-book specific settings
    reward_categories: List[str] = None
    quality_weight: float = 0.4
    sales_weight: float = 0.3
    approval_weight: float = 0.3

class VERLEbookTrainer:
    """VERL-powered trainer for e-book generation models"""
    
    def __init__(self, config: EbookRLConfig):
        self.config = config
        self.feedback_collector = FeedbackCollector()
        
        # Initialize VERL components
        self.actor_model = None
        self.critic_model = None
        self.reward_model = None
        self.trainer = None
        
        # Training data buffers
        self.training_buffer = []
        self.reward_buffer = []
    
    async def initialize_models(self):
        """Initialize VERL actor, critic, and reward models"""
        
        try:
            # Initialize distributed training if available
            if torch.cuda.device_count() > 1:
                init_distributed()
            
            # Actor model (generates e-book content)
            self.actor_model = ActorModel(
                model_name=self.config.model_name,
                max_length=self.config.max_length
            )
            
            # Critic model (estimates value of states)
            self.critic_model = CriticModel(
                model_name=self.config.model_name
            )
            
            # Reward model (trained on our feedback data)
            self.reward_model = await self._initialize_reward_model()
            
            # PPO trainer
            self.trainer = PPOTrainer(
                actor_model=self.actor_model,
                critic_model=self.critic_model,
                reward_model=self.reward_model,
                config=self._get_ppo_config()
            )
            
            logger.info("VERL models initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize VERL models: {str(e)}")
            raise
    
    async def _initialize_reward_model(self) -> RewardModel:
        """Initialize and train reward model from our feedback data"""
        
        # Get training data from our feedback system
        training_data = await self._prepare_reward_training_data()
        
        if len(training_data) < 100:
            logger.warning("Insufficient data for reward model training, using base model")
            return RewardModel(model_name=self.config.model_name)
        
        # Train reward model on our specific e-book metrics
        reward_model = RewardModel(model_name=self.config.model_name)
        
        # Custom training loop for e-book specific rewards
        await self._train_reward_model(reward_model, training_data)
        
        return reward_model
    
    async def _prepare_reward_training_data(self) -> List[Dict[str, Any]]:
        """Prepare training data for reward model from our feedback"""
        
        # Get feedback data from the last 90 days
        training_examples = await self.feedback_collector.collect_training_data(days_back=90)
        
        formatted_data = []
        
        for example in training_examples:
            # Convert our training examples to VERL format
            formatted_data.append({
                'prompt': example.prompt,
                'response': example.output,
                'reward': example.reward,
                'metadata': {
                    'category': example.context.get('category'),
                    'word_count': example.context.get('word_count'),
                    'user_approval': example.context.get('user_approval'),
                    'quality_score': example.context.get('quality_score')
                }
            })
        
        logger.info(f"Prepared {len(formatted_data)} examples for reward model training")
        return formatted_data
    
    async def _train_reward_model(self, reward_model: RewardModel, training_data: List[Dict]):
        """Train the reward model on e-book specific metrics"""
        
        # Convert our training data to VERL format
        dataset = self._create_reward_dataset(training_data)
        
        # Train reward model
        reward_trainer = reward_model.get_trainer(
            learning_rate=self.config.learning_rate,
            batch_size=self.config.batch_size,
            num_epochs=2
        )
        
        await reward_trainer.train(dataset)
        logger.info("Reward model training completed")
    
    def _create_reward_dataset(self, training_data: List[Dict]) -> Any:
        """Create VERL-compatible dataset from training data"""
        
        prompts = []
        responses = []
        rewards = []
        
        for example in training_data:
            prompts.append(example['prompt'])
            responses.append(example['response'])
            rewards.append(example['reward'])
        
        # Use VERL's DataCollator
        collator = DataCollator(
            tokenizer=self.actor_model.tokenizer,
            max_length=self.config.max_length
        )
        
        return collator.create_dataset(prompts, responses, rewards)
    
    async def train_on_feedback(self, manuscript_data: List[Dict[str, Any]]):
        """Train the model using VERL PPO on manuscript feedback"""
        
        if not self.trainer:
            await self.initialize_models()
        
        # Prepare training episodes from manuscript data
        episodes = await self._prepare_training_episodes(manuscript_data)
        
        if len(episodes) < 10:
            logger.warning("Insufficient episodes for training")
            return
        
        try:
            # Run PPO training with VERL
            training_stats = await self.trainer.train(
                episodes=episodes,
                num_epochs=self.config.ppo_epochs,
                batch_size=self.config.batch_size
            )
            
            # Log training results
            logger.info(f"PPO training completed: {training_stats}")
            
            # Save the updated model
            await self._save_trained_model()
            
            return training_stats
            
        except Exception as e:
            logger.error(f"VERL training failed: {str(e)}")
            raise
    
    async def _prepare_training_episodes(self, manuscript_data: List[Dict]) -> List[RolloutRequest]:
        """Prepare training episodes for VERL"""
        
        episodes = []
        
        for data in manuscript_data:
            manuscript = data['manuscript']
            feedback = data['feedback']
            
            # Create rollout request for each chapter
            for chapter in manuscript.get('chapters', []):
                prompt = self._create_chapter_prompt(manuscript, chapter)
                
                episode = RolloutRequest(
                    prompt=prompt,
                    max_length=self.config.max_length,
                    temperature=0.7,
                    top_p=0.9
                )
                
                # Add our custom reward signal
                episode.expected_reward = self._calculate_episode_reward(feedback)
                episode.metadata = {
                    'category': manuscript.get('category'),
                    'book_id': data.get('book_id'),
                    'chapter_id': chapter.get('chapter_number')
                }
                
                episodes.append(episode)
        
        return episodes
    
    def _create_chapter_prompt(self, manuscript: Dict, chapter: Dict) -> str:
        """Create optimized prompts for chapter generation"""
        
        return f"""
Write a compelling chapter for an e-book with the following specifications:

Title: "{manuscript.get('title', 'Unknown')}"
Category: {manuscript.get('category', 'general')}
Chapter: {chapter.get('title', 'Chapter')}

Requirements:
- Provide practical, actionable content
- Use engaging, professional writing style
- Target approximately {manuscript.get('target_word_count', 1000) // len(manuscript.get('chapters', [1]))} words
- Include specific examples and tips

Chapter content:
"""
    
    def _calculate_episode_reward(self, feedback: Dict) -> float:
        """Calculate reward for training episode"""
        
        reward = 0.0
        
        # User approval (primary signal)
        if feedback.get('user_approval'):
            reward += 1.0
        else:
            reward -= 0.5
        
        # Quality score
        quality = feedback.get('quality_score', 50) / 100
        reward += (quality - 0.5) * 2  # Scale to [-1, 1]
        
        # Sales performance (if available)
        sales = feedback.get('sales_performance', 0) / 100
        reward += min(sales, 1.0) * 0.5
        
        # Review rating (if available)
        rating = feedback.get('review_rating', 3.0)
        reward += (rating - 3.0) / 2.0  # Scale 1-5 to approximately [-1, 1]
        
        return np.tanh(reward)  # Squash to [-1, 1]
    
    async def generate_optimized_content(
        self,
        prompt: str,
        category: str,
        target_length: int = 1000
    ) -> str:
        """Generate content using the trained VERL model"""
        
        if not self.actor_model:
            await self.initialize_models()
        
        # Create rollout request
        request = RolloutRequest(
            prompt=prompt,
            max_length=min(target_length * 2, self.config.max_length),
            temperature=0.7,
            top_p=0.9
        )
        
        # Generate content
        response = await self.actor_model.generate(request)
        
        # Post-process the generated content
        content = self._post_process_content(response.text, target_length)
        
        return content
    
    def _post_process_content(self, content: str, target_length: int) -> str:
        """Post-process generated content for quality"""
        
        # Remove any incomplete sentences at the end
        sentences = content.split('.')
        complete_content = '.'.join(sentences[:-1]) + '.'
        
        # Ensure minimum length
        words = complete_content.split()
        if len(words) < target_length * 0.8:  # At least 80% of target
            # Content too short, might need regeneration
            logger.warning(f"Generated content too short: {len(words)} words")
        
        return complete_content.strip()
    
    async def evaluate_model_performance(self) -> Dict[str, float]:
        """Evaluate current model performance"""
        
        # Get recent feedback data
        recent_feedback = await self.feedback_collector.get_performance_trends(
            metric_type="user_approval", days=30
        )
        
        if not recent_feedback:
            return {"error": "No recent feedback data available"}
        
        # Calculate performance metrics
        approval_rates = [f['value'] for f in recent_feedback]
        avg_approval = np.mean(approval_rates)
        approval_trend = np.polyfit(range(len(approval_rates)), approval_rates, 1)[0]
        
        # Get quality metrics
        quality_feedback = await self.feedback_collector.get_performance_trends(
            metric_type="quality_score", days=30
        )
        
        quality_scores = [f['value'] for f in quality_feedback] if quality_feedback else [50]
        avg_quality = np.mean(quality_scores)
        
        return {
            "approval_rate": avg_approval,
            "approval_trend": approval_trend,
            "average_quality": avg_quality,
            "sample_size": len(approval_rates),
            "performance_score": (avg_approval + avg_quality/100) / 2
        }
    
    async def _save_trained_model(self):
        """Save the trained VERL model"""
        
        model_dir = Path("storage/verl_models")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = model_dir / f"ebook_model_{timestamp}"
        
        # Save VERL model components
        await self.actor_model.save(save_path / "actor")
        await self.critic_model.save(save_path / "critic")
        await self.reward_model.save(save_path / "reward")
        
        # Save configuration
        config_path = save_path / "config.json"
        with open(config_path, 'w') as f:
            json.dump(self.config.__dict__, f, indent=2)
        
        logger.info(f"VERL model saved to {save_path}")
    
    def _get_ppo_config(self) -> Dict[str, Any]:
        """Get PPO configuration for VERL"""
        
        return {
            "learning_rate": self.config.learning_rate,
            "batch_size": self.config.batch_size,
            "ppo_epochs": self.config.ppo_epochs,
            "clip_ratio": self.config.clip_ratio,
            "value_clip": self.config.value_clip,
            "entropy_coef": self.config.entropy_coef,
            "value_coef": self.config.value_coef,
            "max_grad_norm": self.config.max_grad_norm,
            "discount_factor": 0.99,
            "gae_lambda": 0.95
        }

# Integration with existing manuscript generator
class VERLEnhancedManuscriptGenerator:
    """Enhanced manuscript generator using VERL"""
    
    def __init__(self):
        self.verl_trainer = VERLEbookTrainer(EbookRLConfig())
        self.traditional_generators = {
            'openai': None,  # Your existing OpenAI generator
            'anthropic': None  # Your existing Anthropic generator
        }
    
    async def generate_manuscript(
        self,
        trend_data: Dict[str, Any],
        use_verl: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate manuscript with optional VERL enhancement"""
        
        if use_verl and await self._should_use_verl():
            return await self._generate_with_verl(trend_data, **kwargs)
        else:
            return await self._generate_traditional(trend_data, **kwargs)
    
    async def _should_use_verl(self) -> bool:
        """Determine if VERL should be used based on training data availability"""
        
        # Check if we have enough training data
        training_examples = await self.verl_trainer.feedback_collector.collect_training_data(days_back=30)
        
        return len(training_examples) >= 50  # Minimum threshold for VERL
    
    async def _generate_with_verl(self, trend_data: Dict, **kwargs) -> Dict[str, Any]:
        """Generate using VERL-trained model"""
        
        opportunity = trend_data['trends'][0]  # Best opportunity
        
        # Create optimized prompt
        prompt = f"""
        Write a compelling e-book chapter for the category: {opportunity['category']}
        Topic: {opportunity['title_suggestion']}
        Keywords: {', '.join(opportunity.get('keywords', []))}
        
        The content should be practical, engaging, and valuable to readers.
        """
        
        # Generate with VERL
        content = await self.verl_trainer.generate_optimized_content(
            prompt=prompt,
            category=opportunity['category'],
            target_length=1500
        )
        
        return {
            'content': content,
            'method': 'verl',
            'confidence': 0.85,  # VERL typically gives higher confidence
            'optimization_used': True
        }
    
    async def _generate_traditional(self, trend_data: Dict, **kwargs) -> Dict[str, Any]:
        """Fallback to traditional generation methods"""
        
        # Use existing OpenAI/Anthropic generators
        # ... your existing generation logic ...
        
        return {
            'content': "Generated with traditional method",
            'method': 'traditional',
            'confidence': 0.70,
            'optimization_used': False
        }