### app/models/book.py - Enhanced Book Model with Pydantic Validation

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Float, JSON, Index, event
from sqlalchemy.orm import relationship, validates
from datetime import datetime, timezone
from app.database import Base
from typing import Dict, Any, List, Optional
import re

class Book(Base):
    __tablename__ = "books"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Book metadata
    title = Column(String(500), nullable=False)
    category = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    keywords = Column(JSON, nullable=True)  # List of keywords
    
    # Content
    manuscript_path = Column(String(500), nullable=True)  # Path to manuscript file
    cover_paths = Column(JSON, nullable=True)  # Dict of cover file paths
    word_count = Column(Integer, default=0)
    chapter_count = Column(Integer, default=0)
    
    # Generation metadata
    source_trend_id = Column(Integer, ForeignKey("trend_analyses.id"), nullable=True, index=True)
    generation_config = Column(JSON, nullable=True)
    
    # Status tracking
    status = Column(String(50), default="draft", index=True)  # draft, generating, awaiting_approval, approved, rejected, published
    quality_score = Column(Float, nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    approved_at = Column(DateTime, nullable=True)
    published_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="books")
    chapters = relationship("Chapter", back_populates="book", cascade="all, delete-orphan")
    
    # Add indexes for performance
    __table_args__ = (
        Index('idx_book_user_id', 'user_id'),
        Index('idx_book_status', 'status'),
        Index('idx_book_source_trend', 'source_trend_id'),
    )
    
    # Pydantic-style validation methods
    @validates('title')
    def validate_title(self, key, title):
        """Validate and clean book title"""
        if not title or not title.strip():
            raise ValueError("Book title cannot be empty")
        
        title = title.strip()
        # Remove excessive whitespace
        title = re.sub(r'\s+', ' ', title)
        # Remove filesystem-unsafe characters
        title = re.sub(r'[<>:"\\|?*]', '', title)
        
        if len(title) > 500:
            raise ValueError("Title too long (max 500 characters)")
        
        return title
    
    @validates('category')
    def validate_category(self, key, category):
        """Validate and normalize category"""
        valid_categories = {
            'self-help', 'business', 'health', 'romance', 'mystery', 
            'fantasy', 'cooking', 'parenting', 'finance', 'productivity',
            'technology', 'science', 'history', 'biography', 'education'
        }
        
        if not category:
            return 'self-help'  # Default category
        
        category = category.lower().strip()
        if category not in valid_categories:
            # Try to find a close match
            for valid_cat in valid_categories:
                if category in valid_cat or valid_cat in category:
                    return valid_cat
            # If no match, default to self-help
            return 'self-help'
        return category
    
    @validates('status')
    def validate_status(self, key, status):
        """Validate status transitions"""
        valid_statuses = {"draft", "generating", "awaiting_approval", "approved", "rejected", "published"}
        if status not in valid_statuses:
            raise ValueError(f"Invalid status: {status}")
        return status
    
    @validates('quality_score')
    def validate_quality_score(self, key, score):
        """Validate quality score range"""
        if score is not None and (score < 0 or score > 100):
            raise ValueError("Quality score must be between 0 and 100")
        return score
    
    @validates('keywords')
    def validate_keywords(self, key, keywords):
        """Validate and clean keywords"""
        if not keywords:
            return None
        
        if isinstance(keywords, str):
            # Handle string input by splitting
            keywords = [k.strip() for k in keywords.split(',')]
        
        cleaned = []
        for keyword in keywords[:20]:  # Limit to 20 keywords
            if isinstance(keyword, str):
                cleaned_keyword = re.sub(r'[^a-zA-Z0-9\s-]', '', keyword.strip().lower())
                if len(cleaned_keyword) >= 2:
                    cleaned.append(cleaned_keyword)
        
        return cleaned if cleaned else None
    
    # Computed properties (Pydantic-style)
    @property
    def total_reading_time(self) -> int:
        """Total estimated reading time in minutes"""
        return max(1, (self.word_count or 0) // 250)
    
    @property
    def is_novel_length(self) -> bool:
        """Whether this is novel length (50k+ words)"""
        return (self.word_count or 0) >= 50000
    
    @property
    def is_novella_length(self) -> bool:
        """Whether this is novella length (17.5k-40k words)"""
        word_count = self.word_count or 0
        return 17500 <= word_count < 40000
    
    @property
    def completion_percentage(self) -> float:
        """Estimate completion percentage based on status"""
        status_percentages = {
            "draft": 10.0,
            "generating": 50.0,
            "awaiting_approval": 80.0,
            "approved": 90.0,
            "rejected": 75.0,
            "published": 100.0
        }
        return status_percentages.get(self.status, 0.0)
    
    @property
    def days_since_creation(self) -> int:
        """Number of days since book creation"""
        if not self.created_at:
            return 0
        return (datetime.now(timezone.utc) - self.created_at).days
    
    def to_pydantic(self) -> Dict[str, Any]:
        """Convert to Pydantic-compatible dict"""
        try:
            from app.schemas.book import BookResponse
            return BookResponse.model_validate(self, from_attributes=True).model_dump()
        except ImportError:
            # Fallback if schemas not available
            return {
                'id': self.id,
                'title': self.title,
                'category': self.category,
                'description': self.description,
                'word_count': self.word_count,
                'status': self.status,
                'created_at': self.created_at.isoformat() if self.created_at else None
            }
    
    @classmethod
    def from_pydantic(cls, book_data, user_id: int):
        """Create from Pydantic model with validation"""
        try:
            from app.schemas.book import BookCreate
            
            # Validate using Pydantic schema
            if not isinstance(book_data, dict):
                book_data = book_data.model_dump()
            
            validated_data = BookCreate.model_validate(book_data)
            
            return cls(
                user_id=user_id,
                title=validated_data.title,
                category=validated_data.category,
                description=validated_data.description,
                target_audience=getattr(validated_data, 'target_audience', 'general adults'),
                writing_style=getattr(validated_data, 'writing_style', 'professional'),
                ai_provider=getattr(validated_data, 'ai_provider', 'openai')
            )
        except ImportError:
            # Fallback if schemas not available
            return cls(
                user_id=user_id,
                title=book_data.get('title', 'Untitled'),
                category=book_data.get('category', 'self-help'),
                description=book_data.get('description')
            )

class Chapter(Base):
    __tablename__ = "chapters"

    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), nullable=False, index=True)
    
    chapter_number = Column(Integer, nullable=False)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    word_count = Column(Integer, default=0)
    
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    
    # Relationships
    book = relationship("Book", back_populates="chapters")
    
    # Pydantic-style validation methods
    @validates('title')
    def validate_title(self, key, title):
        """Validate and clean chapter title"""
        if not title or not title.strip():
            raise ValueError("Chapter title cannot be empty")
        
        title = title.strip()
        title = re.sub(r'\s+', ' ', title)  # Remove excessive whitespace
        
        if len(title) > 500:
            raise ValueError("Chapter title too long (max 500 characters)")
        
        return title
    
    @validates('content')
    def validate_content(self, key, content):
        """Validate and sanitize chapter content"""
        if not content or not content.strip():
            raise ValueError("Chapter content cannot be empty")
        
        content = content.strip()
        
        # Remove potential security threats
        content = re.sub(r'<script.*?</script>', '', content, flags=re.IGNORECASE | re.DOTALL)
        content = re.sub(r'<iframe.*?</iframe>', '', content, flags=re.IGNORECASE | re.DOTALL)
        content = re.sub(r'javascript:', '', content, flags=re.IGNORECASE)
        
        if len(content) < 100:
            raise ValueError("Chapter content too short (minimum 100 characters)")
        if len(content) > 100000:
            raise ValueError("Chapter content too long (maximum 100,000 characters)")
        
        return content
    
    @validates('chapter_number')
    def validate_chapter_number(self, key, number):
        """Validate chapter number"""
        if number is None or number < 1:
            raise ValueError("Chapter number must be positive")
        if number > 200:
            raise ValueError("Chapter number too high (maximum 200)")
        return number
    
    @validates('word_count')
    def validate_word_count(self, key, count):
        """Validate word count"""
        if count is not None and count < 0:
            raise ValueError("Word count cannot be negative")
        return count
    
    # Computed properties
    @property
    def estimated_reading_time(self) -> int:
        """Estimated reading time in minutes (250 words/minute)"""
        return max(1, (self.word_count or 0) // 250)
    
    @property
    def reading_difficulty(self) -> str:
        """Estimate reading difficulty based on content"""
        word_count = self.word_count or 0
        if word_count < 500:
            return "easy"
        elif word_count < 2000:
            return "medium"
        else:
            return "hard"
    
    def to_pydantic(self) -> Dict[str, Any]:
        """Convert to Pydantic-compatible dict"""
        try:
            from app.schemas.book import Chapter as ChapterSchema
            return ChapterSchema.model_validate(self, from_attributes=True).model_dump()
        except ImportError:
            # Fallback if schemas not available
            return {
                'id': self.id,
                'title': self.title,
                'content': self.content,
                'chapter_number': self.chapter_number,
                'word_count': self.word_count,
                'created_at': self.created_at.isoformat() if self.created_at else None
            }