### app/models/feedback.py - Feedback Models

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON, ForeignKey, Index
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from app.database import Base

class FeedbackMetric(Base):
    __tablename__ = "feedback_metrics"

    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), nullable=False, index=True)
    
    # Metric details
    metric_type = Column(String(100), nullable=False, index=True)  # MetricType enum value
    value = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    
    # Additional context
    context = Column(JSON, nullable=True)

class ModelPerformance(Base):
    __tablename__ = "model_performance"

    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), nullable=False, index=True)
    
    # Generation configuration used
    generation_config = Column(JSON, nullable=False)
    
    # Feedback metrics
    user_approval = Column(Boolean, nullable=False)
    quality_score = Column(Float, nullable=False)
    sales_performance = Column(Float, nullable=True)
    review_rating = Column(Float, nullable=True)
    engagement_score = Column(Float, nullable=True)
    
    # Calculated reward signal for RL
    reward_signal = Column(Float, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class SystemConfiguration(Base):
    __tablename__ = "system_configurations"

    id = Column(Integer, primary_key=True, index=True)
    
    # Configuration details
    config_key = Column(String(255), unique=True, nullable=False)
    config_value = Column(JSON, nullable=False)
    
    # Performance tracking
    performance_score = Column(Float, nullable=True)
    active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

class ExperimentResult(Base):
    __tablename__ = "experiment_results"

    id = Column(Integer, primary_key=True, index=True)
    
    # Experiment details
    experiment_name = Column(String(255), nullable=False)
    variant = Column(String(100), nullable=False)  # A/B test variant
    
    # Results
    sample_size = Column(Integer, nullable=False)
    success_rate = Column(Float, nullable=False)
    confidence_interval = Column(JSON, nullable=True)  # [lower, upper]
    
    # Statistical significance
    p_value = Column(Float, nullable=True)
    is_significant = Column(Boolean, default=False)
    
    # Timestamps
    started_at = Column(DateTime, nullable=False)
    completed_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))