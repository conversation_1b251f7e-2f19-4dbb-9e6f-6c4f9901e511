### app/models/publication.py - Publication Model

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Float, JSON, Boolean, Index
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from app.database import Base

class Publication(Base):
    __tablename__ = "publications"

    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), nullable=False, index=True)
    
    # KDP details
    kdp_id = Column(String(255), nullable=True, index=True)  # KDP book ID
    publication_url = Column(String(500), nullable=True)
    
    # Publication settings
    price = Column(Float, default=2.99)
    royalty_rate = Column(Integer, default=70)
    auto_publish = Column(Boolean, default=False)
    marketing_description = Column(Text, nullable=True)
    publication_date = Column(DateTime, nullable=True)
    
    # Status tracking
    status = Column(String(50), default="publishing", index=True)  # publishing, published, failed, draft, unpublished
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    published_at = Column(DateTime, nullable=True)
    
    # Add indexes for performance
    __table_args__ = (
        Index('idx_publication_book', 'book_id'),
        Index('idx_publication_status', 'status'),
        Index('idx_publication_kdp_id', 'kdp_id'),
    )

class SalesData(Base):
    __tablename__ = "sales_data"

    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), nullable=False, index=True)
    
    # Sales metrics
    sales_units = Column(Integer, default=0)
    revenue = Column(Float, default=0.0)
    royalties = Column(Float, default=0.0)
    pages_read = Column(Integer, default=0)  # KDP Unlimited
    
    # Performance metrics
    rank = Column(Integer, nullable=True)
    reviews_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)
    
    # Time period
    report_date = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
    
    # Add indexes for performance
    __table_args__ = (
        Index('idx_sales_book_date', 'book_id', 'report_date'),
    )