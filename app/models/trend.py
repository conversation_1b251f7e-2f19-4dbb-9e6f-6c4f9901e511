### app/models/trend.py - Trend Analysis Model

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Float, Index
from sqlalchemy.orm import relationship
from datetime import datetime
from app.database import Base

class TrendAnalysis(Base):
    __tablename__ = "trend_analyses"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)  # Nullable for system analyses
    
    # Analysis parameters
    request_config = Column(JSON, nullable=True)  # Analysis configuration
    
    # Results
    result = Column(JSON, nullable=True)  # Full analysis results
    
    # Status and metadata
    status = Column(String(50), default="analyzing", index=True)  # analyzing, completed, failed
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="trend_analyses")
    
    # Add indexes for performance
    __table_args__ = (
        Index('idx_trend_user_status', 'user_id', 'status'),
        Index('idx_trend_created', 'created_at'),
    )

# TrendOpportunity is handled through Pydantic schemas in the result JSON