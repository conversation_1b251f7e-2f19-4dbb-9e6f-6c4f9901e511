### app/models/user.py - Enhanced User Model with Pydantic Validation

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, event
from sqlalchemy.orm import relationship, validates
from datetime import datetime, timezone
from app.database import Base
from typing import Dict, Any, Optional
import re

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    password_hash = Column(String(255), nullable=False)
    subscription_tier = Column(String(50), default="free")
    is_active = Column(Boolean, default=True)
    
    # KDP Credentials (encrypted JSON)
    kdp_credentials = Column(JSON, nullable=True)
    kdp_credentials_updated_at = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    last_login_at = Column(DateTime, nullable=True)
    
    # Relationships
    books = relationship("Book", back_populates="user", cascade="all, delete-orphan")
    trend_analyses = relationship("TrendAnalysis", back_populates="user", cascade="all, delete-orphan")
    
    # Pydantic-style validation methods
    @validates('email')
    def validate_email(self, key, email):
        """Validate and normalize email"""
        if not email or not email.strip():
            raise ValueError("Email cannot be empty")
        
        email = email.strip().lower()
        
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValueError("Invalid email format")
        
        if len(email) > 255:
            raise ValueError("Email too long (max 255 characters)")
        
        return email
    
    @validates('full_name')
    def validate_full_name(self, key, full_name):
        """Validate and clean full name"""
        if not full_name or not full_name.strip():
            raise ValueError("Full name cannot be empty")
        
        full_name = full_name.strip()
        
        # Allow letters, spaces, hyphens, dots, and apostrophes
        if not re.match(r'^[a-zA-Z\s\-\.\']+$', full_name):
            raise ValueError("Full name can only contain letters, spaces, hyphens, dots, and apostrophes")
        
        if len(full_name) < 2:
            raise ValueError("Full name too short (minimum 2 characters)")
        if len(full_name) > 255:
            raise ValueError("Full name too long (max 255 characters)")
        
        # Capitalize properly
        return full_name.title()
    
    @validates('subscription_tier')
    def validate_subscription_tier(self, key, tier):
        """Validate subscription tier"""
        valid_tiers = {"free", "basic", "premium"}
        
        if not tier:
            return "free"  # Default
        
        tier = tier.lower().strip()
        if tier not in valid_tiers:
            raise ValueError(f"Invalid subscription tier. Must be one of: {', '.join(valid_tiers)}")
        
        return tier
    
    @validates('kdp_credentials')
    def validate_kdp_credentials(self, key, credentials):
        """Validate KDP credentials structure"""
        if credentials is None:
            return None
        
        if not isinstance(credentials, dict):
            raise ValueError("KDP credentials must be a dictionary")
        
        required_fields = {'email', 'password'}
        if not all(field in credentials for field in required_fields):
            raise ValueError("KDP credentials must include email and password")
        
        # Validate email format in KDP credentials
        email = credentials.get('email', '').strip()
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValueError("Invalid email in KDP credentials")
        
        return credentials
    
    # Computed properties
    @property
    def days_since_registration(self) -> int:
        """Number of days since user registered"""
        if not self.created_at:
            return 0
        return (datetime.now(timezone.utc) - self.created_at).days
    
    @property
    def has_kdp_credentials(self) -> bool:
        """Whether user has configured KDP credentials"""
        return self.kdp_credentials is not None
    
    @property
    def subscription_level_numeric(self) -> int:
        """Numeric representation of subscription tier"""
        levels = {"free": 0, "basic": 1, "premium": 2}
        return levels.get(self.subscription_tier, 0)
    
    @property
    def is_premium_user(self) -> bool:
        """Whether user has premium subscription"""
        return self.subscription_tier == "premium"
    
    @property
    def book_count(self) -> int:
        """Number of books user has created"""
        return len(self.books) if self.books else 0
    
    @property
    def published_book_count(self) -> int:
        """Number of published books"""
        if not self.books:
            return 0
        return len([book for book in self.books if book.status == "published"])
    
    def to_pydantic(self) -> Dict[str, Any]:
        """Convert to Pydantic-compatible dict"""
        try:
            from app.schemas.user import UserResponse
            return UserResponse.model_validate(self, from_attributes=True).model_dump()
        except ImportError:
            # Fallback if schemas not available
            return {
                'id': self.id,
                'email': self.email,
                'full_name': self.full_name,
                'subscription_tier': self.subscription_tier,
                'is_active': self.is_active,
                'created_at': self.created_at.isoformat() if self.created_at else None,
                'last_login_at': self.last_login_at.isoformat() if self.last_login_at else None
            }
    
    @classmethod
    def from_pydantic(cls, user_data, password_hash: str):
        """Create from Pydantic model with validation"""
        try:
            from app.schemas.user import UserCreate
            
            # Validate using Pydantic schema
            if not isinstance(user_data, dict):
                user_data = user_data.model_dump()
            
            validated_data = UserCreate.model_validate(user_data)
            
            return cls(
                email=validated_data.email,
                full_name=validated_data.full_name,
                password_hash=password_hash,
                subscription_tier=validated_data.subscription_tier
            )
        except ImportError:
            # Fallback if schemas not available
            return cls(
                email=user_data.get('email'),
                full_name=user_data.get('full_name'),
                password_hash=password_hash,
                subscription_tier=user_data.get('subscription_tier', 'free')
            )