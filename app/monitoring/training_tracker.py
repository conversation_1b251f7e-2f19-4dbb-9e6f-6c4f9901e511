### app/monitoring/training_tracker.py - Training Tracker

import async<PERSON>
import json
from typing import Dict, Any, Optional
from datetime import datetime
import logging
from pathlib import Path

import wandb
from celery.result import AsyncResult
from celery import current_app as celery_app

logger = logging.getLogger(__name__)

class TrainingTracker:
    """Tracks VERL training progress in real-time"""
    
    def __init__(self):
        self.current_training_task = None
        self.training_logs = []
        self.performance_history = []
    
    def start_tracking_training(self, task_id: str):
        """Start tracking a training task"""
        self.current_training_task = task_id
        logger.info(f"Started tracking training task: {task_id}")
    
    async def get_training_progress(self) -> Dict[str, Any]:
        """Get current training progress"""
        
        if not self.current_training_task:
            return {"status": "no_training", "message": "No training in progress"}
        
        # Get Celery task status
        result = AsyncResult(self.current_training_task, app=celery_app)
        
        progress_data = {
            "task_id": self.current_training_task,
            "status": result.status,
            "progress": 0.0,
            "current_epoch": 0,
            "total_epochs": 0,
            "logs": self.training_logs[-10:],  # Last 10 log entries
            "estimated_completion": None
        }
        
        if result.info:
            if isinstance(result.info, dict):
                progress_data.update(result.info)
        
        return progress_data
    
    async def get_wandb_metrics(self, run_id: Optional[str] = None) -> Dict[str, Any]:
        """Get training metrics from Weights & Biases"""
        
        try:
            if not run_id:
                # Get latest run
                api = wandb.Api()
                runs = api.runs("ebook-verl")  # Your project name
                if runs:
                    run = runs[0]  # Latest run
                else:
                    return {"error": "No training runs found"}
            else:
                api = wandb.Api()
                run = api.run(f"ebook-verl/{run_id}")
            
            # Get metrics
            metrics = {
                "run_id": run.id,
                "status": run.state,
                "duration": run.summary.get("_runtime", 0),
                "metrics": {
                    "loss": run.summary.get("loss", 0),
                    "reward": run.summary.get("reward", 0),
                    "approval_rate": run.summary.get("approval_rate", 0),
                    "quality_score": run.summary.get("quality_score", 0)
                },
                "config": dict(run.config),
                "created_at": run.created_at,
                "updated_at": run.updated_at
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error fetching W&B metrics: {str(e)}")
            return {"error": str(e)}
    
    def log_training_event(self, event_type: str, data: Dict[str, Any]):
        """Log a training event"""
        
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "type": event_type,
            "data": data
        }
        
        self.training_logs.append(log_entry)
        
        # Keep only last 100 entries
        if len(self.training_logs) > 100:
            self.training_logs = self.training_logs[-100:]
        
        logger.info(f"Training event: {event_type} - {data}")

# Global tracker instance
training_tracker = TrainingTracker()