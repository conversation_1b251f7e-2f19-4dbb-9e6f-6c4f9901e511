### app/monitoring/verl_monitor.py - VERL Monitor

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging
from dataclasses import dataclass, asdict
import threading
import time

from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_

from app.database import get_db
from app.models.book import Book
from app.models.feedback import FeedbackMetric, ModelPerformance
from app.models.publication import SalesData
from app.ml.data_access import VERLDataAccessor

logger = logging.getLogger(__name__)

@dataclass
class DataFlowMetrics:
    """Real-time data flow metrics"""
    timestamp: datetime
    new_feedback_count: int
    approval_rate: float
    quality_average: float
    training_readiness: bool
    total_training_examples: int
    reward_signals_strength: float
    data_quality_score: float

@dataclass
class TrainingStatus:
    """Current training pipeline status"""
    is_training: bool
    current_epoch: int
    total_epochs: int
    progress_percentage: float
    estimated_completion: Optional[datetime]
    current_loss: float
    current_reward: float
    last_training_completed: Optional[datetime]

class VERLMonitor:
    """Real-time monitoring system for VERL data flow and training"""
    
    def __init__(self):
        self.data_accessor = VERLDataAccessor()
        self.metrics_history = deque(maxlen=1000)  # Keep last 1000 metrics
        self.is_monitoring = False
        self.monitoring_thread = None
        
        # Real-time counters
        self.live_counters = {
            'approvals_today': 0,
            'rejections_today': 0,
            'books_generated_today': 0,
            'training_sessions_today': 0,
            'data_points_collected': 0
        }
        
        # Performance tracking
        self.performance_trends = {
            'approval_rate_24h': deque(maxlen=144),  # 10-minute intervals for 24h
            'quality_scores_24h': deque(maxlen=144),
            'reward_signals_24h': deque(maxlen=144),
            'training_performance': deque(maxlen=100)
        }
    
    async def start_monitoring(self, interval_seconds: int = 60):
        """Start real-time monitoring"""
        
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval_seconds,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        logger.info("VERL monitoring started")
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("VERL monitoring stopped")
    
    def _monitoring_loop(self, interval_seconds: int):
        """Main monitoring loop (runs in background thread)"""
        
        while self.is_monitoring:
            try:
                # Collect current metrics
                asyncio.run(self._collect_current_metrics())
                
                # Update live counters
                self._update_live_counters()
                
                # Update performance trends
                self._update_performance_trends()
                
                time.sleep(interval_seconds)
                
            except Exception as e:
                logger.error(f"Monitoring loop error: {str(e)}")
                time.sleep(10)  # Wait longer on error
    
    async def _collect_current_metrics(self):
        """Collect current system metrics"""
        
        try:
            # Get recent feedback stats
            feedback_stats = await self.data_accessor.get_recent_feedback_stats(days=1)
            
            # Check training readiness
            from app.services.training_scheduler import TrainingScheduler
            scheduler = TrainingScheduler()
            training_conditions = await scheduler.check_training_conditions()
            
            # Calculate data quality score
            data_quality = await self._calculate_data_quality_score()
            
            # Calculate reward signal strength
            reward_strength = await self._calculate_reward_signal_strength()
            
            # Create metrics object
            metrics = DataFlowMetrics(
                timestamp=datetime.now(),
                new_feedback_count=feedback_stats.get('total_books', 0),
                approval_rate=feedback_stats.get('approval_rate', 0.0),
                quality_average=feedback_stats.get('average_quality', 0.0),
                training_readiness=training_conditions['should_train'],
                total_training_examples=await self._count_total_training_examples(),
                reward_signals_strength=reward_strength,
                data_quality_score=data_quality
            )
            
            # Add to history
            self.metrics_history.append(metrics)
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {str(e)}")
    
    async def _calculate_data_quality_score(self) -> float:
        """Calculate overall data quality score"""
        
        db = next(get_db())
        try:
            # Get recent data
            cutoff = datetime.now() - timedelta(days=7)
            
            recent_performance = db.query(ModelPerformance).filter(
                ModelPerformance.created_at >= cutoff
            ).all()
            
            if not recent_performance:
                return 0.0
            
            quality_factors = []
            
            # Factor 1: Data completeness
            complete_records = sum(1 for p in recent_performance 
                                 if p.reward_signal is not None and p.quality_score > 0)
            completeness = complete_records / len(recent_performance)
            quality_factors.append(completeness)
            
            # Factor 2: Signal clarity (high absolute reward values)
            clear_signals = sum(1 for p in recent_performance 
                              if p.reward_signal and abs(p.reward_signal) > 0.3)
            signal_clarity = clear_signals / len(recent_performance)
            quality_factors.append(signal_clarity)
            
            # Factor 3: Consistency (similar books have similar rewards)
            # Simplified: check if approval rate correlates with reward
            approvals = [p for p in recent_performance if p.user_approval]
            rejections = [p for p in recent_performance if not p.user_approval]
            
            if approvals and rejections:
                avg_approval_reward = sum(p.reward_signal or 0 for p in approvals) / len(approvals)
                avg_rejection_reward = sum(p.reward_signal or 0 for p in rejections) / len(rejections)
                consistency = max(0, avg_approval_reward - avg_rejection_reward)
            else:
                consistency = 0.5
            
            quality_factors.append(min(consistency, 1.0))
            
            # Overall quality score
            return sum(quality_factors) / len(quality_factors)
            
        finally:
            db.close()
    
    async def _calculate_reward_signal_strength(self) -> float:
        """Calculate strength of reward signals"""
        
        db = next(get_db())
        try:
            # Get recent reward signals
            cutoff = datetime.now() - timedelta(days=7)
            
            rewards = db.query(ModelPerformance.reward_signal).filter(
                and_(
                    ModelPerformance.created_at >= cutoff,
                    ModelPerformance.reward_signal.isnot(None)
                )
            ).all()
            
            if not rewards:
                return 0.0
            
            reward_values = [r[0] for r in rewards]
            
            # Calculate signal strength (average absolute value)
            signal_strength = sum(abs(r) for r in reward_values) / len(reward_values)
            
            return min(signal_strength, 1.0)
            
        finally:
            db.close()
    
    async def _count_total_training_examples(self) -> int:
        """Count total available training examples"""
        
        try:
            training_data = await self.data_accessor.get_training_data(days_back=90)
            return len(training_data)
        except:
            return 0
    
    def _update_live_counters(self):
        """Update live counters for today"""
        
        # These would be updated by event handlers in your app
        # For now, simulate with database queries
        
        db = next(get_db())
        try:
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time())
            
            # Count today's activities
            self.live_counters['books_generated_today'] = db.query(Book).filter(
                Book.created_at >= today_start
            ).count()
            
            approvals_today = db.query(ModelPerformance).filter(
                and_(
                    ModelPerformance.created_at >= today_start,
                    ModelPerformance.user_approval == True
                )
            ).count()
            
            rejections_today = db.query(ModelPerformance).filter(
                and_(
                    ModelPerformance.created_at >= today_start,
                    ModelPerformance.user_approval == False
                )
            ).count()
            
            self.live_counters['approvals_today'] = approvals_today
            self.live_counters['rejections_today'] = rejections_today
            self.live_counters['data_points_collected'] = approvals_today + rejections_today
            
        finally:
            db.close()
    
    def _update_performance_trends(self):
        """Update performance trend data"""
        
        if not self.metrics_history:
            return
        
        current_metrics = self.metrics_history[-1]
        
        # Add to 24-hour trends
        self.performance_trends['approval_rate_24h'].append(current_metrics.approval_rate)
        self.performance_trends['quality_scores_24h'].append(current_metrics.quality_average)
        self.performance_trends['reward_signals_24h'].append(current_metrics.reward_signals_strength)
    
    async def get_current_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        
        if not self.metrics_history:
            await self._collect_current_metrics()
        
        current_metrics = self.metrics_history[-1] if self.metrics_history else None
        
        return {
            'monitoring_active': self.is_monitoring,
            'last_update': current_metrics.timestamp if current_metrics else None,
            'current_metrics': asdict(current_metrics) if current_metrics else None,
            'live_counters': self.live_counters.copy(),
            'trends': {
                'approval_rate_trend': list(self.performance_trends['approval_rate_24h']),
                'quality_trend': list(self.performance_trends['quality_scores_24h']),
                'reward_trend': list(self.performance_trends['reward_signals_24h'])
            }
        }
    
    async def get_training_status(self) -> TrainingStatus:
        """Get current training pipeline status"""
        
        # This would integrate with your actual training system
        # For now, simulate based on available information
        
        try:
            # Check if training is currently running
            # (You'd implement this based on your Celery task monitoring)
            
            return TrainingStatus(
                is_training=False,  # Would check actual training tasks
                current_epoch=0,
                total_epochs=0,
                progress_percentage=0.0,
                estimated_completion=None,
                current_loss=0.0,
                current_reward=0.0,
                last_training_completed=None  # Would get from training logs
            )
            
        except Exception as e:
            logger.error(f"Error getting training status: {str(e)}")
            return TrainingStatus(
                is_training=False,
                current_epoch=0,
                total_epochs=0,
                progress_percentage=0.0,
                estimated_completion=None,
                current_loss=0.0,
                current_reward=0.0,
                last_training_completed=None
            )
    
    async def get_data_flow_analysis(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get detailed data flow analysis"""
        
        cutoff = datetime.now() - timedelta(hours=hours_back)
        
        # Filter metrics to time window
        recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff]
        
        if not recent_metrics:
            return {"error": "No recent data available"}
        
        # Calculate statistics
        approval_rates = [m.approval_rate for m in recent_metrics]
        quality_scores = [m.quality_average for m in recent_metrics]
        data_quality = [m.data_quality_score for m in recent_metrics]
        
        return {
            'time_period': f"Last {hours_back} hours",
            'total_data_points': len(recent_metrics),
            'approval_rate': {
                'current': approval_rates[-1] if approval_rates else 0,
                'average': sum(approval_rates) / len(approval_rates) if approval_rates else 0,
                'trend': 'improving' if len(approval_rates) > 1 and approval_rates[-1] > approval_rates[0] else 'stable'
            },
            'quality_scores': {
                'current': quality_scores[-1] if quality_scores else 0,
                'average': sum(quality_scores) / len(quality_scores) if quality_scores else 0,
                'trend': 'improving' if len(quality_scores) > 1 and quality_scores[-1] > quality_scores[0] else 'stable'
            },
            'data_quality': {
                'current': data_quality[-1] if data_quality else 0,
                'average': sum(data_quality) / len(data_quality) if data_quality else 0
            },
            'training_readiness': recent_metrics[-1].training_readiness if recent_metrics else False
        }

# Singleton monitor instance
verl_monitor = VERLMonitor()