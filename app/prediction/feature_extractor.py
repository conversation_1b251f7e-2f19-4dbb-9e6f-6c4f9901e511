### app/prediction/feature_extractor.py - Feature Extractor

import re
import nltk
from textstat import flesch_reading_ease, flesch_kincaid_grade
from collections import Counter
import numpy as np
from typing import Dict, Any, List
import logging

from app.schemas.book import Manuscript

logger = logging.getLogger(__name__)

class ContentFeatureExtractor:
    """Extract predictive features from manuscript content"""
    
    def __init__(self):
        # Download required NLTK data
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('vader_lexicon', quiet=True)
        except:
            logger.warning("Failed to download NLTK data")
    
    async def extract_all_features(self, manuscript: Manuscript) -> Dict[str, float]:
        """Extract comprehensive features for prediction model"""
        
        # Combine all text content
        full_text = self._get_full_text(manuscript)
        
        features = {}
        
        # Content quality features
        features.update(await self._extract_quality_features(full_text))
        
        # Readability features
        features.update(await self._extract_readability_features(full_text))
        
        # Title and metadata features
        features.update(await self._extract_metadata_features(manuscript))
        
        # Structure features
        features.update(await self._extract_structure_features(manuscript))
        
        # Engagement features
        features.update(await self._extract_engagement_features(full_text))
        
        # Market relevance features
        features.update(await self._extract_market_features(manuscript))
        
        return features
    
    def _get_full_text(self, manuscript: Manuscript) -> str:
        """Combine all manuscript text"""
        
        full_text = ""
        
        if manuscript.introduction:
            full_text += manuscript.introduction + " "
        
        for chapter in manuscript.chapters:
            full_text += chapter.content + " "
        
        if manuscript.conclusion:
            full_text += manuscript.conclusion + " "
        
        return full_text.strip()
    
    async def _extract_quality_features(self, text: str) -> Dict[str, float]:
        """Extract content quality indicators"""
        
        features = {}
        
        # Word count
        words = text.split()
        features['word_count'] = len(words)
        features['word_count_normalized'] = min(len(words) / 10000, 1.0)  # Normalize to 0-1
        
        # Sentence variety
        sentences = re.split(r'[.!?]+', text)
        sentence_lengths = [len(s.split()) for s in sentences if s.strip()]
        
        if sentence_lengths:
            features['avg_sentence_length'] = np.mean(sentence_lengths)
            features['sentence_length_variety'] = np.std(sentence_lengths) / np.mean(sentence_lengths)
        else:
            features['avg_sentence_length'] = 0
            features['sentence_length_variety'] = 0
        
        # Vocabulary richness
        unique_words = set(word.lower() for word in words if word.isalpha())
        features['vocabulary_richness'] = len(unique_words) / len(words) if words else 0
        
        # Content density (non-stop words)
        try:
            from nltk.corpus import stopwords
            stop_words = set(stopwords.words('english'))
            content_words = [word for word in words if word.lower() not in stop_words]
            features['content_density'] = len(content_words) / len(words) if words else 0
        except:
            features['content_density'] = 0.7  # Default estimate
        
        # Actionable content indicators
        action_words = ['how to', 'step', 'guide', 'tip', 'strategy', 'method', 'technique', 'approach']
        action_count = sum(text.lower().count(word) for word in action_words)
        features['actionable_content_score'] = min(action_count / 10, 1.0)
        
        # Question engagement
        question_count = text.count('?')
        features['engagement_questions'] = min(question_count / 20, 1.0)
        
        return features
    
    async def _extract_readability_features(self, text: str) -> Dict[str, float]:
        """Extract readability metrics"""
        
        features = {}
        
        try:
            # Flesch Reading Ease (higher = easier)
            ease_score = flesch_reading_ease(text)
            features['reading_ease'] = ease_score / 100  # Normalize to 0-1
            
            # Flesch-Kincaid Grade Level
            grade_level = flesch_kincaid_grade(text)
            features['grade_level'] = min(grade_level / 16, 1.0)  # Normalize (cap at 16th grade)
            
            # Optimal reading level (8th-12th grade is ideal for most audiences)
            if 8 <= grade_level <= 12:
                features['optimal_reading_level'] = 1.0
            else:
                features['optimal_reading_level'] = max(0, 1 - abs(grade_level - 10) / 10)
                
        except Exception as e:
            logger.warning(f"Readability calculation failed: {e}")
            features['reading_ease'] = 0.7
            features['grade_level'] = 0.6
            features['optimal_reading_level'] = 0.8
        
        return features
    
    async def _extract_metadata_features(self, manuscript: Manuscript) -> Dict[str, float]:
        """Extract features from title and metadata"""
        
        features = {}
        
        # Title analysis
        title = manuscript.title.lower()
        title_words = title.split()
        
        features['title_length'] = len(title_words)
        features['title_length_optimal'] = 1.0 if 3 <= len(title_words) <= 8 else 0.5
        
        # Title power words
        power_words = [
            'ultimate', 'complete', 'guide', 'mastery', 'secrets', 'proven', 
            'step-by-step', 'easy', 'simple', 'advanced', 'professional',
            'success', 'winning', 'effective', 'powerful', 'essential'
        ]
        
        power_word_count = sum(1 for word in power_words if word in title)
        features['title_power_words'] = min(power_word_count / 3, 1.0)
        
        # Subtitle presence
        features['has_subtitle'] = 1.0 if manuscript.subtitle else 0.0
        
        # Keywords quality
        if manuscript.keywords:
            features['keyword_count'] = min(len(manuscript.keywords) / 7, 1.0)  # KDP allows 7
            
            # Check if keywords are in title/content
            title_keyword_match = sum(1 for kw in manuscript.keywords if kw.lower() in title)
            features['title_keyword_alignment'] = title_keyword_match / len(manuscript.keywords)
        else:
            features['keyword_count'] = 0
            features['title_keyword_alignment'] = 0
        
        return features
    
    async def _extract_structure_features(self, manuscript: Manuscript) -> Dict[str, float]:
        """Extract structural features"""
        
        features = {}
        
        # Chapter analysis
        chapter_count = len(manuscript.chapters)
        features['chapter_count'] = chapter_count
        features['optimal_chapter_count'] = 1.0 if 5 <= chapter_count <= 15 else 0.7
        
        # Chapter length consistency
        if manuscript.chapters:
            chapter_lengths = [chapter.word_count for chapter in manuscript.chapters if chapter.word_count]
            
            if chapter_lengths:
                avg_length = np.mean(chapter_lengths)
                length_variance = np.var(chapter_lengths)
                features['avg_chapter_length'] = min(avg_length / 1500, 1.0)  # Optimal ~1500 words
                features['chapter_consistency'] = max(0, 1 - (length_variance / (avg_length ** 2)))
            else:
                features['avg_chapter_length'] = 0.5
                features['chapter_consistency'] = 0.5
        
        # Structure completeness
        structure_score = 0
        if manuscript.introduction:
            structure_score += 0.2
        if manuscript.conclusion:
            structure_score += 0.2
        if manuscript.about_author:
            structure_score += 0.1
        if chapter_count >= 5:
            structure_score += 0.5
        
        features['structure_completeness'] = structure_score
        
        return features
    
    async def _extract_engagement_features(self, text: str) -> Dict[str, float]:
        """Extract engagement prediction features"""
        
        features = {}
        
        # Emotional engagement
        try:
            from nltk.sentiment import SentimentIntensityAnalyzer
            sia = SentimentIntensityAnalyzer()
            sentiment = sia.polarity_scores(text)
            
            # Positive sentiment generally performs better
            features['sentiment_positive'] = sentiment['pos']
            features['sentiment_engagement'] = abs(sentiment['compound'])  # Strong sentiment (positive or negative)
        except:
            features['sentiment_positive'] = 0.6
            features['sentiment_engagement'] = 0.5
        
        # Examples and case studies
        example_indicators = ['example', 'for instance', 'case study', 'imagine', 'picture this']
        example_count = sum(text.lower().count(indicator) for indicator in example_indicators)
        features['examples_count'] = min(example_count / 10, 1.0)
        
        # Lists and bullet points
        list_indicators = text.count('\n•') + text.count('\n-') + text.count('\n*')
        features['structured_content'] = min(list_indicators / 20, 1.0)
        
        # Personal pronouns (engagement)
        personal_pronouns = ['you', 'your', 'we', 'our', 'I', 'my']
        pronoun_count = sum(text.lower().count(f' {pronoun} ') for pronoun in personal_pronouns)
        features['personal_engagement'] = min(pronoun_count / 100, 1.0)
        
        return features
    
    async def _extract_market_features(self, manuscript: Manuscript) -> Dict[str, float]:
        """Extract market relevance features"""
        
        features = {}
        
        # Category popularity (simplified - would use real market data)
        category_popularity = {
            'self-help': 0.9,
            'business': 0.8,
            'health': 0.85,
            'romance': 0.7,
            'mystery': 0.6,
            'fantasy': 0.5,
            'cooking': 0.7,
            'parenting': 0.75,
            'finance': 0.8,
            'productivity': 0.85
        }
        
        features['category_popularity'] = category_popularity.get(manuscript.category, 0.5)
        
        # Trending keywords (simplified)
        trending_keywords = [
            'ai', 'productivity', 'mindfulness', 'remote work', 'crypto',
            'sustainable', 'mental health', 'side hustle', 'habits'
        ]
        
        text_lower = self._get_full_text(manuscript).lower()
        trending_count = sum(1 for keyword in trending_keywords if keyword in text_lower)
        features['trending_relevance'] = min(trending_count / 5, 1.0)
        
        # Evergreen vs timely content
        evergreen_indicators = ['principle', 'foundation', 'basic', 'fundamental', 'timeless']
        timely_indicators = ['2024', '2025', 'current', 'latest', 'new', 'recent']
        
        evergreen_score = sum(1 for indicator in evergreen_indicators if indicator in text_lower)
        timely_score = sum(1 for indicator in timely_indicators if indicator in text_lower)
        
        # Evergreen content typically performs better long-term
        features['evergreen_content'] = min(evergreen_score / 10, 1.0)
        features['timely_content'] = min(timely_score / 5, 1.0)
        
        return features

class TitleAnalyzer:
    """Specialized analysis for book titles"""
    
    def __init__(self):
        # High-performing title patterns from successful books
        self.success_patterns = [
            r'the .* guide to .*',
            r'how to .* in .* steps',
            r'.* secrets of .*',
            r'mastering .*',
            r'the complete .*',
            r'.* for beginners',
            r'ultimate .* handbook'
        ]
    
    async def analyze_title_appeal(self, title: str) -> Dict[str, float]:
        """Analyze title for market appeal"""
        
        features = {}
        title_lower = title.lower()
        
        # Pattern matching
        pattern_matches = sum(1 for pattern in self.success_patterns 
                            if re.search(pattern, title_lower))
        features['successful_pattern_match'] = min(pattern_matches / 2, 1.0)
        
        # Clarity and specificity
        specific_words = ['step', 'guide', 'method', 'strategy', 'system', 'blueprint']
        specificity_score = sum(1 for word in specific_words if word in title_lower)
        features['title_specificity'] = min(specificity_score / 3, 1.0)
        
        # Benefit-oriented
        benefit_words = ['success', 'wealth', 'health', 'happiness', 'freedom', 'mastery']
        benefit_score = sum(1 for word in benefit_words if word in title_lower)
        features['benefit_oriented'] = min(benefit_score / 2, 1.0)
        
        # Curiosity gap
        curiosity_words = ['secret', 'hidden', 'unknown', 'surprising', 'shocking']
        curiosity_score = sum(1 for word in curiosity_words if word in title_lower)
        features['curiosity_factor'] = min(curiosity_score / 2, 1.0)
        
        return features