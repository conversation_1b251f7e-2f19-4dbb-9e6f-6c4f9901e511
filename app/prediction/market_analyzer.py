### app/prediction/market_analyzer.py - Market Analyzer

import asyncio
import httpx
from bs4 import BeautifulSoup
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any
import numpy as np
import logging

from app.database import get_db
from app.prediction.models import MarketAnalysis
from app.utils.scrapers import AmazonScraper

logger = logging.getLogger(__name__)

class MarketIntelligenceEngine:
    """Analyze market conditions and competition"""
    
    def __init__(self):
        self.amazon_scraper = AmazonScraper()
        self.cache_duration = timedelta(hours=6)  # Cache market data for 6 hours
    
    async def analyze_market_conditions(
        self, 
        category: str, 
        force_refresh: bool = False
    ) -> Dict[str, float]:
        """Comprehensive market analysis for category"""
        
        # Check for cached analysis
        if not force_refresh:
            cached_analysis = await self._get_cached_analysis(category)
            if cached_analysis:
                return cached_analysis
        
        logger.info(f"Analyzing market conditions for category: {category}")
        
        # Perform comprehensive market analysis
        market_data = {}
        
        # 1. Competition density analysis
        competition_data = await self._analyze_competition_density(category)
        market_data.update(competition_data)
        
        # 2. Price analysis
        price_data = await self._analyze_price_landscape(category)
        market_data.update(price_data)
        
        # 3. Quality benchmarks
        quality_data = await self._analyze_quality_benchmarks(category)
        market_data.update(quality_data)
        
        # 4. Trend analysis
        trend_data = await self._analyze_category_trends(category)
        market_data.update(trend_data)
        
        # 5. Seasonal patterns
        seasonal_data = await self._analyze_seasonal_patterns(category)
        market_data.update(seasonal_data)
        
        # 6. Market opportunity score
        opportunity_score = await self._calculate_opportunity_score(market_data)
        market_data['market_opportunity_score'] = opportunity_score
        
        # Cache the results
        await self._cache_market_analysis(category, market_data)
        
        return market_data
    
    async def _analyze_competition_density(self, category: str) -> Dict[str, float]:
        """Analyze how competitive the category is"""
        
        try:
            # Get recent releases (last 30 days)
            recent_books = await self.amazon_scraper.get_new_releases(category, limit=100)
            
            if not recent_books:
                return {
                    'competition_density': 0.5,
                    'daily_new_releases': 0,
                    'market_saturation': 0.5
                }
            
            # Calculate daily release rate
            daily_releases = len(recent_books) / 30
            
            # Competition density score (more books = higher competition)
            # Normalize: 0-3 books/day = low, 3-10 = medium, 10+ = high
            competition_density = min(daily_releases / 10, 1.0)
            
            # Market saturation analysis
            market_saturation = await self._calculate_market_saturation(recent_books)
            
            return {
                'competition_density': competition_density,
                'daily_new_releases': daily_releases,
                'market_saturation': market_saturation
            }
            
        except Exception as e:
            logger.error(f"Competition analysis failed: {e}")
            return {
                'competition_density': 0.5,
                'daily_new_releases': 2.0,
                'market_saturation': 0.5
            }
    
    async def _analyze_price_landscape(self, category: str) -> Dict[str, float]:
        """Analyze pricing in the category"""
        
        try:
            # Get bestsellers to understand successful pricing
            bestsellers = await self.amazon_scraper.get_kindle_bestsellers(category, limit=50)
            
            if not bestsellers:
                return {
                    'avg_price': 4.99,
                    'price_competition': 0.5,
                    'optimal_price_range_min': 2.99,
                    'optimal_price_range_max': 7.99
                }
            
            # Extract prices
            prices = [book.get('price', 0) for book in bestsellers if book.get('price', 0) > 0]
            
            if not prices:
                return {
                    'avg_price': 4.99,
                    'price_competition': 0.5,
                    'optimal_price_range_min': 2.99,
                    'optimal_price_range_max': 7.99
                }
            
            avg_price = np.mean(prices)
            price_std = np.std(prices)
            
            # Price competition (lower std = more price competition)
            price_competition = max(0, 1 - (price_std / avg_price))
            
            # Optimal price range (middle 50% of successful books)
            prices_sorted = sorted(prices)
            q25_index = int(len(prices_sorted) * 0.25)
            q75_index = int(len(prices_sorted) * 0.75)
            
            optimal_min = prices_sorted[q25_index] if q25_index < len(prices_sorted) else 2.99
            optimal_max = prices_sorted[q75_index] if q75_index < len(prices_sorted) else 7.99
            
            return {
                'avg_price': avg_price,
                'price_competition': price_competition,
                'optimal_price_range_min': optimal_min,
                'optimal_price_range_max': optimal_max,
                'price_volatility': price_std / avg_price if avg_price > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Price analysis failed: {e}")
            return {
                'avg_price': 4.99,
                'price_competition': 0.5,
                'optimal_price_range_min': 2.99,
                'optimal_price_range_max': 7.99,
                'price_volatility': 0.3
            }
    
    async def _analyze_quality_benchmarks(self, category: str) -> Dict[str, float]:
        """Analyze quality standards in the category"""
        
        try:
            bestsellers = await self.amazon_scraper.get_kindle_bestsellers(category, limit=30)
            
            if not bestsellers:
                return {
                    'avg_rating': 4.0,
                    'avg_review_count': 50,
                    'quality_bar': 0.7
                }
            
            # Extract ratings and review counts
            ratings = [book.get('rating', 0) for book in bestsellers if book.get('rating', 0) > 0]
            review_counts = [book.get('review_count', 0) for book in bestsellers]
            
            avg_rating = np.mean(ratings) if ratings else 4.0
            avg_reviews = np.mean(review_counts) if review_counts else 50
            
            # Quality bar (how high the standards are)
            # Higher average rating + more reviews = higher quality bar
            quality_bar = (avg_rating / 5.0) * 0.7 + min(avg_reviews / 100, 1.0) * 0.3
            
            return {
                'avg_rating': avg_rating,
                'avg_review_count': avg_reviews,
                'quality_bar': quality_bar,
                'review_threshold': avg_reviews * 0.5  # Minimum reviews to be competitive
            }
            
        except Exception as e:
            logger.error(f"Quality analysis failed: {e}")
            return {
                'avg_rating': 4.0,
                'avg_review_count': 50,
                'quality_bar': 0.7,
                'review_threshold': 25
            }
    
    async def _analyze_category_trends(self, category: str) -> Dict[str, float]:
        """Analyze trending topics and keywords in category"""
        
        try:
            # Get recent bestsellers and new releases
            bestsellers = await self.amazon_scraper.get_kindle_bestsellers(category, limit=20)
            new_releases = await self.amazon_scraper.get_new_releases(category, limit=20)
            
            all_books = bestsellers + new_releases
            
            if not all_books:
                return {
                    'trend_momentum': 0.5,
                    'topic_saturation': 0.5,
                    'innovation_opportunity': 0.7
                }
            
            # Extract titles and analyze keywords
            titles = [book.get('title', '') for book in all_books]
            
            # Find common keywords
            all_words = []
            for title in titles:
                words = re.findall(r'\b\w+\b', title.lower())
                all_words.extend([word for word in words if len(word) > 3])
            
            from collections import Counter
            word_freq = Counter(all_words)
            
            # Trend momentum (variety in topics)
            unique_words = len(word_freq)
            total_words = len(all_words)
            trend_momentum = unique_words / total_words if total_words > 0 else 0.5
            
            # Topic saturation (repetition of same themes)
            if word_freq:
                most_common_freq = word_freq.most_common(1)[0][1]
                topic_saturation = most_common_freq / len(titles)
            else:
                topic_saturation = 0.5
            
            # Innovation opportunity (inverse of saturation)
            innovation_opportunity = 1 - topic_saturation
            
            return {
                'trend_momentum': trend_momentum,
                'topic_saturation': topic_saturation,
                'innovation_opportunity': innovation_opportunity,
                'trending_keywords': dict(word_freq.most_common(10))
            }
            
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            return {
                'trend_momentum': 0.5,
                'topic_saturation': 0.5,
                'innovation_opportunity': 0.7,
                'trending_keywords': {}
            }
    
    async def _analyze_seasonal_patterns(self, category: str) -> Dict[str, float]:
        """Analyze seasonal buying patterns"""
        
        # Seasonal multipliers based on category and month
        seasonal_patterns = {
            'self-help': {
                1: 1.3,   # January (New Year resolutions)
                2: 1.1,   # February
                3: 1.0,   # March
                4: 0.9,   # April
                5: 0.9,   # May
                6: 0.8,   # June
                7: 0.8,   # July
                8: 0.9,   # August
                9: 1.1,   # September (back to school)
                10: 1.0,  # October
                11: 0.9,  # November
                12: 1.2   # December (holiday gifts)
            },
            'business': {
                1: 1.2, 2: 1.1, 3: 1.0, 4: 1.0, 5: 1.0, 6: 0.9,
                7: 0.8, 8: 0.9, 9: 1.2, 10: 1.1, 11: 1.0, 12: 0.9
            },
            'health': {
                1: 1.4, 2: 1.2, 3: 1.0, 4: 1.0, 5: 1.1, 6: 1.0,
                7: 0.9, 8: 0.9, 9: 1.0, 10: 1.0, 11: 0.9, 12: 0.8
            },
            'romance': {
                1: 0.9, 2: 1.3, 3: 1.0, 4: 1.0, 5: 1.1, 6: 1.2,
                7: 1.1, 8: 1.0, 9: 0.9, 10: 1.0, 11: 0.9, 12: 1.1
            },
            'finance': {
                1: 1.3, 2: 1.1, 3: 1.0, 4: 1.2, 5: 1.0, 6: 0.9,
                7: 0.8, 8: 0.9, 9: 1.1, 10: 1.0, 11: 1.0, 12: 0.9
            }
        }
        
        # Get current month
        current_month = datetime.now().month
        
        # Get seasonal multiplier for category
        category_pattern = seasonal_patterns.get(category, seasonal_patterns['self-help'])
        current_seasonal_factor = category_pattern.get(current_month, 1.0)
        
        # Calculate seasonal opportunity (next 3 months)
        next_months = [(current_month + i - 1) % 12 + 1 for i in range(1, 4)]
        upcoming_factors = [category_pattern.get(month, 1.0) for month in next_months]
        seasonal_opportunity = np.mean(upcoming_factors)
        
        return {
            'current_seasonal_factor': current_seasonal_factor,
            'seasonal_opportunity_3m': seasonal_opportunity,
            'best_month_factor': max(category_pattern.values()),
            'worst_month_factor': min(category_pattern.values()),
            'seasonal_volatility': np.std(list(category_pattern.values()))
        }
    
    async def _calculate_opportunity_score(self, market_data: Dict[str, float]) -> float:
        """Calculate overall market opportunity score"""
        
        # Factors that increase opportunity
        positive_factors = [
            market_data.get('innovation_opportunity', 0.5),
            market_data.get('trend_momentum', 0.5),
            market_data.get('seasonal_opportunity_3m', 1.0),
            1 - market_data.get('competition_density', 0.5),  # Lower competition = higher opportunity
            1 - market_data.get('topic_saturation', 0.5)     # Lower saturation = higher opportunity
        ]
        
        # Weighted opportunity score
        weights = [0.25, 0.2, 0.15, 0.25, 0.15]
        opportunity_score = sum(factor * weight for factor, weight in zip(positive_factors, weights))
        
        return min(max(opportunity_score, 0), 1)  # Clamp to [0, 1]
    
    async def _calculate_market_saturation(self, recent_books: List[Dict]) -> float:
        """Calculate how saturated the market is"""
        
        if len(recent_books) < 10:
            return 0.3  # Low saturation if few books
        
        # Analyze title similarity
        titles = [book.get('title', '') for book in recent_books]
        
        # Simple similarity check (count common words)
        all_words = []
        for title in titles:
            words = set(re.findall(r'\b\w+\b', title.lower()))
            all_words.extend(words)
        
        from collections import Counter
        word_freq = Counter(all_words)
        
        # High repetition of same words = high saturation
        if word_freq:
            total_words = len(all_words)
            top_10_freq = sum(count for word, count in word_freq.most_common(10))
            saturation = top_10_freq / total_words
        else:
            saturation = 0.5
        
        return min(saturation, 1.0)
    
    async def _get_cached_analysis(self, category: str) -> Dict[str, Any]:
        """Get cached market analysis if available and recent"""
        
        db = next(get_db())
        try:
            cutoff_time = datetime.utcnow() - self.cache_duration
            
            analysis = db.query(MarketAnalysis).filter(
                MarketAnalysis.category == category,
                MarketAnalysis.analysis_date >= cutoff_time
            ).order_by(MarketAnalysis.analysis_date.desc()).first()
            
            if analysis:
                return {
                    'competition_density': analysis.competition_density,
                    'market_saturation': analysis.market_saturation_score,
                    'opportunity_score': analysis.opportunity_score,
                    'avg_price': analysis.average_price,
                    'current_seasonal_factor': analysis.seasonal_multipliers.get(str(datetime.now().month), 1.0) if analysis.seasonal_multipliers else 1.0
                }
            
            return None
            
        finally:
            db.close()
    
    async def _cache_market_analysis(self, category: str, market_data: Dict[str, float]):
        """Cache market analysis results"""
        
        db = next(get_db())
        try:
            analysis = MarketAnalysis(
                category=category,
                analysis_date=datetime.utcnow(),
                competition_density=market_data.get('competition_density', 0),
                market_saturation_score=market_data.get('market_saturation', 0),
                opportunity_score=market_data.get('market_opportunity_score', 0),
                average_price=market_data.get('avg_price', 0),
                seasonal_multipliers={str(i): 1.0 for i in range(1, 13)}  # Simplified
            )
            
            db.add(analysis)
            db.commit()
            
        except Exception as e:
            logger.error(f"Failed to cache market analysis: {e}")
            db.rollback()
        finally:
            db.close()