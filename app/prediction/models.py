### app/prediction/models.py - Prediction Models

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, JSON, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from app.models.user import Base

class SalesPrediction(Base):
    __tablename__ = "sales_predictions"

    id = Column(Integer, primary_key=True, index=True)
    book_id = Column(Integer, ForeignKey("books.id"), nullable=False)
    
    # Prediction results
    predicted_sales_30d = Column(Integer, default=0)
    predicted_revenue_30d = Column(Float, default=0.0)
    predicted_sales_90d = Column(Integer, default=0)
    predicted_revenue_90d = Column(Float, default=0.0)
    
    # Confidence and risk
    confidence_score = Column(Float, default=0.0)  # 0-1
    success_probability = Column(Float, default=0.0)  # 0-1
    risk_level = Column(String, default="medium")  # low, medium, high
    
    # Optimal pricing
    recommended_price = Column(Float, default=2.99)
    price_elasticity = Column(Float, default=0.0)
    
    # Market analysis
    market_opportunity_score = Column(Float, default=0.0)
    competition_level = Column(Float, default=0.0)
    market_saturation = Column(Float, default=0.0)
    seasonal_factor = Column(Float, default=1.0)
    
    # Feature importance
    content_quality_score = Column(Float, default=0.0)
    title_appeal_score = Column(Float, default=0.0)
    category_performance_score = Column(Float, default=0.0)
    
    # Predictions metadata
    prediction_model_version = Column(String, default="1.0")
    features_used = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Actual results (for model improvement)
    actual_sales_30d = Column(Integer, nullable=True)
    actual_revenue_30d = Column(Float, nullable=True)
    prediction_accuracy = Column(Float, nullable=True)
    
    # Relationships
    book = relationship("Book", back_populates="predictions")

class MarketAnalysis(Base):
    __tablename__ = "market_analyses"

    id = Column(Integer, primary_key=True, index=True)
    category = Column(String, nullable=False)
    analysis_date = Column(DateTime, default=datetime.utcnow)
    
    # Market metrics
    total_books_published_30d = Column(Integer, default=0)
    average_price = Column(Float, default=0.0)
    price_range_min = Column(Float, default=0.0)
    price_range_max = Column(Float, default=0.0)
    
    # Quality metrics
    average_review_rating = Column(Float, default=0.0)
    average_review_count = Column(Integer, default=0)
    top_performer_threshold = Column(Integer, default=0)
    
    # Competition analysis
    competition_density = Column(Float, default=0.0)  # books per day
    market_saturation_score = Column(Float, default=0.0)
    opportunity_score = Column(Float, default=0.0)
    
    # Trending data
    trending_keywords = Column(JSON, nullable=True)
    emerging_subtopics = Column(JSON, nullable=True)
    declining_topics = Column(JSON, nullable=True)
    
    # Seasonal patterns
    seasonal_multipliers = Column(JSON, nullable=True)  # Month-based multipliers
    best_publishing_months = Column(JSON, nullable=True)

class PredictionAccuracy(Base):
    __tablename__ = "prediction_accuracy"

    id = Column(Integer, primary_key=True, index=True)
    prediction_id = Column(Integer, ForeignKey("sales_predictions.id"), nullable=False)
    
    # Accuracy metrics
    sales_accuracy_percentage = Column(Float, default=0.0)
    revenue_accuracy_percentage = Column(Float, default=0.0)
    overall_accuracy = Column(Float, default=0.0)
    
    # Error analysis
    prediction_error = Column(Float, default=0.0)
    error_type = Column(String, nullable=True)  # overestimate, underestimate
    
    # Model improvement data
    feature_importance_actual = Column(JSON, nullable=True)
    improvement_suggestions = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=datetime.utcnow)