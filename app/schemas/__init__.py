### app/schemas/__init__.py - Schema Exports

from .book import (
    Chapter,
    BookOutline,
    Manuscript,
    PublicationRequest,
    PublicationResult,
    BookCreate,
    BookResponse,
    ManuscriptGenerationRequest,
    FeedbackData
)

from .user import (
    UserBase,
    UserCreate,
    UserLogin,
    UserResponse,
    Token,
    KDPCredentials
)

from .trend import (
    TrendKeyword,
    TrendOpportunity,
    TrendAnalysisRequest,
    TrendAnalysisResult,
    TrendAnalysisResponse,
    MarketAnalysis,
    CompetitorAnalysis
)

from .feedback import (
    FeedbackMetric,
    UserFeedback,
    SalesData,
    ModelPerformance,
    TrainingExample,
    VERLTrainingRequest,
    VERLTrainingResult,
    QualityAssessment,
    RewardCalculation
)

__all__ = [
    # Book schemas
    "Chapter",
    "BookOutline", 
    "Manuscript",
    "PublicationRequest",
    "PublicationResult",
    "BookCreate",
    "BookResponse",
    "ManuscriptGenerationRequest",
    "FeedbackData",
    
    # User schemas
    "UserBase",
    "UserCreate",
    "UserLogin", 
    "UserResponse",
    "Token",
    "KDPCredentials",
    
    # Trend schemas
    "TrendKeyword",
    "TrendOpportunity",
    "TrendAnalysisRequest",
    "TrendAnalysisResult",
    "TrendAnalysisResponse",
    "MarketAnalysis",
    "CompetitorAnalysis",
    
    # Feedback schemas
    "FeedbackMetric",
    "UserFeedback",
    "SalesData", 
    "ModelPerformance",
    "TrainingExample",
    "VERLTrainingRequest",
    "VERLTrainingResult",
    "QualityAssessment",
    "RewardCalculation",
]