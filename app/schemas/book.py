## app/schemas/book.py - Book Schemas

from pydantic import BaseModel, Field, field_validator, computed_field, ConfigDict, field_serializer
from typing import List, Dict, Any, Optional, Literal
from datetime import datetime
import re

class Chapter(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True, validate_assignment=True)
    
    title: str = Field(max_length=200, min_length=1, description="Chapter title")
    content: str = Field(min_length=100, max_length=50000, description="Chapter content")
    word_count: int = Field(ge=50, le=10000, description="Word count for the chapter")
    chapter_number: int = Field(ge=1, le=100, description="Chapter sequence number")
    
    @field_validator('content')
    @classmethod
    def sanitize_content(cls, v: str) -> str:
        """Remove potential script tags and sanitize content"""
        v = re.sub(r'<script.*?</script>', '', v, flags=re.IGNORECASE | re.DOTALL)
        v = re.sub(r'<iframe.*?</iframe>', '', v, flags=re.IGNORECASE | re.DOTALL)
        return v.strip()
    
    @computed_field
    @property
    def estimated_reading_time(self) -> int:
        """Estimated reading time in minutes (250 words/minute)"""
        return max(1, self.word_count // 250)

class BookOutline(BaseModel):
    title: str
    description: str
    category: str
    target_word_count: int
    chapter_outlines: List[Dict[str, Any]]
    keywords: List[str] = []

class Manuscript(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True, validate_assignment=True)
    
    title: str = Field(max_length=500, min_length=1, description="Book title")
    subtitle: str = Field(default="", max_length=500, description="Book subtitle")
    author: str = Field(max_length=200, min_length=1, description="Author name")
    category: str = Field(max_length=100, min_length=1, description="Book category")
    description: str = Field(max_length=5000, min_length=10, description="Book description")
    keywords: List[str] = Field(default=[], max_length=20, description="SEO keywords")
    introduction: str = Field(max_length=10000, description="Book introduction")
    chapters: List[Chapter] = Field(min_length=1, max_length=50, description="Book chapters")
    content: str = Field(min_length=100, description="Full book content")
    conclusion: str = Field(max_length=10000, description="Book conclusion")
    about_author: str = Field(default="", max_length=2000, description="About the author section")
    word_count: int = Field(ge=1000, le=500000, description="Total word count")
    generation_metadata: Dict[str, Any] = Field(default={}, description="Generation metadata")
    source_trend: Dict[str, Any] = Field(description="Source trend data")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    
    @field_serializer('created_at')
    def serialize_created_at(self, dt: datetime) -> str:
        """Serialize datetime to ISO format with timezone"""
        return dt.isoformat() + 'Z'
    
    @field_validator('keywords')
    @classmethod
    def validate_keywords(cls, v: List[str]) -> List[str]:
        """Validate and clean keywords"""
        cleaned = []
        for keyword in v:
            cleaned_keyword = re.sub(r'[^a-zA-Z0-9\s-]', '', keyword.strip().lower())
            if len(cleaned_keyword) >= 2:
                cleaned.append(cleaned_keyword)
        return cleaned[:20]  # Limit to 20 keywords
    
    @computed_field
    @property
    def total_reading_time(self) -> int:
        """Total estimated reading time in minutes"""
        return max(1, self.word_count // 250)
    
    @computed_field
    @property
    def chapter_count(self) -> int:
        """Number of chapters"""
        return len(self.chapters)
    
    @computed_field
    @property
    def is_novel_length(self) -> bool:
        """Whether this is novel length (50k+ words)"""
        return self.word_count >= 50000
    
    @computed_field
    @property
    def is_novella_length(self) -> bool:
        """Whether this is novella length (17.5k-40k words)"""
        return 17500 <= self.word_count < 40000

class PublicationRequest(BaseModel):
    model_config = ConfigDict(str_strip_whitespace=True)
    
    manuscript_id: str = Field(min_length=1, description="Manuscript identifier")
    price: float = Field(ge=0.99, le=99.99, default=2.99, description="Price between $0.99-$99.99")
    royalty_rate: Literal[35, 70] = Field(default=70, description="KDP royalty rate (35% or 70%)")
    publication_date: Optional[datetime] = Field(default=None, description="Scheduled publication date")
    marketing_description: str = Field(default="", max_length=4000, description="Marketing description for KDP")
    auto_publish: bool = Field(default=False, description="If False, save as draft")
    
    @field_validator('price')
    @classmethod
    def validate_kdp_price(cls, v: float) -> float:
        """Validate KDP pricing rules"""
        if v < 2.99 and v != 0.99:
            raise ValueError("Price must be $0.99 or $2.99 or higher for KDP")
        return v
    
    @field_validator('marketing_description')
    @classmethod
    def sanitize_marketing_description(cls, v: str) -> str:
        """Sanitize marketing description"""
        v = re.sub(r'<[^>]+>', '', v)  # Remove HTML tags
        return v.strip()

class PublicationResult(BaseModel):
    success: bool
    kdp_id: Optional[str] = None
    publication_url: Optional[str] = None
    error: Optional[str] = None
    status: str  # 'draft', 'published', 'failed'

class BookCreate(BaseModel):
    title: str
    category: str
    description: Optional[str] = None
    target_audience: str = "general adults"
    writing_style: str = "professional"
    ai_provider: str = "openai"

class BookResponse(BaseModel):
    id: int
    title: str
    subtitle: Optional[str] = None
    author: str
    category: str
    description: Optional[str] = None
    status: str
    word_count: int
    chapter_count: int
    quality_score: Optional[float] = None
    created_at: datetime
    approved_at: Optional[datetime] = None
    published_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class ManuscriptGenerationRequest(BaseModel):
    title: Optional[str] = None
    category: str
    target_audience: str = "general adults"
    writing_style: str = "professional"
    ai_provider: str = "openai"
    trend_data: Dict[str, Any]
    output_formats: List[str] = ["docx", "epub", "pdf"]
    layout_theme: Optional[str] = None

class FeedbackData(BaseModel):
    book_id: int
    approved: bool
    approval_time_seconds: float = 0.0
    rejection_reason: Optional[str] = None
    quality_rating: Optional[int] = None
    user_notes: Optional[str] = None