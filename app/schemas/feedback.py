### app/schemas/feedback.py - Feedback and VERL Training Schemas

from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from datetime import datetime

class FeedbackMetric(BaseModel):
    book_id: int
    metric_type: str  # 'user_approval', 'quality_score', 'sales_performance'
    value: float
    metadata: Dict[str, Any] = {}
    timestamp: datetime = datetime.now()

class UserFeedback(BaseModel):
    book_id: int
    user_id: int
    approved: bool
    approval_time_seconds: float = 0.0
    rejection_reason: Optional[str] = None
    quality_rating: Optional[int] = None  # 1-5 scale
    user_notes: Optional[str] = None
    timestamp: datetime = datetime.now()

class SalesData(BaseModel):
    book_id: int
    sales_units: int
    revenue: float
    average_rating: float
    reviews_count: int
    royalty_earned: float
    reporting_period: str  # 'daily', 'weekly', 'monthly'
    timestamp: datetime = datetime.now()

class ModelPerformance(BaseModel):
    book_id: int
    generation_config: Dict[str, Any]
    prompt_hash: str  # Hash of the prompt used
    output_hash: str  # Hash of the generated content
    reward_signal: float
    user_approval: bool
    quality_score: float
    sales_performance: Optional[float] = None
    training_round: int = 0
    timestamp: datetime = datetime.now()

class TrainingExample(BaseModel):
    prompt: str
    output: str
    reward: float
    context: Dict[str, Any]  # Category, word count, etc.
    source_book_id: Optional[int] = None
    timestamp: datetime = datetime.now()

class VERLTrainingRequest(BaseModel):
    min_examples: int = 50
    max_examples: int = 1000
    training_epochs: int = 3
    learning_rate: float = 1e-5
    batch_size: int = 8
    model_name: str = "microsoft/DialoGPT-medium"
    
class VERLTrainingResult(BaseModel):
    training_id: str
    total_examples: int
    training_epochs: int
    final_loss: float
    performance_metrics: Dict[str, float]
    model_path: str
    training_duration: float
    timestamp: datetime = datetime.now()

class QualityAssessment(BaseModel):
    book_id: int
    readability_score: float
    engagement_factor: float
    actionability_score: float
    length_appropriateness: float
    overall_quality: float
    automated_flags: List[str] = []  # Issues detected automatically
    timestamp: datetime = datetime.now()

class RewardCalculation(BaseModel):
    book_id: int
    user_approval_reward: float
    quality_reward: float
    sales_reward: float
    final_reward: float
    normalization_method: str
    calculation_timestamp: datetime = datetime.now()