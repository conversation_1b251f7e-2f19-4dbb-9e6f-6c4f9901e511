### app/schemas/trend.py - Trend Analysis Schemas

from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from datetime import datetime

class TrendKeyword(BaseModel):
    keyword: str
    search_volume: int
    competition: str  # 'low', 'medium', 'high'
    trend_direction: str  # 'rising', 'stable', 'declining'
    related_keywords: List[str] = []

class TrendOpportunity(BaseModel):
    title_suggestion: str
    category: str
    keywords: List[str]
    market_size: int
    competition_level: str
    profit_potential: float
    content_difficulty: str  # 'easy', 'medium', 'hard'
    recommended_price: float
    target_word_count: int

class TrendAnalysisRequest(BaseModel):
    categories: List[str] = ["self-help", "business", "health", "technology"]
    max_results: int = 10
    min_search_volume: int = 1000
    max_competition: str = "medium"
    analysis_depth: str = "standard"  # 'quick', 'standard', 'deep'

class TrendAnalysisResult(BaseModel):
    analysis_id: str
    keywords_analyzed: List[TrendKeyword]
    opportunities: List[TrendOpportunity]
    market_insights: Dict[str, Any]
    recommendation_score: float
    analysis_date: datetime
    source_platforms: List[str] = ["google_trends", "amazon_kdp", "goodreads"]

class TrendAnalysisResponse(BaseModel):
    id: int
    status: str  # 'analyzing', 'completed', 'failed'
    total_opportunities: int
    best_opportunity: Optional[TrendOpportunity] = None
    analysis_result: Optional[TrendAnalysisResult] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class MarketAnalysis(BaseModel):
    category: str
    total_books: int
    average_rating: float
    average_price: float
    bestseller_characteristics: Dict[str, Any]
    saturation_level: str  # 'low', 'medium', 'high'
    entry_difficulty: str  # 'easy', 'medium', 'hard'
    
class CompetitorAnalysis(BaseModel):
    title: str
    author: str
    price: float
    rating: float
    reviews_count: int
    publication_date: datetime
    strengths: List[str]
    weaknesses: List[str]
    differentiation_opportunities: List[str]