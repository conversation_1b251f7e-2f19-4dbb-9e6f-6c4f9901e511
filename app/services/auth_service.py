### app/services/auth_service.py - Authentication Service

from typing import Optional, Dict, Any
from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
import bcrypt
import jwt
import logging
from cryptography.fernet import Fernet
import base64
import os

from app.models.user import User
from app.schemas.user import UserCreate, UserResponse, UserLogin, KDPCredentials
from app.database import get_db_context

logger = logging.getLogger(__name__)

class AuthService:
    """Service for managing authentication and user accounts"""
    
    def __init__(self):
        # JWT configuration
        self.jwt_secret = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this")
        self.jwt_algorithm = "HS256"
        self.jwt_expiration_hours = 24
        
        # Encryption for sensitive data
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for sensitive data"""
        key_env = os.getenv("ENCRYPTION_KEY")
        if key_env:
            return base64.urlsafe_b64decode(key_env.encode())
        else:
            # Generate new key (in production, store this securely)
            key = Fernet.generate_key()
            logger.warning("Generated new encryption key. Store ENCRYPTION_KEY in environment variables.")
            return key
    
    async def register_user(self, user_data: UserCreate) -> Dict[str, Any]:
        """Register a new user"""
        
        with get_db_context() as db:
            # Check if user already exists
            existing_user = db.query(User).filter(
                User.email == user_data.email
            ).first()
            
            if existing_user:
                return {
                    "success": False,
                    "error": "User with this email already exists"
                }
            
            # Hash password
            password_hash = self._hash_password(user_data.password)
            
            # Create user
            user = User(
                email=user_data.email,
                password_hash=password_hash,
                full_name=user_data.full_name,
                subscription_tier=user_data.subscription_tier or "free",
                is_active=True,
                created_at=datetime.now(timezone.utc)
            )
            
            db.add(user)
            db.flush()  # Get the ID
            
            # Generate JWT token
            token = self._generate_jwt_token(user.id, user.email)
            
            logger.info(f"Registered new user: {user.email} (ID: {user.id})")
            
            return {
                "success": True,
                "user": UserResponse.model_validate(user),
                "access_token": token,
                "token_type": "bearer"
            }
    
    async def login_user(self, login_data: UserLogin) -> Dict[str, Any]:
        """Authenticate user login"""
        
        with get_db_context() as db:
            user = db.query(User).filter(
                User.email == login_data.email
            ).first()
            
            if not user:
                return {
                    "success": False,
                    "error": "Invalid email or password"
                }
            
            if not user.is_active:
                return {
                    "success": False,
                    "error": "Account is deactivated"
                }
            
            # Verify password
            if not self._verify_password(login_data.password, user.password_hash):
                return {
                    "success": False,
                    "error": "Invalid email or password"
                }
            
            # Update last login using proper SQLAlchemy update
            from sqlalchemy import update
            db.execute(
                update(User)
                .where(User.id == user.id)
                .values(last_login_at=datetime.now(timezone.utc))
            )

            # Generate JWT token - get fresh values
            user_id = user.id
            user_email = user.email
            token = self._generate_jwt_token(user_id, user_email)
            
            logger.info(f"User logged in: {user.email}")
            
            return {
                "success": True,
                "user": UserResponse.model_validate(user),
                "access_token": token,
                "token_type": "bearer"
            }
    
    async def get_current_user(self, token: str) -> Optional[UserResponse]:
        """Get current user from JWT token"""
        
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            user_id = payload.get("user_id")
            
            if not user_id:
                return None
            
            with get_db_context() as db:
                user = db.query(User).filter(User.id == user_id).first()
                
                if user and user.is_active:
                    return UserResponse.model_validate(user)
                    
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token expired")
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT token")
        except Exception as e:
            logger.error(f"Error validating token: {str(e)}")
        
        return None
    
    async def change_password(
        self, 
        user_id: int, 
        current_password: str, 
        new_password: str
    ) -> Dict[str, Any]:
        """Change user password"""
        
        with get_db_context() as db:
            user = db.query(User).filter(User.id == user_id).first()
            
            if not user:
                return {"success": False, "error": "User not found"}
            
            # Verify current password
            if not self._verify_password(current_password, user.password_hash):
                return {"success": False, "error": "Current password is incorrect"}
            
            # Update password using proper SQLAlchemy update
            from sqlalchemy import update
            db.execute(
                update(User)
                .where(User.id == user_id)
                .values(password_hash=self._hash_password(new_password))
            )
            
            logger.info(f"Password changed for user {user.email}")
            
            return {
                "success": True,
                "message": "Password changed successfully"
            }
    
    async def update_user_profile(
        self, 
        user_id: int, 
        profile_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update user profile information"""
        
        with get_db_context() as db:
            user = db.query(User).filter(User.id == user_id).first()
            
            if not user:
                return {"success": False, "error": "User not found"}
            
            # Update allowed fields
            allowed_fields = ["full_name", "subscription_tier"]
            updated_fields = []
            
            for field in allowed_fields:
                if field in profile_data:
                    setattr(user, field, profile_data[field])
                    updated_fields.append(field)
            
            if updated_fields:
                logger.info(f"Updated profile for user {user.email}: {updated_fields}")
            
            return {
                "success": True,
                "user": UserResponse.model_validate(user),
                "updated_fields": updated_fields
            }
    
    async def store_kdp_credentials(
        self, 
        user_id: int, 
        credentials: KDPCredentials
    ) -> Dict[str, Any]:
        """Store encrypted KDP credentials"""
        
        with get_db_context() as db:
            user = db.query(User).filter(User.id == user_id).first()
            
            if not user:
                return {"success": False, "error": "User not found"}
            
            try:
                # Encrypt sensitive credentials
                encrypted_creds = {
                    "email": self._encrypt_data(credentials.email),
                    "password": self._encrypt_data(credentials.password),
                    "two_factor_secret": self._encrypt_data(credentials.two_factor_secret) if credentials.two_factor_secret else None
                }
                
                # Update KDP credentials using proper SQLAlchemy update
                from sqlalchemy import update
                db.execute(
                    update(User)
                    .where(User.id == user_id)
                    .values(
                        kdp_credentials=encrypted_creds,
                        kdp_credentials_updated_at=datetime.now(timezone.utc)
                    )
                )
                
                logger.info(f"Stored KDP credentials for user {user.email}")
                
                return {
                    "success": True,
                    "message": "KDP credentials stored successfully"
                }
                
            except Exception as e:
                logger.error(f"Failed to encrypt KDP credentials: {str(e)}")
                return {"success": False, "error": "Failed to store credentials"}
    
    async def get_kdp_credentials(self, user_id: int) -> Optional[KDPCredentials]:
        """Retrieve and decrypt KDP credentials"""
        
        with get_db_context() as db:
            user = db.query(User).filter(User.id == user_id).first()
            
            if not user or not user.kdp_credentials:
                return None
            
            try:
                # Decrypt credentials
                encrypted_creds = user.kdp_credentials
                
                credentials = KDPCredentials(
                    email=self._decrypt_data(encrypted_creds["email"]),
                    password=self._decrypt_data(encrypted_creds["password"]),
                    two_factor_secret=self._decrypt_data(encrypted_creds["two_factor_secret"]) if encrypted_creds.get("two_factor_secret") else None
                )
                
                return credentials
                
            except Exception as e:
                logger.error(f"Failed to decrypt KDP credentials: {str(e)}")
                return None
    
    async def delete_kdp_credentials(self, user_id: int) -> Dict[str, Any]:
        """Delete stored KDP credentials"""
        
        with get_db_context() as db:
            user = db.query(User).filter(User.id == user_id).first()
            
            if not user:
                return {"success": False, "error": "User not found"}
            
            # Delete KDP credentials using proper SQLAlchemy update
            from sqlalchemy import update
            db.execute(
                update(User)
                .where(User.id == user_id)
                .values(kdp_credentials=None, kdp_credentials_updated_at=None)
            )
            
            logger.info(f"Deleted KDP credentials for user {user.email}")
            
            return {
                "success": True,
                "message": "KDP credentials deleted successfully"
            }
    
    async def deactivate_user(self, user_id: int) -> Dict[str, Any]:
        """Deactivate user account"""
        
        with get_db_context() as db:
            user = db.query(User).filter(User.id == user_id).first()
            
            if not user:
                return {"success": False, "error": "User not found"}
            
            # Deactivate user using proper SQLAlchemy update
            from sqlalchemy import update
            db.execute(
                update(User)
                .where(User.id == user_id)
                .values(is_active=False)
            )
            
            logger.info(f"Deactivated user account: {user.email}")
            
            return {
                "success": True,
                "message": "Account deactivated successfully"
            }
    
    def _hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    def _generate_jwt_token(self, user_id: int, email: str) -> str:
        """Generate JWT token"""
        payload = {
            "user_id": user_id,
            "email": email,
            "exp": datetime.now(timezone.utc) + timedelta(hours=self.jwt_expiration_hours),
            "iat": datetime.now(timezone.utc)
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    def _encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data"""
        return self.cipher_suite.encrypt(data.encode()).decode()
    
    def _decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        return self.cipher_suite.decrypt(encrypted_data.encode()).decode()
    
    async def get_user_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get user account statistics"""
        
        with get_db_context() as db:
            user = db.query(User).filter(User.id == user_id).first()
            
            if not user:
                return {"error": "User not found"}
            
            # Get related statistics
            from app.models.book import Book
            from app.models.publication import Publication
            
            total_books = db.query(Book).filter(Book.user_id == user_id).count()
            published_books = db.query(Book).filter(
                Book.user_id == user_id,
                Book.status == "published"
            ).count()
            
            total_publications = db.query(Publication).join(Book).filter(
                Book.user_id == user_id
            ).count()
            
            return {
                "user_id": user.id,
                "email": user.email,
                "full_name": user.full_name,
                "subscription_tier": user.subscription_tier,
                "account_created": user.created_at,
                "last_login": user.last_login_at,
                "total_books": total_books,
                "published_books": published_books,
                "total_publications": total_publications,
                "has_kdp_credentials": user.kdp_credentials is not None
            }