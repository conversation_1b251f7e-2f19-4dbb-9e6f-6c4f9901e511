### app/services/book_service.py - Book Business Logic Service

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import asyncio
from pathlib import Path

from app.models.book import Book, Chapter
from app.models.user import User
from app.schemas.book import BookCreate, BookResponse, ManuscriptGenerationRequest, Manuscript
from app.agents.pydantic_ai_manager import execute_agent
from app.utils.layout_designer import ProfessionalLayoutDesigner
from app.ml.feedback_integration import LiveFeedbackCollector
from app.database import get_db_context

logger = logging.getLogger(__name__)

class BookService:
    """Service for managing book operations"""
    
    def __init__(self):
        # Using PydanticAI agents through manager
        self.layout_designer = ProfessionalLayoutDesigner()
    
    async def create_book(self, book_data: BookCreate, user_id: int) -> BookResponse:
        """Create a new book record"""
        
        with get_db_context() as db:
            book = Book(
                user_id=user_id,
                title=book_data.title,
                category=book_data.category,
                description=book_data.description,
                status="draft",
                generation_config={
                    "target_audience": book_data.target_audience,
                    "writing_style": book_data.writing_style,
                    "ai_provider": book_data.ai_provider
                }
            )
            
            db.add(book)
            db.flush()  # Get the ID
            
            logger.info(f"Created new book: {book.title} (ID: {book.id})")
            return BookResponse.model_validate(book)
    
    async def get_user_books(self, user_id: int, skip: int = 0, limit: int = 100) -> List[BookResponse]:
        """Get all books for a user"""
        
        with get_db_context() as db:
            books = db.query(Book).filter(
                Book.user_id == user_id
            ).offset(skip).limit(limit).all()
            
            return [BookResponse.model_validate(book) for book in books]
    
    async def get_book_by_id(self, book_id: int, user_id: int) -> Optional[BookResponse]:
        """Get a specific book by ID"""
        
        with get_db_context() as db:
            book = db.query(Book).filter(
                Book.id == book_id,
                Book.user_id == user_id
            ).first()
            
            if book:
                return BookResponse.model_validate(book)
            return None
    
    async def generate_manuscript(
        self, 
        request: ManuscriptGenerationRequest, 
        user_id: int
    ) -> Dict[str, Any]:
        """Generate a complete manuscript using AI"""
        
        try:
            # Create book record
            book_data = BookCreate(
                title=request.title or "AI Generated Book",
                category=request.category,
                target_audience=request.target_audience,
                writing_style=request.writing_style,
                ai_provider=request.ai_provider
            )
            
            book_response = await self.create_book(book_data, user_id)
            book_id = book_response.id
            
            # Update status to generating
            await self._update_book_status(book_id, "generating")
            
            # Generate manuscript using PydanticAI agent
            task_data = {
                'trend_data': request.trend_data,
                'style': request.writing_style,
                'target_audience': request.target_audience,
                'target_length': 8000,
                'output_formats': request.output_formats
            }
            result = await execute_agent("manuscript_generator", task_data, user_id)
            
            if result.success:
                manuscript_data = result.data['manuscript']
                
                # Update book with generated content
                await self._update_book_with_manuscript(book_id, manuscript_data, result.data)
                
                # Update status to awaiting approval
                await self._update_book_status(book_id, "awaiting_approval")
                
                logger.info(f"Successfully generated manuscript for book {book_id}")
                
                return {
                    "success": True,
                    "book_id": book_id,
                    "manuscript": manuscript_data,
                    "file_paths": result.data.get('file_paths', {}),
                    "formatted_files": result.data.get('formatted_files', {}),
                    "quality_score": result.data.get('quality_score', 0),
                    "word_count": result.data.get('word_count', 0),
                    "layout_theme": result.data.get('layout_theme'),
                    "status": "awaiting_approval"
                }
            else:
                # Update status to failed
                await self._update_book_status(book_id, "failed", result.error)
                
                return {
                    "success": False,
                    "book_id": book_id,
                    "error": result.error,
                    "status": "failed"
                }
                
        except Exception as e:
            logger.error(f"Manuscript generation failed: {str(e)}")
            if 'book_id' in locals():
                await self._update_book_status(book_id, "failed", str(e))
            
            return {
                "success": False,
                "error": str(e),
                "status": "failed"
            }
    
    async def approve_manuscript(self, book_id: int, user_id: int, approval_time: float = 0.0) -> Dict[str, Any]:
        """Approve a generated manuscript"""
        
        with get_db_context() as db:
            book = db.query(Book).filter(
                Book.id == book_id,
                Book.user_id == user_id
            ).first()
            
            if not book:
                return {"success": False, "error": "Book not found"}
            
            if book.status != "awaiting_approval":
                return {"success": False, "error": "Book is not ready for approval"}
            
            # Update book status
            book.status = "approved"
            book.approved_at = datetime.utcnow()
            
            # Capture feedback for VERL training
            feedback_collector = LiveFeedbackCollector()
            await feedback_collector.capture_approval_feedback(
                book_id=book_id,
                approved=True,
                approval_time_seconds=approval_time
            )
            
            logger.info(f"Approved manuscript for book {book_id}")
            
            return {
                "success": True,
                "book_id": book_id,
                "status": "approved",
                "message": "Manuscript approved successfully"
            }
    
    async def reject_manuscript(
        self, 
        book_id: int, 
        user_id: int, 
        reason: Optional[str] = None,
        rejection_time: float = 0.0
    ) -> Dict[str, Any]:
        """Reject a generated manuscript"""
        
        with get_db_context() as db:
            book = db.query(Book).filter(
                Book.id == book_id,
                Book.user_id == user_id
            ).first()
            
            if not book:
                return {"success": False, "error": "Book not found"}
            
            # Update book status
            book.status = "rejected"
            book.rejection_reason = reason
            
            # Capture feedback for VERL training
            feedback_collector = LiveFeedbackCollector()
            await feedback_collector.capture_approval_feedback(
                book_id=book_id,
                approved=False,
                approval_time_seconds=rejection_time,
                rejection_reason=reason
            )
            
            logger.info(f"Rejected manuscript for book {book_id}: {reason}")
            
            return {
                "success": True,
                "book_id": book_id,
                "status": "rejected",
                "message": "Manuscript rejected",
                "reason": reason
            }
    
    async def regenerate_manuscript(
        self, 
        book_id: int, 
        user_id: int, 
        modifications: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Regenerate manuscript with optional modifications"""
        
        with get_db_context() as db:
            book = db.query(Book).filter(
                Book.id == book_id,
                Book.user_id == user_id
            ).first()
            
            if not book:
                return {"success": False, "error": "Book not found"}
            
            # Create modified generation request
            original_config = book.generation_config or {}
            modified_config = {**original_config, **(modifications or {})}
            
            request = ManuscriptGenerationRequest(
                title=book.title,
                category=book.category,
                target_audience=modified_config.get("target_audience", "general adults"),
                writing_style=modified_config.get("writing_style", "professional"),
                ai_provider=modified_config.get("ai_provider", "openai"),
                trend_data=modified_config.get("trend_data", {}),
                output_formats=modified_config.get("output_formats", ["docx", "epub", "pdf"]),
                layout_theme=modified_config.get("layout_theme")
            )
            
            # Reset book status
            book.status = "generating"
            book.approved_at = None
            book.rejection_reason = None
            
            # Generate new manuscript
            return await self.generate_manuscript(request, user_id)
    
    async def delete_book(self, book_id: int, user_id: int) -> Dict[str, Any]:
        """Delete a book and its associated files"""
        
        with get_db_context() as db:
            book = db.query(Book).filter(
                Book.id == book_id,
                Book.user_id == user_id
            ).first()
            
            if not book:
                return {"success": False, "error": "Book not found"}
            
            # Delete associated files if they exist
            if book.manuscript_path:
                try:
                    manuscript_path = Path(book.manuscript_path)
                    if manuscript_path.exists():
                        manuscript_path.unlink()
                except Exception as e:
                    logger.warning(f"Could not delete manuscript file: {e}")
            
            # Delete cover files
            if book.cover_paths:
                for cover_path in book.cover_paths.values():
                    try:
                        cover_file = Path(cover_path)
                        if cover_file.exists():
                            cover_file.unlink()
                    except Exception as e:
                        logger.warning(f"Could not delete cover file: {e}")
            
            # Delete book record (cascades to related records)
            db.delete(book)
            
            logger.info(f"Deleted book {book_id}")
            
            return {
                "success": True,
                "book_id": book_id,
                "message": "Book deleted successfully"
            }
    
    async def get_book_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get statistics about user's books"""
        
        with get_db_context() as db:
            books = db.query(Book).filter(Book.user_id == user_id).all()
            
            total_books = len(books)
            status_counts = {}
            total_word_count = 0
            avg_quality_score = 0
            
            for book in books:
                status = book.status
                status_counts[status] = status_counts.get(status, 0) + 1
                total_word_count += book.word_count or 0
                
            if books:
                quality_scores = [book.quality_score for book in books if book.quality_score]
                avg_quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            
            return {
                "total_books": total_books,
                "status_breakdown": status_counts,
                "total_word_count": total_word_count,
                "average_quality_score": round(avg_quality_score, 2),
                "approved_books": status_counts.get("approved", 0),
                "published_books": status_counts.get("published", 0)
            }
    
    async def _update_book_status(self, book_id: int, status: str, error_message: Optional[str] = None):
        """Update book status"""
        
        with get_db_context() as db:
            book = db.query(Book).filter(Book.id == book_id).first()
            if book:
                book.status = status
                if error_message:
                    book.rejection_reason = error_message
    
    async def _update_book_with_manuscript(
        self, 
        book_id: int, 
        manuscript_data: Dict[str, Any], 
        generation_result: Dict[str, Any]
    ):
        """Update book record with generated manuscript data"""
        
        with get_db_context() as db:
            book = db.query(Book).filter(Book.id == book_id).first()
            if not book:
                return
            
            # Update book metadata
            if isinstance(manuscript_data, dict):
                book.title = manuscript_data.get('title', book.title)
                book.word_count = manuscript_data.get('word_count', 0)
                book.chapter_count = len(manuscript_data.get('chapters', []))
            else:
                # Manuscript object
                book.title = manuscript_data.title
                book.word_count = manuscript_data.word_count
                book.chapter_count = len(manuscript_data.chapters)
            
            book.quality_score = generation_result.get('quality_score', 0)
            
            # Store file paths
            file_paths = generation_result.get('file_paths', {})
            book.manuscript_path = file_paths.get('markdown')
            
            # Store cover paths if generated
            formatted_files = generation_result.get('formatted_files', {})
            if formatted_files:
                book.cover_paths = {
                    format_type: path for format_type, path in formatted_files.items()
                }
            
            # Store chapters if it's a Manuscript object with chapters
            if hasattr(manuscript_data, 'chapters') and manuscript_data.chapters:
                # Clear existing chapters
                db.query(Chapter).filter(Chapter.book_id == book_id).delete()
                
                # Add new chapters
                for chapter_data in manuscript_data.chapters:
                    chapter = Chapter(
                        book_id=book_id,
                        chapter_number=chapter_data.chapter_number,
                        title=chapter_data.title,
                        content=chapter_data.content,
                        word_count=chapter_data.word_count
                    )
                    db.add(chapter)