### app/services/publication_service.py - Publication Management Service

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime
import logging
import asyncio

from app.models.publication import Publication, SalesData
from app.models.book import Book
from app.models.user import User
from app.schemas.book import PublicationRequest, PublicationResult
from app.agents.pydantic_ai_manager import execute_agent
from app.ml.feedback_integration import LiveFeedbackCollector
from app.database import get_db_context

logger = logging.getLogger(__name__)

class PublicationService:
    """Service for managing book publications"""
    
    def __init__(self):
        # Using PydanticAI agents through manager
        pass
    
    async def publish_book(
        self, 
        book_id: int, 
        user_id: int, 
        publication_request: PublicationRequest
    ) -> Dict[str, Any]:
        """Publish a book to KDP"""
        
        with get_db_context() as db:
            # Verify book exists and is approved
            book = db.query(Book).filter(
                Book.id == book_id,
                Book.user_id == user_id,
                Book.status == "approved"
            ).first()
            
            if not book:
                return {
                    "success": False,
                    "error": "Book not found or not approved for publication"
                }
            
            # Create publication record
            publication = Publication(
                book_id=book_id,
                status="publishing",
                price=publication_request.price,
                royalty_rate=publication_request.royalty_rate,
                publication_date=publication_request.publication_date or datetime.utcnow(),
                marketing_description=publication_request.marketing_description,
                auto_publish=publication_request.auto_publish
            )
            
            db.add(publication)
            db.flush()
            
            publication_id = publication.id
            
            logger.info(f"Starting publication process for book {book_id}")
            
            # Start background publication
            asyncio.create_task(
                self._run_publication_process(publication_id, book, publication_request)
            )
            
            return {
                "success": True,
                "publication_id": publication_id,
                "book_id": book_id,
                "status": "publishing",
                "message": "Publication process started"
            }
    
    async def get_publication_status(
        self, 
        publication_id: int, 
        user_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get publication status"""
        
        with get_db_context() as db:
            publication = db.query(Publication).join(Book).filter(
                Publication.id == publication_id,
                Book.user_id == user_id
            ).first()
            
            if not publication:
                return None
            
            return {
                "publication_id": publication.id,
                "book_id": publication.book_id,
                "status": publication.status,
                "kdp_id": publication.kdp_id,
                "publication_url": publication.publication_url,
                "price": float(publication.price) if publication.price else None,
                "royalty_rate": publication.royalty_rate,
                "created_at": publication.created_at,
                "published_at": publication.published_at,
                "error_message": publication.error_message
            }
    
    async def get_user_publications(
        self, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get all publications for a user"""
        
        with get_db_context() as db:
            publications = db.query(Publication).join(Book).filter(
                Book.user_id == user_id
            ).order_by(Publication.created_at.desc()).offset(skip).limit(limit).all()
            
            results = []
            for pub in publications:
                pub_data = {
                    "publication_id": pub.id,
                    "book_id": pub.book_id,
                    "book_title": pub.book.title,
                    "status": pub.status,
                    "kdp_id": pub.kdp_id,
                    "publication_url": pub.publication_url,
                    "price": float(pub.price) if pub.price else None,
                    "royalty_rate": pub.royalty_rate,
                    "created_at": pub.created_at,
                    "published_at": pub.published_at
                }
                
                # Add latest sales data if available
                latest_sales = db.query(SalesData).filter(
                    SalesData.book_id == pub.book_id
                ).order_by(SalesData.report_date.desc()).first()
                
                if latest_sales:
                    pub_data["latest_sales"] = {
                        "sales_units": latest_sales.sales_units,
                        "revenue": float(latest_sales.revenue),
                        "royalties": float(latest_sales.royalties),
                        "average_rating": latest_sales.average_rating,
                        "reviews_count": latest_sales.reviews_count,
                        "report_date": latest_sales.report_date
                    }
                
                results.append(pub_data)
            
            return results
    
    async def update_publication_price(
        self, 
        publication_id: int, 
        user_id: int, 
        new_price: float
    ) -> Dict[str, Any]:
        """Update publication price"""
        
        with get_db_context() as db:
            publication = db.query(Publication).join(Book).filter(
                Publication.id == publication_id,
                Book.user_id == user_id
            ).first()
            
            if not publication:
                return {"success": False, "error": "Publication not found"}
            
            if publication.status != "published":
                return {"success": False, "error": "Can only update price for published books"}
            
            # Update price using PydanticAI KDP agent
            try:
                from app.agents.pydantic_ai_manager import execute_agent
                task_data = {
                    "title": publication.book.title if publication.book else "Book",
                    "author": "Author",
                    "description": "Price update",
                    "genre": "general",
                    "keywords": [],
                    "manuscript_file": "",
                    "cover_file": "",
                    "action": "update_price",
                    "kdp_id": publication.kdp_id,
                    "new_price": new_price
                }
                result = await execute_agent("kdp_uploader", task_data, user_id)
                
                if result.success:
                    publication.price = new_price
                    
                    logger.info(f"Updated price for publication {publication_id} to ${new_price}")
                    
                    return {
                        "success": True,
                        "publication_id": publication_id,
                        "new_price": new_price,
                        "message": "Price updated successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Failed to update price: {result.error}"
                    }
                    
            except Exception as e:
                logger.error(f"Price update failed: {str(e)}")
                return {"success": False, "error": str(e)}
    
    async def refresh_sales_data(self, publication_id: int, user_id: int) -> Dict[str, Any]:
        """Refresh sales data for a publication"""
        
        with get_db_context() as db:
            publication = db.query(Publication).join(Book).filter(
                Publication.id == publication_id,
                Book.user_id == user_id
            ).first()
            
            if not publication:
                return {"success": False, "error": "Publication not found"}
            
            if not publication.kdp_id:
                return {"success": False, "error": "No KDP ID available for sales monitoring"}
            
            try:
                # Get latest sales data using PydanticAI agent
                from app.agents.pydantic_ai_manager import execute_agent
                task_data = {
                    "date_range": "last_30_days",
                    "include_page_reads": True,
                    "generate_insights": True
                }
                result = await execute_agent("sales_monitor", task_data, user_id)
                
                if result.success:
                    sales_data = result.data.get('sales_data', {})
                    
                    # Store sales data
                    await self._store_sales_data(publication.book_id, sales_data)
                    
                    # Capture feedback for VERL training
                    await LiveFeedbackCollector.capture_sales_feedback(
                        book_id=publication.book_id,
                        sales_data=sales_data
                    )
                    
                    return {
                        "success": True,
                        "publication_id": publication_id,
                        "sales_data": sales_data,
                        "message": "Sales data refreshed successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Failed to refresh sales data: {result.error}"
                    }
                    
            except Exception as e:
                logger.error(f"Sales data refresh failed: {str(e)}")
                return {"success": False, "error": str(e)}
    
    async def unpublish_book(self, publication_id: int, user_id: int) -> Dict[str, Any]:
        """Unpublish a book from KDP"""
        
        with get_db_context() as db:
            publication = db.query(Publication).join(Book).filter(
                Publication.id == publication_id,
                Book.user_id == user_id
            ).first()
            
            if not publication:
                return {"success": False, "error": "Publication not found"}
            
            if publication.status != "published":
                return {"success": False, "error": "Book is not currently published"}
            
            try:
                # Unpublish using PydanticAI KDP agent
                from app.agents.pydantic_ai_manager import execute_agent
                task_data = {
                    "title": publication.book.title if publication.book else "Book",
                    "author": "Author",
                    "description": "Unpublish",
                    "genre": "general",
                    "keywords": [],
                    "manuscript_file": "",
                    "cover_file": "",
                    "action": "unpublish",
                    "kdp_id": publication.kdp_id
                }
                result = await execute_agent("kdp_uploader", task_data, user_id)
                
                if result.success:
                    publication.status = "unpublished"
                    publication.book.status = "approved"  # Reset book status
                    
                    logger.info(f"Unpublished book {publication.book_id}")
                    
                    return {
                        "success": True,
                        "publication_id": publication_id,
                        "message": "Book unpublished successfully"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Failed to unpublish: {result.error}"
                    }
                    
            except Exception as e:
                logger.error(f"Unpublish failed: {str(e)}")
                return {"success": False, "error": str(e)}
    
    async def get_sales_summary(self, user_id: int, days: int = 30) -> Dict[str, Any]:
        """Get sales summary for user's publications"""
        
        with get_db_context() as db:
            # Get user's published books
            published_books = db.query(Book).filter(
                Book.user_id == user_id,
                Book.status == "published"
            ).all()
            
            total_sales = 0
            total_revenue = 0.0
            total_royalties = 0.0
            book_count = len(published_books)
            
            best_performing_book = None
            best_performance = 0
            
            for book in published_books:
                # Get latest sales data
                latest_sales = db.query(SalesData).filter(
                    SalesData.book_id == book.id
                ).order_by(SalesData.report_date.desc()).first()
                
                if latest_sales:
                    total_sales += latest_sales.sales_units
                    total_revenue += latest_sales.revenue
                    total_royalties += latest_sales.royalties
                    
                    # Check if this is the best performing book
                    performance_score = latest_sales.sales_units + (latest_sales.revenue * 0.1)
                    if performance_score > best_performance:
                        best_performance = performance_score
                        best_performing_book = {
                            "title": book.title,
                            "book_id": book.id,
                            "sales_units": latest_sales.sales_units,
                            "revenue": float(latest_sales.revenue),
                            "average_rating": latest_sales.average_rating
                        }
            
            return {
                "total_published_books": book_count,
                "total_sales_units": total_sales,
                "total_revenue": round(total_revenue, 2),
                "total_royalties": round(total_royalties, 2),
                "average_revenue_per_book": round(total_revenue / book_count, 2) if book_count > 0 else 0,
                "best_performing_book": best_performing_book
            }
    
    async def _run_publication_process(
        self, 
        publication_id: int, 
        book: Book, 
        publication_request: PublicationRequest
    ):
        """Run publication process in background"""
        
        try:
            # Prepare publication data
            publication_data = {
                "book_id": book.id,
                "title": book.title,
                "category": book.category,
                "description": book.description,
                "manuscript_path": book.manuscript_path,
                "cover_paths": book.cover_paths,
                "price": publication_request.price,
                "royalty_rate": publication_request.royalty_rate,
                "marketing_description": publication_request.marketing_description,
                "auto_publish": publication_request.auto_publish
            }
            
            # Execute publication using PydanticAI KDP agent
            from app.agents.pydantic_ai_manager import execute_agent
            task_data = {
                "title": publication_data["title"],
                "author": "Author",
                "description": publication_data["description"],
                "genre": publication_data["category"],
                "keywords": [],
                "manuscript_file": publication_data["manuscript_path"],
                "cover_file": publication_data.get("cover_paths", {}).get("main", "")
            }
            result = await execute_agent("kdp_uploader", task_data, None)
            
            with get_db_context() as db:
                publication = db.query(Publication).filter(
                    Publication.id == publication_id
                ).first()
                
                if not publication:
                    return
                
                if result.success:
                    publication.status = "published" if publication_request.auto_publish else "draft"
                    publication.kdp_id = result.data.get('kdp_id')
                    publication.publication_url = result.data.get('publication_url')
                    publication.published_at = datetime.utcnow()
                    
                    # Update book status
                    book = db.query(Book).filter(Book.id == book.id).first()
                    if book:
                        book.status = "published"
                        book.published_at = datetime.utcnow()
                    
                    logger.info(f"Successfully published book {book.id}")
                else:
                    publication.status = "failed"
                    publication.error_message = result.error
                    
                    logger.error(f"Publication failed for book {book.id}: {result.error}")
                
        except Exception as e:
            logger.error(f"Publication process failed: {str(e)}")
            
            with get_db_context() as db:
                publication = db.query(Publication).filter(
                    Publication.id == publication_id
                ).first()
                
                if publication:
                    publication.status = "failed"
                    publication.error_message = str(e)
    
    async def _store_sales_data(self, book_id: int, sales_data: Dict[str, Any]):
        """Store sales data in database"""
        
        with get_db_context() as db:
            sales_record = SalesData(
                book_id=book_id,
                sales_units=sales_data.get('sales_units', 0),
                revenue=sales_data.get('revenue', 0.0),
                royalties=sales_data.get('royalties', 0.0),
                average_rating=sales_data.get('average_rating', 0.0),
                reviews_count=sales_data.get('reviews_count', 0),
                report_date=datetime.utcnow()
            )
            
            db.add(sales_record)