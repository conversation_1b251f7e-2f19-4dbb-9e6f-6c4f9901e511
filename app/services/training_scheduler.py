### app/services/training_scheduler.py - Training Scheduler

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any
import logging

from app.ml.data_access import VERLDataAccessor
from app.tasks.verl_training import train_verl_model_task

logger = logging.getLogger(__name__)

class TrainingScheduler:
    """Manages when VERL training should be triggered"""
    
    def __init__(self):
        self.data_accessor = VERLDataAccessor()
        self.last_training = None
        self.min_training_interval = timedelta(hours=12)  # Minimum 12 hours between training
    
    async def check_training_conditions(self) -> Dict[str, Any]:
        """Check if training should be triggered"""
        
        # Get current data statistics
        stats = await self.data_accessor.get_recent_feedback_stats(days=7)
        
        conditions = {
            'should_train': False,
            'reason': '',
            'data_points': stats.get('total_books', 0),
            'approval_rate': stats.get('approval_rate', 0),
            'last_training': self.last_training
        }
        
        # Check minimum data threshold
        if stats.get('total_books', 0) < 10:
            conditions['reason'] = 'Insufficient new data'
            return conditions
        
        # Check time since last training
        if self.last_training and datetime.now() - self.last_training < self.min_training_interval:
            conditions['reason'] = 'Too soon since last training'
            return conditions
        
        # Check performance decline
        if stats.get('approval_rate', 1.0) < 0.7:  # Less than 70% approval
            conditions['should_train'] = True
            conditions['reason'] = 'Performance decline detected'
            return conditions
        
        # Check sufficient new data
        if stats.get('total_books', 0) >= 20:
            conditions['should_train'] = True
            conditions['reason'] = 'Sufficient new data available'
            return conditions
        
        conditions['reason'] = 'No training trigger conditions met'
        return conditions
    
    async def auto_schedule_training(self):
        """Automatically schedule training if conditions are met"""
        
        conditions = await self.check_training_conditions()
        
        if conditions['should_train']:
            logger.info(f"Triggering VERL training: {conditions['reason']}")
            
            # Start training task (mock implementation for now)
            # train_verl_model_task.delay(days_back=30)
            logger.info("VERL training task would be started here")
            
            # Update last training time
            self.last_training = datetime.now()
            
            return True
        else:
            logger.info(f"Training not triggered: {conditions['reason']}")
            return False
