### app/services/trend_service.py - Trend Analysis Service

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import logging
import asyncio

from app.models.trend import TrendAnalysis
from app.models.user import User
from app.schemas.trend import (
    TrendAnalysisRequest, 
    TrendAnalysisResponse, 
    TrendAnalysisResult,
    TrendOpportunity,
    MarketAnalysis
)
from app.agents.pydantic_ai_manager import execute_agent
from app.database import get_db_context

logger = logging.getLogger(__name__)

class TrendService:
    """Service for managing trend analysis operations"""
    
    def __init__(self):
        # Using PydanticAI agents through manager
        pass
    
    async def create_trend_analysis(
        self, 
        request: TrendAnalysisRequest, 
        user_id: int
    ) -> TrendAnalysisResponse:
        """Create a new trend analysis"""
        
        with get_db_context() as db:
            # Create trend analysis record
            trend_analysis = TrendAnalysis(
                user_id=user_id,
                status="analyzing",
                request_config={
                    "categories": request.categories,
                    "max_results": request.max_results,
                    "min_search_volume": request.min_search_volume,
                    "max_competition": request.max_competition,
                    "analysis_depth": request.analysis_depth
                }
            )
            
            db.add(trend_analysis)
            db.flush()  # Get the ID
            
            analysis_id = trend_analysis.id
            
            logger.info(f"Started trend analysis {analysis_id} for user {user_id}")
            
            # Start background analysis
            asyncio.create_task(self._run_trend_analysis(analysis_id, request))
            
            return TrendAnalysisResponse(
                id=analysis_id,
                status="analyzing",
                total_opportunities=0,
                created_at=trend_analysis.created_at
            )
    
    async def get_trend_analysis(self, analysis_id: int, user_id: int) -> Optional[TrendAnalysisResponse]:
        """Get trend analysis by ID"""
        
        with get_db_context() as db:
            analysis = db.query(TrendAnalysis).filter(
                TrendAnalysis.id == analysis_id,
                TrendAnalysis.user_id == user_id
            ).first()
            
            if not analysis:
                return None
            
            # Prepare response
            response = TrendAnalysisResponse(
                id=analysis.id,
                status=analysis.status,
                total_opportunities=len(analysis.result.get('opportunities', [])) if analysis.result else 0,
                created_at=analysis.created_at,
                completed_at=analysis.completed_at
            )
            
            # Add analysis result if completed
            if analysis.status == "completed" and analysis.result:
                response.analysis_result = TrendAnalysisResult(**analysis.result)
                
                # Get best opportunity
                opportunities = analysis.result.get('opportunities', [])
                if opportunities:
                    best_opp = opportunities[0]  # Assuming sorted by score
                    response.best_opportunity = TrendOpportunity(**best_opp)
            
            return response
    
    async def get_user_trend_analyses(
        self, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 50
    ) -> List[TrendAnalysisResponse]:
        """Get all trend analyses for a user"""
        
        with get_db_context() as db:
            analyses = db.query(TrendAnalysis).filter(
                TrendAnalysis.user_id == user_id
            ).order_by(TrendAnalysis.created_at.desc()).offset(skip).limit(limit).all()
            
            responses = []
            for analysis in analyses:
                response = TrendAnalysisResponse(
                    id=analysis.id,
                    status=analysis.status,
                    total_opportunities=len(analysis.result.get('opportunities', [])) if analysis.result else 0,
                    created_at=analysis.created_at,
                    completed_at=analysis.completed_at
                )
                
                # Add best opportunity if available
                if analysis.result and analysis.result.get('opportunities'):
                    best_opp = analysis.result['opportunities'][0]
                    response.best_opportunity = TrendOpportunity(**best_opp)
                
                responses.append(response)
            
            return responses
    
    async def get_recent_opportunities(
        self, 
        user_id: int, 
        categories: Optional[List[str]] = None,
        limit: int = 20
    ) -> List[TrendOpportunity]:
        """Get recent high-quality opportunities"""
        
        with get_db_context() as db:
            # Get completed analyses from last 7 days
            since_date = datetime.utcnow() - timedelta(days=7)
            
            query = db.query(TrendAnalysis).filter(
                TrendAnalysis.user_id == user_id,
                TrendAnalysis.status == "completed",
                TrendAnalysis.completed_at >= since_date
            )
            
            analyses = query.order_by(TrendAnalysis.completed_at.desc()).all()
            
            # Collect all opportunities
            all_opportunities = []
            for analysis in analyses:
                if analysis.result and analysis.result.get('opportunities'):
                    for opp_data in analysis.result['opportunities']:
                        # Filter by categories if specified
                        if categories and opp_data.get('category') not in categories:
                            continue
                        
                        opportunity = TrendOpportunity(**opp_data)
                        all_opportunities.append(opportunity)
            
            # Sort by profit potential and return top results
            all_opportunities.sort(key=lambda x: x.profit_potential, reverse=True)
            return all_opportunities[:limit]
    
    async def analyze_market_competition(
        self, 
        category: str, 
        keywords: List[str]
    ) -> MarketAnalysis:
        """Analyze market competition for a specific category/keywords"""
        
        try:
            # Use PydanticAI trend analyzer
            from app.agents.pydantic_ai_manager import execute_agent
            task_data = {
                "categories": [category],
                "keywords": keywords,
                "analysis_type": "market_competition"
            }
            result = await execute_agent("trend_analyzer", task_data, None)
            
            if result.success:
                market_data = result.data.get('market_analysis', {})
                
                return MarketAnalysis(
                    category=category,
                    total_books=market_data.get('total_books', 0),
                    average_rating=market_data.get('average_rating', 0.0),
                    average_price=market_data.get('average_price', 0.0),
                    bestseller_characteristics=market_data.get('bestseller_characteristics', {}),
                    saturation_level=market_data.get('saturation_level', 'unknown'),
                    entry_difficulty=market_data.get('entry_difficulty', 'unknown')
                )
            else:
                raise Exception(f"Market analysis failed: {result.error}")
                
        except Exception as e:
            logger.error(f"Market competition analysis failed: {str(e)}")
            
            # Return default analysis
            return MarketAnalysis(
                category=category,
                total_books=0,
                average_rating=0.0,
                average_price=0.0,
                bestseller_characteristics={},
                saturation_level="unknown",
                entry_difficulty="unknown"
            )
    
    async def get_trending_categories(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get currently trending categories"""
        
        with get_db_context() as db:
            # Get recent analyses and aggregate by category
            since_date = datetime.utcnow() - timedelta(days=30)
            
            analyses = db.query(TrendAnalysis).filter(
                TrendAnalysis.status == "completed",
                TrendAnalysis.completed_at >= since_date
            ).all()
            
            category_stats = {}
            
            for analysis in analyses:
                if not analysis.result or not analysis.result.get('opportunities'):
                    continue
                
                for opp in analysis.result['opportunities']:
                    category = opp.get('category')
                    if not category:
                        continue
                    
                    if category not in category_stats:
                        category_stats[category] = {
                            'category': category,
                            'opportunity_count': 0,
                            'avg_profit_potential': 0.0,
                            'avg_market_size': 0,
                            'total_profit_potential': 0.0,
                            'opportunities': []
                        }
                    
                    stats = category_stats[category]
                    stats['opportunity_count'] += 1
                    stats['total_profit_potential'] += opp.get('profit_potential', 0)
                    stats['avg_market_size'] += opp.get('market_size', 0)
                    stats['opportunities'].append(opp)
            
            # Calculate averages and sort
            trending_categories = []
            for category, stats in category_stats.items():
                if stats['opportunity_count'] > 0:
                    stats['avg_profit_potential'] = stats['total_profit_potential'] / stats['opportunity_count']
                    stats['avg_market_size'] = stats['avg_market_size'] / stats['opportunity_count']
                    
                    # Calculate trend score
                    trend_score = (
                        stats['avg_profit_potential'] * 0.4 +
                        stats['opportunity_count'] * 10 * 0.3 +
                        min(stats['avg_market_size'] / 1000, 50) * 0.3
                    )
                    stats['trend_score'] = trend_score
                    
                    trending_categories.append(stats)
            
            # Sort by trend score and return top categories
            trending_categories.sort(key=lambda x: x['trend_score'], reverse=True)
            return trending_categories[:limit]
    
    async def delete_trend_analysis(self, analysis_id: int, user_id: int) -> Dict[str, Any]:
        """Delete a trend analysis"""
        
        with get_db_context() as db:
            analysis = db.query(TrendAnalysis).filter(
                TrendAnalysis.id == analysis_id,
                TrendAnalysis.user_id == user_id
            ).first()
            
            if not analysis:
                return {"success": False, "error": "Trend analysis not found"}
            
            db.delete(analysis)
            
            logger.info(f"Deleted trend analysis {analysis_id}")
            
            return {
                "success": True,
                "analysis_id": analysis_id,
                "message": "Trend analysis deleted successfully"
            }
    
    async def _run_trend_analysis(self, analysis_id: int, request: TrendAnalysisRequest):
        """Run trend analysis in background"""
        
        try:
            # Execute trend analysis using PydanticAI agent
            from app.agents.pydantic_ai_manager import execute_agent
            task_data = {
                "categories": request.categories,
                "max_results": request.max_results,
                "analysis_type": request.analysis_depth
            }
            result = await execute_agent("trend_analyzer", task_data, None)
            
            with get_db_context() as db:
                analysis = db.query(TrendAnalysis).filter(
                    TrendAnalysis.id == analysis_id
                ).first()
                
                if not analysis:
                    return
                
                if result.success:
                    analysis.status = "completed"
                    analysis.result = result.data
                    analysis.completed_at = datetime.utcnow()
                    
                    logger.info(f"Completed trend analysis {analysis_id}")
                else:
                    analysis.status = "failed"
                    analysis.error_message = result.error
                    
                    logger.error(f"Trend analysis {analysis_id} failed: {result.error}")
                
        except Exception as e:
            logger.error(f"Trend analysis {analysis_id} failed with exception: {str(e)}")
            
            with get_db_context() as db:
                analysis = db.query(TrendAnalysis).filter(
                    TrendAnalysis.id == analysis_id
                ).first()
                
                if analysis:
                    analysis.status = "failed"
                    analysis.error_message = str(e)
    
    async def get_analysis_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get statistics about user's trend analyses"""
        
        with get_db_context() as db:
            analyses = db.query(TrendAnalysis).filter(
                TrendAnalysis.user_id == user_id
            ).all()
            
            total_analyses = len(analyses)
            status_counts = {}
            total_opportunities = 0
            avg_analysis_time = 0
            
            analysis_times = []
            
            for analysis in analyses:
                status = analysis.status
                status_counts[status] = status_counts.get(status, 0) + 1
                
                if analysis.result:
                    total_opportunities += len(analysis.result.get('opportunities', []))
                
                if analysis.completed_at and analysis.created_at:
                    duration = (analysis.completed_at - analysis.created_at).total_seconds()
                    analysis_times.append(duration)
            
            if analysis_times:
                avg_analysis_time = sum(analysis_times) / len(analysis_times)
            
            return {
                "total_analyses": total_analyses,
                "status_breakdown": status_counts,
                "total_opportunities_found": total_opportunities,
                "average_analysis_time_seconds": round(avg_analysis_time, 2),
                "completed_analyses": status_counts.get("completed", 0),
                "failed_analyses": status_counts.get("failed", 0)
            }