### app/tasks/__init__.py - Task Module Initialization

from app.celery_app import celery

# Import all task modules to register them
from . import manuscript_generation
from . import trend_analysis
from . import publication
from . import verl_training
from . import model_training

__all__ = [
    'celery',
    'manuscript_generation',
    'trend_analysis', 
    'publication',
    'verl_training',
    'model_training'
]