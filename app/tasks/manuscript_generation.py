### app/tasks/manuscript_generation.py - Manuscript Generation Tasks

from app.celery_app import celery
from app.agents.pydantic_ai_manager import execute_agent
from app.services.book_service import BookService
from app.database import get_db_context
from app.models.book import Book
import logging

logger = logging.getLogger(__name__)

@celery.task(bind=True, max_retries=3)
def generate_manuscript_task(
    self, 
    book_id: int,
    trend_data: dict,
    provider: str = "openai",
    style: str = "professional",
    target_audience: str = "general adults",
    output_formats: list = None,
    layout_theme: str = None
):
    """Background task for manuscript generation"""
    
    if output_formats is None:
        output_formats = ["docx", "epub", "pdf"]
    
    try:
        # Update book status to generating
        with get_db_context() as db:
            book = db.query(Book).filter(Book.id == book_id).first()
            if not book:
                raise ValueError(f"Book {book_id} not found")
            
            book.status = "generating"
        
        # Generate manuscript using PydanticAI agent
        import asyncio
        task_data = {
            'trend_data': trend_data,
            'style': style,
            'target_audience': target_audience,
            'target_length': 8000,  # Default length
            'output_formats': output_formats
        }
        result = asyncio.run(execute_agent("manuscript_generator", task_data, None))
        
        if result.success:
            # Update book with generated content
            book_service = BookService()
            asyncio.run(book_service._update_book_with_manuscript(
                book_id, 
                result.data['manuscript'], 
                result.data
            ))
            
            # Update status to awaiting approval
            with get_db_context() as db:
                book = db.query(Book).filter(Book.id == book_id).first()
                if book:
                    book.status = "awaiting_approval"
            
            logger.info(f"Successfully generated manuscript for book {book_id}")
            
            return {
                "status": "success",
                "book_id": book_id,
                "manuscript_data": result.data,
                "message": "Manuscript generated successfully"
            }
        else:
            # Update status to failed
            with get_db_context() as db:
                book = db.query(Book).filter(Book.id == book_id).first()
                if book:
                    book.status = "failed"
                    book.rejection_reason = result.error
            
            raise Exception(f"Manuscript generation failed: {result.error}")
            
    except Exception as e:
        logger.error(f"Manuscript generation task failed for book {book_id}: {str(e)}")
        
        # Update book status to failed
        with get_db_context() as db:
            book = db.query(Book).filter(Book.id == book_id).first()
            if book:
                book.status = "failed"
                book.rejection_reason = str(e)
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying manuscript generation for book {book_id} (attempt {self.request.retries + 1})")
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        raise

@celery.task(bind=True)
def batch_generate_manuscripts(self, generation_requests: list):
    """Generate multiple manuscripts in batch"""
    
    results = []
    
    for request in generation_requests:
        try:
            # Trigger individual manuscript generation
            task_result = generate_manuscript_task.delay(
                book_id=request['book_id'],
                trend_data=request.get('trend_data', {}),
                provider=request.get('provider', 'openai'),
                style=request.get('style', 'professional'),
                target_audience=request.get('target_audience', 'general adults'),
                output_formats=request.get('output_formats', ['docx', 'epub', 'pdf']),
                layout_theme=request.get('layout_theme')
            )
            
            results.append({
                "book_id": request['book_id'],
                "task_id": task_result.id,
                "status": "queued"
            })
            
        except Exception as e:
            logger.error(f"Failed to queue manuscript generation for book {request['book_id']}: {str(e)}")
            results.append({
                "book_id": request['book_id'],
                "status": "failed",
                "error": str(e)
            })
    
    return {
        "batch_id": self.request.id,
        "total_requests": len(generation_requests),
        "queued_tasks": len([r for r in results if r['status'] == 'queued']),
        "failed_requests": len([r for r in results if r['status'] == 'failed']),
        "results": results
    }

@celery.task
def cleanup_failed_generations():
    """Clean up failed manuscript generations"""
    
    with get_db_context() as db:
        # Find books stuck in generating status for more than 2 hours
        from datetime import datetime, timedelta
        cutoff_time = datetime.utcnow() - timedelta(hours=2)
        
        stuck_books = db.query(Book).filter(
            Book.status == "generating",
            Book.updated_at < cutoff_time
        ).all()
        
        for book in stuck_books:
            book.status = "failed"
            book.rejection_reason = "Generation timed out"
            logger.warning(f"Marked book {book.id} as failed due to timeout")
        
        return {
            "cleaned_up_books": len(stuck_books),
            "book_ids": [book.id for book in stuck_books]
        }