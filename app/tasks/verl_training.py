### app/tasks/verl_training.py - VERL Training Tasks

from app.celery_app import celery
from app.ml.verl_trainer import VERLEbookTrainer, EbookRLConfig
from app.ml.feedback_collector import FeedbackCollector
import logging

logger = logging.getLogger(__name__)

@celery.task(bind=True)
def train_verl_model_task(self, days_back: int = 90, config_override: dict = None):
    """Background task for VERL model training"""
    
    try:
        # Initialize trainer with config
        config = EbookRLConfig()
        if config_override:
            for key, value in config_override.items():
                if hasattr(config, key):
                    setattr(config, key, value)
        
        trainer = VERLEbookTrainer(config)
        
        # Collect recent manuscript data with feedback
        collector = FeedbackCollector()
        
        # Get manuscripts with feedback
        import asyncio
        training_data = asyncio.run(collector.collect_training_data(days_back=days_back))
        
        if len(training_data) < 20:
            logger.warning("Insufficient data for VERL training")
            return {"status": "skipped", "reason": "insufficient_data"}
        
        # Run VERL training
        training_stats = asyncio.run(trainer.train_on_feedback(training_data))
        
        logger.info(f"VERL training completed: {training_stats}")
        
        return {
            "status": "success",
            "training_stats": training_stats,
            "data_points": len(training_data)
        }
        
    except Exception as e:
        logger.error(f"VERL training failed: {str(e)}")
        raise

@celery.task
def daily_verl_evaluation():
    """Daily evaluation of VERL model performance"""
    
    trainer = VERLEbookTrainer(EbookRLConfig())
    performance = asyncio.run(trainer.evaluate_model_performance())
    
    # Store performance metrics
    # Log or alert if performance drops
    
    return performance