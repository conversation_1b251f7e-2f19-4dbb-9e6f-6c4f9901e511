### app/utils/formatters.py - Document Formatting Utilities

import re
import markdown
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Document processing imports
try:
    from docx import Document
    from docx.shared import Inches, Pt
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    from docx.enum.style import WD_STYLE_TYPE
except ImportError:
    Document = None

try:
    import ebooklib
    from ebooklib import epub
except ImportError:
    epub = None

try:
    from weasyprint import HTML, CSS
except ImportError:
    HTML = None

logger = logging.getLogger(__name__)

class DocumentFormatter:
    """Handles document formatting and conversion for manuscripts"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=3)
        
    def slugify(self, text: str) -> str:
        """Convert text to a URL/filename-safe slug"""
        # Remove special characters and convert to lowercase
        text = re.sub(r'[^\w\s-]', '', text.lower())
        # Replace spaces and multiple hyphens with single hyphen
        text = re.sub(r'[-\s]+', '-', text)
        # Remove leading/trailing hyphens
        return text.strip('-')
    
    def clean_markdown(self, content: str) -> str:
        """Clean and normalize markdown content"""
        # Remove excessive whitespace
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        # Fix heading formatting
        content = re.sub(r'^#{1,6}\s*(.+)$', lambda m: f"{'#' * min(len(m.group(0).split()[0]), 6)} {m.group(1).strip()}", content, flags=re.MULTILINE)
        
        # Ensure proper paragraph spacing
        lines = content.split('\n')
        cleaned_lines = []
        for i, line in enumerate(lines):
            cleaned_lines.append(line.strip())
            if i < len(lines) - 1 and line.strip() and lines[i + 1].strip() and not line.startswith('#'):
                if not lines[i + 1].startswith('#'):
                    cleaned_lines.append('')
        
        return '\n'.join(cleaned_lines)
    
    async def convert_to_docx(self, content: str, title: str, metadata: Dict[str, Any] = None) -> bytes:
        """Convert markdown content to DOCX format"""
        if Document is None:
            raise ImportError("python-docx is required for DOCX conversion")
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, self._create_docx, content, title, metadata or {}
        )
    
    def _create_docx(self, content: str, title: str, metadata: Dict[str, Any]) -> bytes:
        """Create DOCX document (runs in thread pool)"""
        doc = Document()
        
        # Set document properties
        properties = doc.core_properties
        properties.title = title
        properties.author = metadata.get('author', 'AI Generated')
        properties.category = metadata.get('category', 'Book')
        properties.created = datetime.now()
        
        # Configure styles
        self._setup_docx_styles(doc)
        
        # Add title
        title_para = doc.add_heading(title, level=0)
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add metadata section
        if metadata.get('author'):
            author_para = doc.add_paragraph(f"by {metadata['author']}")
            author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        doc.add_page_break()
        
        # Convert markdown to paragraphs
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if line.startswith('# '):
                doc.add_heading(line[2:], level=1)
            elif line.startswith('## '):
                doc.add_heading(line[3:], level=2)
            elif line.startswith('### '):
                doc.add_heading(line[4:], level=3)
            elif line.startswith('#### '):
                doc.add_heading(line[5:], level=4)
            else:
                # Regular paragraph
                para = doc.add_paragraph(line)
                para.style = 'Normal'
        
        # Save to bytes
        from io import BytesIO
        buffer = BytesIO()
        doc.save(buffer)
        buffer.seek(0)
        return buffer.getvalue()
    
    def _setup_docx_styles(self, doc):
        """Setup professional styles for DOCX document"""
        styles = doc.styles
        
        # Modify Normal style
        normal_style = styles['Normal']
        normal_font = normal_style.font
        normal_font.name = 'Times New Roman'
        normal_font.size = Pt(12)
        
        # Modify heading styles
        heading1_style = styles['Heading 1']
        heading1_font = heading1_style.font
        heading1_font.name = 'Times New Roman'
        heading1_font.size = Pt(18)
        heading1_font.bold = True
        
        heading2_style = styles['Heading 2']
        heading2_font = heading2_style.font
        heading2_font.name = 'Times New Roman'
        heading2_font.size = Pt(16)
        heading2_font.bold = True
    
    async def convert_to_epub(self, content: str, title: str, metadata: Dict[str, Any] = None) -> bytes:
        """Convert markdown content to EPUB format"""
        if epub is None:
            raise ImportError("ebooklib is required for EPUB conversion")
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, self._create_epub, content, title, metadata or {}
        )
    
    def _create_epub(self, content: str, title: str, metadata: Dict[str, Any]) -> bytes:
        """Create EPUB document (runs in thread pool)"""
        book = epub.EpubBook()
        
        # Set metadata
        book.set_identifier(f"book_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        book.set_title(title)
        book.set_language('en')
        book.add_author(metadata.get('author', 'AI Generated'))
        
        if metadata.get('description'):
            book.add_metadata('DC', 'description', metadata['description'])
        
        # Create chapters from content
        chapters = self._split_content_into_chapters(content)
        epub_chapters = []
        
        for i, (chapter_title, chapter_content) in enumerate(chapters):
            # Convert markdown to HTML
            html_content = markdown.markdown(chapter_content)
            
            # Create EPUB chapter
            chapter = epub.EpubHtml(
                title=chapter_title,
                file_name=f'chapter_{i+1}.xhtml',
                lang='en'
            )
            chapter.content = f'''
            <!DOCTYPE html>
            <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <title>{chapter_title}</title>
                <link rel="stylesheet" type="text/css" href="style/default.css"/>
            </head>
            <body>
                <h1>{chapter_title}</h1>
                {html_content}
            </body>
            </html>
            '''
            
            book.add_item(chapter)
            epub_chapters.append(chapter)
        
        # Add CSS
        nav_css = epub.EpubItem(
            uid="nav_css",
            file_name="style/default.css",
            media_type="text/css",
            content=self._get_epub_css()
        )
        book.add_item(nav_css)
        
        # Create table of contents
        book.toc = [(epub.Link("intro.xhtml", "Introduction", "intro"))] + \
                   [(epub.Link(f"chapter_{i+1}.xhtml", chapter_title, f"chapter_{i+1}") 
                     for i, (chapter_title, _) in enumerate(chapters))]
        
        # Add navigation files
        book.add_item(epub.EpubNcx())
        book.add_item(epub.EpubNav())
        
        # Define spine
        book.spine = ['nav'] + epub_chapters
        
        # Save to bytes
        from io import BytesIO
        buffer = BytesIO()
        epub.write_epub(buffer, book)
        buffer.seek(0)
        return buffer.getvalue()
    
    def _split_content_into_chapters(self, content: str) -> List[tuple]:
        """Split content into chapters based on headings"""
        chapters = []
        lines = content.split('\n')
        current_chapter = None
        current_content = []
        
        for line in lines:
            if line.startswith('# ') or line.startswith('## '):
                # Save previous chapter
                if current_chapter and current_content:
                    chapters.append((current_chapter, '\n'.join(current_content)))
                
                # Start new chapter
                current_chapter = line.lstrip('# ').strip()
                current_content = []
            else:
                current_content.append(line)
        
        # Add final chapter
        if current_chapter and current_content:
            chapters.append((current_chapter, '\n'.join(current_content)))
        
        return chapters if chapters else [("Main Content", content)]
    
    def _get_epub_css(self) -> str:
        """Get CSS styles for EPUB"""
        return """
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 1em;
        }
        
        h1 {
            font-size: 18pt;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            text-align: center;
        }
        
        h2 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        h3 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 1em;
            margin-bottom: 0.5em;
        }
        
        p {
            text-align: justify;
            margin-bottom: 1em;
        }
        
        strong {
            font-weight: bold;
        }
        
        em {
            font-style: italic;
        }
        """
    
    async def convert_to_pdf(self, content: str, title: str, metadata: Dict[str, Any] = None) -> bytes:
        """Convert markdown content to PDF format"""
        if HTML is None:
            logger.warning("WeasyPrint not available, falling back to simple PDF generation")
            return await self._simple_pdf_conversion(content, title, metadata or {})
        
        return await asyncio.get_event_loop().run_in_executor(
            self.executor, self._create_pdf, content, title, metadata or {}
        )
    
    def _create_pdf(self, content: str, title: str, metadata: Dict[str, Any]) -> bytes:
        """Create PDF document using WeasyPrint"""
        # Convert markdown to HTML
        html_content = markdown.markdown(content)
        
        # Create complete HTML document
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{title}</title>
            <style>
                {self._get_pdf_css()}
            </style>
        </head>
        <body>
            <div class="title-page">
                <h1 class="book-title">{title}</h1>
                <p class="author">by {metadata.get('author', 'AI Generated')}</p>
            </div>
            <div class="content">
                {html_content}
            </div>
        </body>
        </html>
        """
        
        # Convert to PDF
        pdf_buffer = HTML(string=full_html).write_pdf()
        return pdf_buffer
    
    def _get_pdf_css(self) -> str:
        """Get CSS styles for PDF"""
        return """
        @page {
            size: A4;
            margin: 1in;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #333;
        }
        
        .title-page {
            text-align: center;
            page-break-after: always;
            padding-top: 25%;
        }
        
        .book-title {
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 2em;
        }
        
        .author {
            font-size: 14pt;
            font-style: italic;
        }
        
        .content h1 {
            font-size: 18pt;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            page-break-before: auto;
        }
        
        .content h2 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }
        
        .content h3 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 1em;
            margin-bottom: 0.5em;
        }
        
        .content p {
            text-align: justify;
            margin-bottom: 1em;
        }
        """
    
    async def _simple_pdf_conversion(self, content: str, title: str, metadata: Dict[str, Any]) -> bytes:
        """Fallback PDF conversion without WeasyPrint"""
        try:
            import pdfkit
            
            # Convert markdown to HTML
            html_content = markdown.markdown(content)
            
            # Simple HTML template
            html = f"""
            <html>
            <head>
                <meta charset="UTF-8">
                <title>{title}</title>
            </head>
            <body>
                <h1>{title}</h1>
                <p><i>by {metadata.get('author', 'AI Generated')}</i></p>
                <hr>
                {html_content}
            </body>
            </html>
            """
            
            # Convert to PDF
            pdf_bytes = pdfkit.from_string(html, False, options={'page-size': 'A4'})
            return pdf_bytes
            
        except ImportError:
            logger.error("No PDF conversion library available")
            # Return HTML as fallback
            return html.encode('utf-8')
    
    async def convert_to_html(self, content: str, title: str, metadata: Dict[str, Any] = None) -> str:
        """Convert markdown content to HTML format"""
        # Convert markdown to HTML
        html_content = markdown.markdown(content)
        
        # Create complete HTML document
        full_html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title}</title>
            <style>
                {self._get_html_css()}
            </style>
        </head>
        <body>
            <header>
                <h1 class="book-title">{title}</h1>
                <p class="author">by {metadata.get('author', 'AI Generated') if metadata else 'AI Generated'}</p>
                {f'<p class="category">{metadata["category"]}</p>' if metadata and metadata.get('category') else ''}
            </header>
            <main class="content">
                {html_content}
            </main>
            <footer>
                <p>Generated on {datetime.now().strftime('%B %d, %Y')}</p>
            </footer>
        </body>
        </html>
        """
        
        return full_html
    
    def _get_html_css(self) -> str:
        """Get CSS styles for HTML"""
        return """
        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #fff;
        }
        
        header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #ddd;
        }
        
        .book-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #2c3e50;
        }
        
        .author {
            font-size: 1.2rem;
            font-style: italic;
            color: #7f8c8d;
            margin-bottom: 0.5rem;
        }
        
        .category {
            font-size: 1rem;
            color: #95a5a6;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .content h1 {
            font-size: 2rem;
            font-weight: bold;
            margin-top: 3rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.5rem;
        }
        
        .content h2 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: #34495e;
        }
        
        .content h3 {
            font-size: 1.3rem;
            font-weight: bold;
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
            color: #34495e;
        }
        
        .content p {
            margin-bottom: 1.5rem;
            text-align: justify;
        }
        
        .content strong {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .content em {
            font-style: italic;
        }
        
        footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #ddd;
            color: #95a5a6;
            font-size: 0.9rem;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 1rem;
            }
            
            .book-title {
                font-size: 2rem;
            }
            
            .content h1 {
                font-size: 1.7rem;
            }
        }
        """
    
    def get_word_count(self, content: str) -> int:
        """Get word count from content"""
        # Remove markdown formatting and count words
        text = re.sub(r'[#*_`\[\]()]', '', content)
        words = text.split()
        return len([word for word in words if word.strip()])
    
    def estimate_reading_time(self, content: str, words_per_minute: int = 200) -> int:
        """Estimate reading time in minutes"""
        word_count = self.get_word_count(content)
        return max(1, word_count // words_per_minute)