### app/utils/layout_designer.py - Professional Layout Design System

from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import json
import logging
from datetime import datetime
from dataclasses import dataclass
import asyncio

from app.utils.formatters import DocumentFormatter
from app.schemas.book import Manuscript

logger = logging.getLogger(__name__)

@dataclass
class ThemeConfiguration:
    """Configuration for a professional book theme"""
    name: str
    display_name: str
    description: str
    primary_font: str
    secondary_font: str
    heading_color: str
    text_color: str
    accent_color: str
    line_height: float
    margin_settings: Dict[str, str]
    chapter_style: str
    cover_style: str
    target_categories: List[str]

class ProfessionalLayoutDesigner:
    """Advanced layout designer with professional themes and formatting"""
    
    def __init__(self):
        self.formatter = DocumentFormatter()
        self.themes = self._initialize_themes()
        
    def _initialize_themes(self) -> Dict[str, ThemeConfiguration]:
        """Initialize professional theme configurations"""
        themes = {}
        
        # Clean Professional Theme
        themes['clean_professional'] = ThemeConfiguration(
            name='clean_professional',
            display_name='Clean Professional',
            description='Modern, clean design perfect for business and self-help books',
            primary_font='Times New Roman',
            secondary_font='Arial',
            heading_color='#2c3e50',
            text_color='#333333',
            accent_color='#3498db',
            line_height=1.6,
            margin_settings={
                'top': '1.0in',
                'bottom': '1.0in',
                'left': '1.0in',
                'right': '1.0in'
            },
            chapter_style='numbered_clean',
            cover_style='minimal',
            target_categories=['business', 'self-help', 'productivity', 'finance']
        )
        
        # Modern Minimalist Theme
        themes['modern_minimalist'] = ThemeConfiguration(
            name='modern_minimalist',
            display_name='Modern Minimalist',
            description='Contemporary design with clean lines and ample white space',
            primary_font='Georgia',
            secondary_font='Helvetica',
            heading_color='#1a1a1a',
            text_color='#404040',
            accent_color='#e74c3c',
            line_height=1.7,
            margin_settings={
                'top': '1.2in',
                'bottom': '1.2in',
                'left': '1.1in',
                'right': '1.1in'
            },
            chapter_style='minimalist',
            cover_style='geometric',
            target_categories=['technology', 'design', 'innovation', 'startup']
        )
        
        # Classic Literary Theme
        themes['classic_literary'] = ThemeConfiguration(
            name='classic_literary',
            display_name='Classic Literary',
            description='Traditional, elegant design with ornate elements',
            primary_font='Garamond',
            secondary_font='Times New Roman',
            heading_color='#8b4513',
            text_color='#2f2f2f',
            accent_color='#d4af37',
            line_height=1.8,
            margin_settings={
                'top': '1.3in',
                'bottom': '1.3in',
                'left': '1.2in',
                'right': '1.2in'
            },
            chapter_style='ornate',
            cover_style='classic',
            target_categories=['literature', 'history', 'philosophy', 'arts']
        )
        
        # Tech Business Theme
        themes['tech_business'] = ThemeConfiguration(
            name='tech_business',
            display_name='Tech Business',
            description='Corporate-focused design with modern typography',
            primary_font='Calibri',
            secondary_font='Arial',
            heading_color='#34495e',
            text_color='#2c3e50',
            accent_color='#9b59b6',
            line_height=1.5,
            margin_settings={
                'top': '0.9in',
                'bottom': '0.9in',
                'left': '0.9in',
                'right': '0.9in'
            },
            chapter_style='corporate',
            cover_style='professional',
            target_categories=['technology', 'business', 'management', 'consulting']
        )
        
        # Romantic Elegant Theme
        themes['romantic_elegant'] = ThemeConfiguration(
            name='romantic_elegant',
            display_name='Romantic Elegant',
            description='Graceful design perfect for romance and lifestyle books',
            primary_font='Book Antiqua',
            secondary_font='Palatino',
            heading_color='#c0392b',
            text_color='#5a5a5a',
            accent_color='#e91e63',
            line_height=1.7,
            margin_settings={
                'top': '1.1in',
                'bottom': '1.1in',
                'left': '1.0in',
                'right': '1.0in'
            },
            chapter_style='elegant',
            cover_style='romantic',
            target_categories=['romance', 'lifestyle', 'health', 'wellness']
        )
        
        return themes
    
    def select_optimal_theme(self, manuscript: Union[Manuscript, Dict[str, Any]]) -> str:
        """Intelligently select the best theme based on manuscript content"""
        
        # Extract category and content
        if isinstance(manuscript, Manuscript):
            category = manuscript.category.lower()
            content = manuscript.content.lower()
            title = manuscript.title.lower()
        else:
            category = manuscript.get('category', '').lower()
            content = manuscript.get('content', '').lower()
            title = manuscript.get('title', '').lower()
        
        # Score each theme based on category match and content analysis
        theme_scores = {}
        
        for theme_name, theme in self.themes.items():
            score = 0
            
            # Category matching (40% of score)
            category_match = any(cat in category for cat in theme.target_categories)
            if category_match:
                score += 40
            
            # Content tone analysis (30% of score)
            score += self._analyze_content_tone(content, theme) * 30
            
            # Title analysis (20% of score)
            score += self._analyze_title_style(title, theme) * 20
            
            # Default preference (10% of score)
            score += self._get_default_preference(theme) * 10
            
            theme_scores[theme_name] = score
        
        # Return the highest scoring theme
        best_theme = max(theme_scores, key=theme_scores.get)
        logger.info(f"Selected theme '{best_theme}' with score {theme_scores[best_theme]}")
        
        return best_theme
    
    def _analyze_content_tone(self, content: str, theme: ThemeConfiguration) -> float:
        """Analyze content tone to match with theme (returns 0-1)"""
        
        # Define tone indicators for different themes
        tone_indicators = {
            'clean_professional': ['business', 'professional', 'strategy', 'success', 'growth', 'leadership'],
            'modern_minimalist': ['innovative', 'modern', 'simple', 'efficient', 'digital', 'tech'],
            'classic_literary': ['traditional', 'wisdom', 'knowledge', 'timeless', 'elegant', 'profound'],
            'tech_business': ['technology', 'data', 'system', 'process', 'automation', 'digital'],
            'romantic_elegant': ['beautiful', 'gentle', 'caring', 'nurturing', 'personal', 'emotional']
        }
        
        indicators = tone_indicators.get(theme.name, [])
        matches = sum(1 for indicator in indicators if indicator in content)
        
        return min(matches / len(indicators), 1.0) if indicators else 0.5
    
    def _analyze_title_style(self, title: str, theme: ThemeConfiguration) -> float:
        """Analyze title style to match with theme (returns 0-1)"""
        
        title_patterns = {
            'clean_professional': ['guide', 'handbook', 'complete', 'ultimate', 'master'],
            'modern_minimalist': ['simple', 'easy', 'quick', 'smart', 'minimal'],
            'classic_literary': ['art of', 'wisdom of', 'timeless', 'classic', 'principles'],
            'tech_business': ['system', 'framework', 'method', 'strategy', 'blueprint'],
            'romantic_elegant': ['heart', 'soul', 'beautiful', 'gentle', 'loving']
        }
        
        patterns = title_patterns.get(theme.name, [])
        matches = sum(1 for pattern in patterns if pattern in title)
        
        return min(matches / len(patterns), 1.0) if patterns else 0.5
    
    def _get_default_preference(self, theme: ThemeConfiguration) -> float:
        """Get default preference score for theme"""
        # Clean professional is generally most versatile
        preferences = {
            'clean_professional': 0.8,
            'modern_minimalist': 0.6,
            'tech_business': 0.5,
            'classic_literary': 0.4,
            'romantic_elegant': 0.3
        }
        return preferences.get(theme.name, 0.5)
    
    def create_professional_layout(
        self,
        manuscript: Union[Manuscript, Dict[str, Any]],
        theme_name: Optional[str] = None,
        output_formats: List[str] = None
    ) -> Dict[str, Union[str, bytes]]:
        """Create professional layout in multiple formats"""
        
        if output_formats is None:
            output_formats = ['docx', 'epub', 'pdf', 'html']
        
        # Auto-select theme if not specified
        if theme_name is None or theme_name == "auto":
            theme_name = self.select_optimal_theme(manuscript)
        
        theme = self.themes.get(theme_name, self.themes['clean_professional'])
        
        # Extract manuscript data
        if isinstance(manuscript, Manuscript):
            title = manuscript.title
            content = manuscript.content
            metadata = {
                'author': manuscript.author,
                'category': manuscript.category,
                'description': manuscript.description,
                'word_count': manuscript.word_count
            }
        else:
            title = manuscript.get('title', 'Untitled')
            content = manuscript.get('content', '')
            metadata = {
                'author': manuscript.get('author', 'AI Generated'),
                'category': manuscript.get('category', 'Book'),
                'description': manuscript.get('description', ''),
                'word_count': len(content.split()) if content else 0
            }
        
        # Apply theme styling to content
        styled_content = self._apply_theme_styling(content, theme)
        
        # Generate all requested formats
        formatted_files = {}
        
        try:
            # Use asyncio to run async functions
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                if 'docx' in output_formats:
                    formatted_files['docx'] = loop.run_until_complete(
                        self._create_themed_docx(styled_content, title, metadata, theme)
                    )
                
                if 'epub' in output_formats:
                    formatted_files['epub'] = loop.run_until_complete(
                        self._create_themed_epub(styled_content, title, metadata, theme)
                    )
                
                if 'pdf' in output_formats:
                    formatted_files['pdf'] = loop.run_until_complete(
                        self._create_themed_pdf(styled_content, title, metadata, theme)
                    )
                
                if 'html' in output_formats:
                    formatted_files['html'] = loop.run_until_complete(
                        self._create_themed_html(styled_content, title, metadata, theme)
                    )
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"Error creating formatted files: {str(e)}")
            # Fallback to basic formatting
            formatted_files = self._create_fallback_formats(
                content, title, metadata, output_formats
            )
        
        logger.info(f"Created professional layout with theme '{theme_name}' in formats: {list(formatted_files.keys())}")
        return formatted_files
    
    def _apply_theme_styling(self, content: str, theme: ThemeConfiguration) -> str:
        """Apply theme-specific styling to content"""
        
        styled_content = content
        
        # Add theme-specific chapter decorations
        if theme.chapter_style == 'ornate':
            styled_content = self._add_ornate_decorations(styled_content)
        elif theme.chapter_style == 'minimalist':
            styled_content = self._add_minimalist_styling(styled_content)
        elif theme.chapter_style == 'corporate':
            styled_content = self._add_corporate_styling(styled_content)
        elif theme.chapter_style == 'elegant':
            styled_content = self._add_elegant_styling(styled_content)
        
        return styled_content
    
    def _add_ornate_decorations(self, content: str) -> str:
        """Add ornate decorations for classic literary theme"""
        # Add decorative elements around chapter headings
        lines = content.split('\n')
        decorated_lines = []
        
        for line in lines:
            if line.startswith('## Chapter'):
                decorated_lines.append('---')
                decorated_lines.append(f'✦ {line[3:]} ✦')
                decorated_lines.append('---')
            else:
                decorated_lines.append(line)
        
        return '\n'.join(decorated_lines)
    
    def _add_minimalist_styling(self, content: str) -> str:
        """Add minimalist styling"""
        # Clean up excessive formatting for minimalist look
        lines = content.split('\n')
        clean_lines = []
        
        for line in lines:
            if line.startswith('## Chapter'):
                clean_lines.append(f'{line[3:].upper()}')
                clean_lines.append('')
            else:
                clean_lines.append(line)
        
        return '\n'.join(clean_lines)
    
    def _add_corporate_styling(self, content: str) -> str:
        """Add corporate styling"""
        lines = content.split('\n')
        corporate_lines = []
        
        for line in lines:
            if line.startswith('## Chapter'):
                chapter_num = line.split(':')[0].replace('## Chapter', '').strip()
                chapter_title = ':'.join(line.split(':')[1:]).strip()
                corporate_lines.append(f'SECTION {chapter_num}: {chapter_title.upper()}')
                corporate_lines.append('=' * 50)
            else:
                corporate_lines.append(line)
        
        return '\n'.join(corporate_lines)
    
    def _add_elegant_styling(self, content: str) -> str:
        """Add elegant styling for romantic theme"""
        lines = content.split('\n')
        elegant_lines = []
        
        for line in lines:
            if line.startswith('## Chapter'):
                elegant_lines.append('♥ ♥ ♥')
                elegant_lines.append(line[3:])
                elegant_lines.append('♥ ♥ ♥')
            else:
                elegant_lines.append(line)
        
        return '\n'.join(elegant_lines)
    
    async def _create_themed_docx(
        self, 
        content: str, 
        title: str, 
        metadata: Dict[str, Any], 
        theme: ThemeConfiguration
    ) -> bytes:
        """Create DOCX with theme styling"""
        # Enhance metadata with theme information
        enhanced_metadata = {**metadata, 'theme': theme.name, 'theme_display': theme.display_name}
        return await self.formatter.convert_to_docx(content, title, enhanced_metadata)
    
    async def _create_themed_epub(
        self, 
        content: str, 
        title: str, 
        metadata: Dict[str, Any], 
        theme: ThemeConfiguration
    ) -> bytes:
        """Create EPUB with theme styling"""
        enhanced_metadata = {**metadata, 'theme': theme.name}
        return await self.formatter.convert_to_epub(content, title, enhanced_metadata)
    
    async def _create_themed_pdf(
        self, 
        content: str, 
        title: str, 
        metadata: Dict[str, Any], 
        theme: ThemeConfiguration
    ) -> bytes:
        """Create PDF with theme styling"""
        enhanced_metadata = {**metadata, 'theme': theme.name}
        return await self.formatter.convert_to_pdf(content, title, enhanced_metadata)
    
    async def _create_themed_html(
        self, 
        content: str, 
        title: str, 
        metadata: Dict[str, Any], 
        theme: ThemeConfiguration
    ) -> str:
        """Create HTML with theme styling"""
        enhanced_metadata = {**metadata, 'theme': theme.name}
        return await self.formatter.convert_to_html(content, title, enhanced_metadata)
    
    def _create_fallback_formats(
        self, 
        content: str, 
        title: str, 
        metadata: Dict[str, Any], 
        output_formats: List[str]
    ) -> Dict[str, Union[str, bytes]]:
        """Create fallback formats if themed creation fails"""
        
        formatted_files = {}
        
        # Simple markdown as fallback
        if 'html' in output_formats:
            # Process content outside f-string to avoid backslash issues
            processed_content = content.replace('\n\n', '</p><p>').replace('\n', '<br>')

            simple_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{title}</title>
                <style>
                    body {{ font-family: Georgia, serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 2rem; }}
                    h1 {{ color: #333; }}
                    h2 {{ color: #666; }}
                </style>
            </head>
            <body>
                <h1>{title}</h1>
                <p><em>by {metadata.get('author', 'AI Generated')}</em></p>
                <hr>
                {processed_content}
            </body>
            </html>
            """
            formatted_files['html'] = simple_html
        
        logger.warning("Using fallback formatting due to theme application failure")
        return formatted_files
    
    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all available themes"""
        
        themes_info = {}
        for name, theme in self.themes.items():
            themes_info[name] = {
                'display_name': theme.display_name,
                'description': theme.description,
                'target_categories': theme.target_categories,
                'style_preview': {
                    'primary_font': theme.primary_font,
                    'heading_color': theme.heading_color,
                    'accent_color': theme.accent_color
                }
            }
        
        return themes_info
    
    def preview_theme_on_content(
        self, 
        sample_content: str, 
        theme_name: str
    ) -> Dict[str, Any]:
        """Generate a preview of how content would look with a specific theme"""
        
        theme = self.themes.get(theme_name)
        if not theme:
            return {'error': f'Theme {theme_name} not found'}
        
        # Apply theme styling to sample content
        styled_content = self._apply_theme_styling(sample_content, theme)
        
        # Return preview information
        return {
            'theme_name': theme.display_name,
            'styled_content': styled_content,
            'style_info': {
                'primary_font': theme.primary_font,
                'heading_color': theme.heading_color,
                'text_color': theme.text_color,
                'accent_color': theme.accent_color,
                'chapter_style': theme.chapter_style
            }
        }