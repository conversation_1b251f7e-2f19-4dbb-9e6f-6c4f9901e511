### app/utils/model_adapters.py - SQLAlchemy to Pydantic Model Adapters

from typing import TypeVar, Generic, Type, Dict, Any, List, Optional, Union
from pydantic import BaseModel, ValidationError
from sqlalchemy.orm import Session
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BaseModel)
S = TypeVar('S')  # SQLAlchemy model type

class ModelAdapter(Generic[T, S]):
    """Generic adapter for converting between SQLAlchemy and Pydantic models"""
    
    def __init__(
        self, 
        pydantic_model: Type[T], 
        sqlalchemy_model: Type[S],
        name: str = None
    ):
        self.pydantic_model = pydantic_model
        self.sqlalchemy_model = sqlalchemy_model
        self.name = name or f"{sqlalchemy_model.__name__}Adapter"
    
    def to_pydantic(self, db_obj: S, **extra_fields) -> T:
        """Convert SQLAlchemy model to Pydantic model"""
        try:
            # Use the model's to_pydantic method if available
            if hasattr(db_obj, 'to_pydantic'):
                data = db_obj.to_pydantic()
                data.update(extra_fields)
                return self.pydantic_model.model_validate(data)
            
            # Fallback to standard conversion
            return self.pydantic_model.model_validate(
                db_obj, 
                from_attributes=True,
                **extra_fields
            )
        except ValidationError as e:
            logger.error(f"Validation error in {self.name}.to_pydantic: {e}")
            raise
        except Exception as e:
            logger.error(f"Conversion error in {self.name}.to_pydantic: {e}")
            raise
    
    def from_pydantic(
        self, 
        pydantic_obj: Union[T, Dict[str, Any]], 
        **extra_fields
    ) -> S:
        """Convert Pydantic model to SQLAlchemy model"""
        try:
            # Convert to dict if it's a Pydantic model
            if isinstance(pydantic_obj, BaseModel):
                data = pydantic_obj.model_dump(exclude_unset=True)
            else:
                data = dict(pydantic_obj)
            
            # Add extra fields
            data.update(extra_fields)
            
            # Use the model's from_pydantic method if available
            if hasattr(self.sqlalchemy_model, 'from_pydantic'):
                return self.sqlalchemy_model.from_pydantic(data, **extra_fields)
            
            # Fallback to standard conversion
            return self.sqlalchemy_model(**data)
        except Exception as e:
            logger.error(f"Conversion error in {self.name}.from_pydantic: {e}")
            raise
    
    def to_pydantic_list(self, db_objects: List[S], **extra_fields) -> List[T]:
        """Convert list of SQLAlchemy models to list of Pydantic models"""
        return [self.to_pydantic(obj, **extra_fields) for obj in db_objects]
    
    def update_from_pydantic(
        self, 
        db_obj: S, 
        pydantic_obj: Union[T, Dict[str, Any]], 
        exclude_unset: bool = True,
        exclude_none: bool = True
    ) -> S:
        """Update SQLAlchemy model from Pydantic model"""
        try:
            # Convert to dict if it's a Pydantic model
            if isinstance(pydantic_obj, BaseModel):
                data = pydantic_obj.model_dump(
                    exclude_unset=exclude_unset,
                    exclude_none=exclude_none
                )
            else:
                data = dict(pydantic_obj)
            
            # Update attributes
            for key, value in data.items():
                if hasattr(db_obj, key):
                    setattr(db_obj, key, value)
            
            return db_obj
        except Exception as e:
            logger.error(f"Update error in {self.name}.update_from_pydantic: {e}")
            raise

# Pre-configured adapters for common models
class UserAdapter(ModelAdapter):
    """Adapter for User models"""
    
    def __init__(self):
        from app.models.user import User
        from app.schemas.user import UserResponse, UserCreate
        super().__init__(UserResponse, User, "UserAdapter")
        self.create_schema = UserCreate
    
    def create_from_pydantic(
        self, 
        user_data: Union[UserCreate, Dict[str, Any]], 
        password_hash: str
    ) -> 'User':
        """Create new user from Pydantic data"""
        return self.sqlalchemy_model.from_pydantic(user_data, password_hash)
    
    def to_response(self, db_user: 'User') -> 'UserResponse':
        """Convert to UserResponse schema"""
        return self.to_pydantic(db_user)

class BookAdapter(ModelAdapter):
    """Adapter for Book models"""
    
    def __init__(self):
        from app.models.book import Book
        from app.schemas.book import BookResponse, BookCreate
        super().__init__(BookResponse, Book, "BookAdapter")
        self.create_schema = BookCreate
    
    def create_from_pydantic(
        self, 
        book_data: Union[BookCreate, Dict[str, Any]], 
        user_id: int
    ) -> 'Book':
        """Create new book from Pydantic data"""
        return self.sqlalchemy_model.from_pydantic(book_data, user_id)
    
    def to_response(self, db_book: 'Book') -> 'BookResponse':
        """Convert to BookResponse schema"""
        return self.to_pydantic(db_book)
    
    def with_computed_fields(self, db_book: 'Book') -> Dict[str, Any]:
        """Get book data with all computed fields"""
        base_data = self.to_pydantic(db_book).model_dump()
        
        # Add computed properties
        computed_fields = {
            'total_reading_time': db_book.total_reading_time,
            'is_novel_length': db_book.is_novel_length,
            'is_novella_length': db_book.is_novella_length,
            'completion_percentage': db_book.completion_percentage,
            'days_since_creation': db_book.days_since_creation
        }
        
        base_data.update(computed_fields)
        return base_data

class ChapterAdapter(ModelAdapter):
    """Adapter for Chapter models"""
    
    def __init__(self):
        from app.models.book import Chapter
        from app.schemas.book import Chapter as ChapterSchema
        super().__init__(ChapterSchema, Chapter, "ChapterAdapter")
    
    def create_from_pydantic(
        self, 
        chapter_data: Union[ChapterSchema, Dict[str, Any]], 
        book_id: int
    ) -> 'Chapter':
        """Create new chapter from Pydantic data"""
        if isinstance(chapter_data, BaseModel):
            data = chapter_data.model_dump()
        else:
            data = dict(chapter_data)
        
        data['book_id'] = book_id
        return self.sqlalchemy_model(**data)
    
    def with_computed_fields(self, db_chapter: 'Chapter') -> Dict[str, Any]:
        """Get chapter data with all computed fields"""
        base_data = self.to_pydantic(db_chapter).model_dump()
        
        # Add computed properties
        computed_fields = {
            'estimated_reading_time': db_chapter.estimated_reading_time,
            'reading_difficulty': db_chapter.reading_difficulty
        }
        
        base_data.update(computed_fields)
        return base_data

# Validation utilities
class ValidationHelper:
    """Helper class for validation operations"""
    
    @staticmethod
    def validate_with_pydantic(
        data: Dict[str, Any], 
        schema: Type[BaseModel]
    ) -> BaseModel:
        """Validate data using Pydantic schema"""
        try:
            return schema.model_validate(data)
        except ValidationError as e:
            logger.error(f"Validation failed for {schema.__name__}: {e}")
            raise
    
    @staticmethod
    def safe_validate(
        data: Dict[str, Any], 
        schema: Type[BaseModel]
    ) -> Optional[BaseModel]:
        """Validate data, return None if validation fails"""
        try:
            return schema.model_validate(data)
        except ValidationError as e:
            logger.warning(f"Validation failed for {schema.__name__}: {e}")
            return None
    
    @staticmethod
    def extract_validation_errors(e: ValidationError) -> List[Dict[str, Any]]:
        """Extract validation errors in a readable format"""
        errors = []
        for error in e.errors():
            errors.append({
                'field': '.'.join(str(loc) for loc in error['loc']),
                'message': error['msg'],
                'type': error['type'],
                'input': error.get('input')
            })
        return errors

# Batch operations
class BatchAdapter:
    """Utility for batch operations with model adapters"""
    
    def __init__(self, adapter: ModelAdapter):
        self.adapter = adapter
    
    def batch_create(
        self, 
        db: Session, 
        pydantic_objects: List[Union[BaseModel, Dict[str, Any]]], 
        **extra_fields
    ) -> List[Any]:
        """Create multiple objects in batch"""
        db_objects = []
        
        for pydantic_obj in pydantic_objects:
            try:
                db_obj = self.adapter.from_pydantic(pydantic_obj, **extra_fields)
                db.add(db_obj)
                db_objects.append(db_obj)
            except Exception as e:
                logger.error(f"Failed to create object in batch: {e}")
                db.rollback()
                raise
        
        try:
            db.commit()
            return db_objects
        except Exception as e:
            logger.error(f"Failed to commit batch creation: {e}")
            db.rollback()
            raise
    
    def batch_update(
        self, 
        db: Session, 
        updates: List[Dict[str, Any]]
    ) -> List[Any]:
        """Update multiple objects in batch"""
        updated_objects = []
        
        for update_data in updates:
            try:
                obj_id = update_data.pop('id')
                db_obj = db.query(self.adapter.sqlalchemy_model).filter_by(id=obj_id).first()
                
                if db_obj:
                    self.adapter.update_from_pydantic(db_obj, update_data)
                    updated_objects.append(db_obj)
                else:
                    logger.warning(f"Object with id {obj_id} not found for update")
            except Exception as e:
                logger.error(f"Failed to update object in batch: {e}")
                db.rollback()
                raise
        
        try:
            db.commit()
            return updated_objects
        except Exception as e:
            logger.error(f"Failed to commit batch update: {e}")
            db.rollback()
            raise

# Global adapter instances
user_adapter = UserAdapter()
book_adapter = BookAdapter()
chapter_adapter = ChapterAdapter()

# Convenience functions
def serialize_for_api(db_obj, adapter: ModelAdapter) -> Dict[str, Any]:
    """Serialize database object for API response"""
    return adapter.to_pydantic(db_obj).model_dump(mode='json')

def deserialize_from_api(data: Dict[str, Any], adapter: ModelAdapter) -> Any:
    """Deserialize API data to database object"""
    return adapter.from_pydantic(data)

def validate_api_input(data: Dict[str, Any], schema: Type[BaseModel]) -> BaseModel:
    """Validate API input using Pydantic schema"""
    return ValidationHelper.validate_with_pydantic(data, schema)