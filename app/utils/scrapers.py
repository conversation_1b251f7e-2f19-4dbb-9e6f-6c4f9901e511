## app/utils/scrapers.py - Web Scraping Utilities

import httpx
import asyncio
from bs4 import BeautifulSoup
from typing import List, Dict, Any, Optional
import re
from urllib.parse import urljoin
import logging

logger = logging.getLogger(__name__)

class AmazonScraper:
    """Scraper for Amazon Kindle data"""
    
    def __init__(self):
        self.base_url = "https://www.amazon.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    async def get_kindle_bestsellers(self, category: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get Kindle bestsellers for a category"""
        try:
            # Amazon category mappings
            category_urls = {
                'self-help': '/Best-Sellers-Kindle-Store-Self-Help/zgbs/digital-text/156488011',
                'romance': '/Best-Sellers-Kindle-Store-Romance/zgbs/digital-text/158566011',
                'mystery': '/Best-Sellers-Kindle-Store-Mystery-Thriller-Suspense/zgbs/digital-text/157028011',
                'fantasy': '/Best-Sellers-Kindle-Store-Science-Fiction-Fantasy/zgbs/digital-text/158591011',
                'business': '/Best-Sellers-Kindle-Store-Business-Investing/zgbs/digital-text/156381011'
            }
            
            if category not in category_urls:
                return []
            
            url = self.base_url + category_urls[category]
            
            async with httpx.AsyncClient(headers=self.headers, timeout=30) as client:
                response = await client.get(url)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                books = []
                
                # Parse bestseller items
                items = soup.find_all('div', {'data-component-type': 'zg-item-immersion'})
                
                for item in items[:limit]:
                    try:
                        title_elem = item.find('h3', class_='zg-item-header')
                        title = title_elem.get_text(strip=True) if title_elem else 'Unknown'
                        
                        author_elem = item.find('span', class_='a-size-small')
                        author = author_elem.get_text(strip=True) if author_elem else 'Unknown'
                        
                        price_elem = item.find('span', class_='p13n-sc-price')
                        price_text = price_elem.get_text(strip=True) if price_elem else '$0.00'
                        price = self._extract_price(price_text)
                        
                        rating_elem = item.find('span', class_='a-icon-alt')
                        rating = self._extract_rating(rating_elem.get_text() if rating_elem else '')
                        
                        rank_elem = item.find('span', class_='zg-bdg-text')
                        rank = self._extract_rank(rank_elem.get_text() if rank_elem else '')
                        
                        books.append({
                            'title': title,
                            'author': author,
                            'price': price,
                            'rating': rating,
                            'rank': rank,
                            'category': category,
                            'source': 'amazon_bestseller'
                        })
                        
                    except Exception as e:
                        logger.warning(f"Error parsing book item: {str(e)}")
                        continue
                
                return books
                
        except Exception as e:
            logger.error(f"Error scraping Amazon bestsellers for {category}: {str(e)}")
            return []
    
    async def get_kindle_new_releases(self, category: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get Kindle new releases for a category"""
        try:
            # For now, return similar structure to bestsellers but with different data
            # In production, this would scrape actual new releases
            return [
                {
                    "title": f"New Release in {category} #{i+1}",
                    "author": f"Author {i+1}",
                    "price": 2.99 + (i * 0.5),
                    "rating": 4.0 + (i * 0.1),
                    "review_count": 10 + (i * 5),
                    "rank": i + 1,
                    "category": category,
                    "publication_date": "2024-01-01",
                    "is_new_release": True
                }
                for i in range(min(limit, 10))
            ]
        except Exception as e:
            logger.error(f"Error getting new releases for {category}: {str(e)}")
            return []
    
    def _extract_price(self, price_text: str) -> float:
        """Extract price from text"""
        match = re.search(r'\$(\d+\.?\d*)', price_text)
        return float(match.group(1)) if match else 0.0
    
    def _extract_rating(self, rating_text: str) -> float:
        """Extract rating from text"""
        match = re.search(r'(\d+\.?\d*)', rating_text)
        return float(match.group(1)) if match else 0.0
    
    def _extract_rank(self, rank_text: str) -> int:
        """Extract rank from text"""
        match = re.search(r'#(\d+)', rank_text)
        return int(match.group(1)) if match else 0

class RedditScraper:
    """Scraper for Reddit trending posts"""
    
    def __init__(self):
        self.base_url = "https://www.reddit.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    
    async def get_trending_posts(self, subreddits: List[str], limit: int = 10) -> List[Dict[str, Any]]:
        """Get trending posts from specified subreddits"""
        all_posts = []
        
        for subreddit in subreddits:
            try:
                url = f"{self.base_url}/r/{subreddit}/hot.json?limit={limit}"
                
                async with httpx.AsyncClient(headers=self.headers, timeout=30) as client:
                    response = await client.get(url)
                    response.raise_for_status()
                    
                    data = response.json()
                    posts = data.get('data', {}).get('children', [])
                    
                    for post in posts:
                        post_data = post.get('data', {})
                        all_posts.append({
                            'title': post_data.get('title', ''),
                            'score': post_data.get('score', 0),
                            'num_comments': post_data.get('num_comments', 0),
                            'subreddit': subreddit,
                            'created_utc': post_data.get('created_utc', 0),
                            'url': post_data.get('url', ''),
                            'selftext': post_data.get('selftext', '')[:500]  # First 500 chars
                        })
                
                await asyncio.sleep(1)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"Error scraping r/{subreddit}: {str(e)}")
                continue
        
        # Sort by engagement (score + comments)
        return sorted(all_posts, key=lambda x: x['score'] + x['num_comments'], reverse=True)