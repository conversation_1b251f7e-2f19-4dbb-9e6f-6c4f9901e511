#!/usr/bin/env python3
### celery_beat.py - Celery Beat Scheduler Startup Script

import os
from app.celery_app import celery

if __name__ == "__main__":
    # Set log level
    log_level = os.getenv("CELERY_LOG_LEVEL", "INFO")
    
    # Start beat scheduler
    celery.start([
        'beat',
        '--loglevel', log_level,
        '--schedule', os.getenv("CELERY_BEAT_SCHEDULE", "/tmp/celerybeat-schedule"),
        '--pidfile', os.getenv("CELERY_BEAT_PIDFILE", "/tmp/celerybeat.pid")
    ])