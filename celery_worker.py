#!/usr/bin/env python3
### celery_worker.py - Celery Worker Startup Script

import os
import sys
from app.celery_app import celery

if __name__ == "__main__":
    # Set log level
    log_level = os.getenv("CELERY_LOG_LEVEL", "INFO")
    
    # Start worker
    celery.start([
        'worker',
        '--loglevel', log_level,
        '--concurrency', os.getenv("CELERY_CONCURRENCY", "4"),
        '--queues', os.getenv("CELERY_QUEUES", "default,manuscript,analysis,publishing,training"),
        '--hostname', f'worker@{os.getenv("HOSTNAME", "localhost")}',
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat'
    ])