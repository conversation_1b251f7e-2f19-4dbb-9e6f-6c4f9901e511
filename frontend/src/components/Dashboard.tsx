"use client";

import React, { useState, useEffect } from "react";
import { useQuery } from "react-query";
import {
  BookOpen,
  TrendingUp,
  Upload,
  DollarSign,
  Plus,
  RefreshCw,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import toast from "react-hot-toast";

import { api } from "../utils/api";
import { Book, TrendAnalysis, Publication } from "../types";
import BookCard from "./BookCard";
import TrendAnalysisCard from "./TrendAnalysisCard";
import PublicationCard from "./PublicationCard";
import StatsCards from "./StatsCards";
import NewBookModal from "./NewBookModal";

const Dashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<
    "books" | "trends" | "publications" | "analytics"
  >("books");
  const [showNewBookModal, setShowNewBookModal] = useState(false);

  // Fetch user data
  const {
    data: books,
    isLoading: booksLoading,
    refetch: refetchBooks,
  } = useQuery<Book[]>(
    "books",
    () => api.get("/books").then((res) => res.data),
    { refetchInterval: 30000 } // Refetch every 30 seconds
  );

  const {
    data: trends,
    isLoading: trendsLoading,
    refetch: refetchTrends,
  } = useQuery<TrendAnalysis[]>(
    "trends",
    () => api.get("/trends").then((res) => res.data),
    { refetchInterval: 60000 }
  );

  const { data: publications, isLoading: publicationsLoading } = useQuery<
    Publication[]
  >("publications", () => api.get("/publications").then((res) => res.data), {
    refetchInterval: 30000,
  });

  const handleCreateBook = async (data: any) => {
    try {
      const response = await api.post("/books/generate", data);
      toast.success("Book generation started!");
      setShowNewBookModal(false);
      refetchBooks();
    } catch (error) {
      toast.error("Failed to start book generation");
    }
  };

  const handleAnalyzeTrends = async () => {
    try {
      const response = await api.post("/trends/analyze", {
        categories: ["self-help", "business", "health", "romance", "mystery"],
      });
      toast.success("Trend analysis started!");
      refetchTrends();
    } catch (error) {
      toast.error("Failed to start trend analysis");
    }
  };

  const stats = {
    totalBooks: books?.length || 0,
    publishedBooks: books?.filter((b) => b.status === "published").length || 0,
    awaitingApproval:
      books?.filter((b) => b.status === "awaiting_approval").length || 0,
    totalRevenue:
      publications?.reduce((sum, p) => sum + (p.revenue || 0), 0) || 0,
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-900">
                E-book Generator
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={handleAnalyzeTrends}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <TrendingUp className="h-4 w-4" />
                <span>Analyze Trends</span>
              </button>

              <button
                onClick={() => setShowNewBookModal(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>New Book</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <StatsCards stats={stats} />

        {/* Navigation Tabs */}
        <div className="mt-8">
          <nav className="flex space-x-8">
            {[
              { key: "books", label: "Books", icon: BookOpen },
              { key: "trends", label: "Trend Analysis", icon: TrendingUp },
              { key: "publications", label: "Publications", icon: Upload },
              { key: "analytics", label: "Analytics", icon: DollarSign },
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex items-center space-x-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeTab === key
                    ? "bg-blue-100 text-blue-700"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {activeTab === "books" && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-900">
                  Your Books
                </h2>
                <button
                  onClick={() => refetchBooks()}
                  className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Refresh</span>
                </button>
              </div>

              {booksLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div
                      key={i}
                      className="bg-white p-6 rounded-lg shadow animate-pulse"
                    >
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
                      <div className="h-2 bg-gray-200 rounded w-full mb-2"></div>
                      <div className="h-2 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  ))}
                </div>
              ) : books && books.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {books.map((book) => (
                    <BookCard
                      key={book.id}
                      book={book}
                      onUpdate={refetchBooks}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No books yet
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Start by analyzing trends or creating your first book.
                  </p>
                  <button
                    onClick={() => setShowNewBookModal(true)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create Your First Book
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === "trends" && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-900">
                  Trend Analyses
                </h2>
                <button
                  onClick={handleAnalyzeTrends}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  New Analysis
                </button>
              </div>

              {trendsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div
                      key={i}
                      className="bg-white p-6 rounded-lg shadow animate-pulse"
                    >
                      <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                      <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              ) : trends && trends.length > 0 ? (
                <div className="space-y-4">
                  {trends.map((analysis) => (
                    <TrendAnalysisCard key={analysis.id} analysis={analysis} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No trend analyses yet
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Analyze market trends to discover book opportunities.
                  </p>
                  <button
                    onClick={handleAnalyzeTrends}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Start Analysis
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === "publications" && (
            <div className="space-y-4">
              <h2 className="text-lg font-medium text-gray-900">
                Publications
              </h2>

              {publicationsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div
                      key={i}
                      className="bg-white p-6 rounded-lg shadow animate-pulse"
                    >
                      <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
                      <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  ))}
                </div>
              ) : publications && publications.length > 0 ? (
                <div className="space-y-4">
                  {publications.map((publication) => (
                    <PublicationCard
                      key={publication.id}
                      publication={publication}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No publications yet
                  </h3>
                  <p className="text-gray-600">
                    Your published books will appear here.
                  </p>
                </div>
              )}
            </div>
          )}

          {activeTab === "analytics" && (
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900">
                Sales Analytics
              </h2>
              {/* Analytics charts and metrics would go here */}
              <div className="bg-white p-6 rounded-lg shadow">
                <p className="text-gray-600">
                  Analytics dashboard coming soon...
                </p>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* New Book Modal */}
      {showNewBookModal && (
        <NewBookModal
          isOpen={showNewBookModal}
          onClose={() => setShowNewBookModal(false)}
          onSubmit={handleCreateBook}
          trendAnalyses={trends || []}
        />
      )}
    </div>
  );
};

export default Dashboard;
