//  VERLMonitorDashboard.tsx
//  This component will display real-time monitoring data for the VERL system.
//  It will include live counters, performance trends, and data flow analysis.
//  It will also include controls to start/stop monitoring and to view detailed diagnostics.
//  The data will be fetched using Server-Sent Events (SSE) to provide real-time updates.
//  The component will use React Query for data fetching and caching.
//  The component will use React Query Devtools for debugging.
import React, { useState, useEffect } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  Activity,
  TrendingUp,
  Database,
  Zap,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

interface MonitoringData {
  monitoring: {
    monitoring_active: boolean;
    current_metrics: {
      approval_rate: number;
      quality_average: number;
      training_readiness: boolean;
      data_quality_score: number;
    };
    live_counters: {
      approvals_today: number;
      rejections_today: number;
      books_generated_today: number;
    };
    trends: {
      approval_rate_trend: number[];
      quality_trend: number[];
    };
  };
  training: {
    is_training: boolean;
    progress_percentage: number;
    current_epoch: number;
    total_epochs: number;
  };
}

const VERLMonitorDashboard: React.FC = () => {
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(
    null
  );
  const [isLiveMode, setIsLiveMode] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<
    "connected" | "disconnected" | "connecting"
  >("disconnected");

  // Start live monitoring
  const startLiveMonitoring = () => {
    setIsLiveMode(true);
    setConnectionStatus("connecting");

    const eventSource = new EventSource("/api/monitoring/live-stream");

    eventSource.onopen = () => {
      setConnectionStatus("connected");
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === "metrics_update") {
          setMonitoringData(data);
        }
      } catch (error) {
        console.error("Error parsing live data:", error);
      }
    };

    eventSource.onerror = () => {
      setConnectionStatus("disconnected");
      setIsLiveMode(false);
      eventSource.close();
    };

    return () => {
      eventSource.close();
      setIsLiveMode(false);
      setConnectionStatus("disconnected");
    };
  };

  // Stop live monitoring
  const stopLiveMonitoring = () => {
    setIsLiveMode(false);
    setConnectionStatus("disconnected");
  };

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const response = await fetch("/api/monitoring/status");
        const data = await response.json();
        setMonitoringData(data);
      } catch (error) {
        console.error("Error fetching monitoring data:", error);
      }
    };

    fetchInitialData();
  }, []);

  if (!monitoringData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading monitoring data...</span>
      </div>
    );
  }

  const { monitoring, training } = monitoringData;

  return (
    <div className="space-y-6">
      {/* Header with Live Controls */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">
          VERL System Monitor
        </h2>

        <div className="flex items-center space-x-4">
          <div
            className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
              connectionStatus === "connected"
                ? "bg-green-100 text-green-800"
                : connectionStatus === "connecting"
                  ? "bg-yellow-100 text-yellow-800"
                  : "bg-gray-100 text-gray-800"
            }`}
          >
            <div
              className={`w-2 h-2 rounded-full ${
                connectionStatus === "connected"
                  ? "bg-green-500"
                  : connectionStatus === "connecting"
                    ? "bg-yellow-500"
                    : "bg-gray-500"
              }`}
            />
            <span className="capitalize">{connectionStatus}</span>
          </div>

          {!isLiveMode ? (
            <button
              onClick={startLiveMonitoring}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Live Monitoring
            </button>
          ) : (
            <button
              onClick={stopLiveMonitoring}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Stop Live Monitoring
            </button>
          )}
        </div>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatusCard
          title="Approval Rate"
          value={`${(monitoring.current_metrics.approval_rate * 100).toFixed(1)}%`}
          icon={<CheckCircle className="h-6 w-6" />}
          trend={monitoring.trends.approval_rate_trend}
          color="green"
        />

        <StatusCard
          title="Quality Score"
          value={monitoring.current_metrics.quality_average.toFixed(1)}
          icon={<TrendingUp className="h-6 w-6" />}
          trend={monitoring.trends.quality_trend}
          color="blue"
        />

        <StatusCard
          title="Data Quality"
          value={`${(monitoring.current_metrics.data_quality_score * 100).toFixed(0)}%`}
          icon={<Database className="h-6 w-6" />}
          color="purple"
        />

        <StatusCard
          title="Training Ready"
          value={monitoring.current_metrics.training_readiness ? "Yes" : "No"}
          icon={<Zap className="h-6 w-6" />}
          color={
            monitoring.current_metrics.training_readiness ? "green" : "gray"
          }
        />
      </div>

      {/* Live Counters */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Today's Activity</h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {monitoring.live_counters.approvals_today}
            </div>
            <div className="text-sm text-gray-600">Approvals</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {monitoring.live_counters.rejections_today}
            </div>
            <div className="text-sm text-gray-600">Rejections</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {monitoring.live_counters.books_generated_today}
            </div>
            <div className="text-sm text-gray-600">Books Generated</div>
          </div>
        </div>
      </div>

      {/* Training Status */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Training Pipeline</h3>

        {training.is_training ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Training Progress</span>
              <span className="text-sm text-gray-600">
                Epoch {training.current_epoch} / {training.total_epochs}
              </span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${training.progress_percentage}%` }}
              />
            </div>

            <div className="text-sm text-gray-600">
              {training.progress_percentage.toFixed(1)}% Complete
            </div>
          </div>
        ) : (
          <div className="text-center py-4">
            <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600">No training in progress</p>
            <p className="text-sm text-gray-500 mt-1">
              Training will start automatically when conditions are met
            </p>
          </div>
        )}
      </div>

      {/* Performance Trends Chart */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium mb-4">Performance Trends (24h)</h3>

        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={monitoring.trends.approval_rate_trend.map(
                (rate, index) => ({
                  time: index,
                  approvalRate: rate * 100,
                  quality: monitoring.trends.quality_trend[index] || 0,
                })
              )}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip
                formatter={(value, name) => [
                  `${Number(value).toFixed(1)}${name === "approvalRate" ? "%" : ""}`,
                  name === "approvalRate" ? "Approval Rate" : "Quality Score",
                ]}
              />
              <Line
                type="monotone"
                dataKey="approvalRate"
                stroke="#10B981"
                strokeWidth={2}
              />
              <Line
                type="monotone"
                dataKey="quality"
                stroke="#3B82F6"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Data Flow Visualization */}
      <DataFlowVisualization />
    </div>
  );
};

interface StatusCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend?: number[];
  color: string;
}

const StatusCard: React.FC<StatusCardProps> = ({
  title,
  value,
  icon,
  trend,
  color,
}) => {
  const colorClasses = {
    green: "text-green-600 bg-green-100",
    blue: "text-blue-600 bg-blue-100",
    purple: "text-purple-600 bg-purple-100",
    red: "text-red-600 bg-red-100",
    gray: "text-gray-600 bg-gray-100",
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>{icon}</div>
      </div>

      {trend && trend.length > 1 && (
        <div className="mt-4">
          <div className="h-8">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={trend.map((value, index) => ({ index, value }))}>
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke={`var(--${color}-500)`}
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}
    </div>
  );
};

const DataFlowVisualization: React.FC = () => {
  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-medium mb-4">Data Flow Pipeline</h3>

      <div className="flex items-center justify-between">
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Activity className="h-6 w-6 text-blue-600" />
          </div>
          <span className="text-sm mt-2">User Actions</span>
        </div>

        <div className="flex-1 h-0.5 bg-gray-300 mx-4" />

        <div className="flex flex-col items-center">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <Database className="h-6 w-6 text-green-600" />
          </div>
          <span className="text-sm mt-2">Data Storage</span>
        </div>

        <div className="flex-1 h-0.5 bg-gray-300 mx-4" />

        <div className="flex flex-col items-center">
          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
            <TrendingUp className="h-6 w-6 text-purple-600" />
          </div>
          <span className="text-sm mt-2">VERL Training</span>
        </div>

        <div className="flex-1 h-0.5 bg-gray-300 mx-4" />

        <div className="flex flex-col items-center">
          <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
            <Zap className="h-6 w-6 text-orange-600" />
          </div>
          <span className="text-sm mt-2">Improved AI</span>
        </div>
      </div>
    </div>
  );
};

export default VERLMonitorDashboard;
