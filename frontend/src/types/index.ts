export interface User {
  id: number;
  email: string;
  username: string;
  is_active: boolean;
  is_premium: boolean;
  created_at: string;
}

export interface Book {
  id: number;
  user_id: number;
  title: string;
  subtitle?: string;
  author: string;
  category: string;
  description?: string;
  keywords?: string[];
  word_count?: number;
  chapter_count?: number;
  status:
    | "generating"
    | "awaiting_approval"
    | "approved"
    | "rejected"
    | "published";
  quality_score?: number;
  rejection_reason?: string;
  created_at: string;
  approved_at?: string;
  published_at?: string;
}

export interface TrendAnalysis {
  id: number;
  user_id: number;
  categories: string[];
  results?: any;
  top_opportunities?: any[];
  total_opportunities: number;
  status: "analyzing" | "completed" | "failed";
  execution_time?: number;
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

export interface Publication {
  id: number;
  user_id: number;
  book_id: number;
  kdp_id?: string;
  publication_url?: string;
  price: number;
  royalty_rate: number;
  status: "publishing" | "published" | "failed" | "draft";
  error_message?: string;
  created_at: string;
  published_at?: string;
  revenue?: number;
}

export interface SalesData {
  id: number;
  book_id: number;
  sales_units: number;
  revenue: number;
  royalties: number;
  pages_read: number;
  rank?: number;
  reviews_count: number;
  average_rating: number;
  date_range: string;
  report_date: string;
}
