<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">58%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t27">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t27"><data value='get_user_books'>get_user_books</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t51">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t51"><data value='get_book_performance'>get_book_performance</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t82">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t82"><data value='save_book_draft'>save_book_draft</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t123">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t123"><data value='scrape_amazon_bestsellers'>scrape_amazon_bestsellers</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t148">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t148"><data value='scrape_reddit_trends'>scrape_reddit_trends</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t171">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t171"><data value='analyze_competitor_books'>analyze_competitor_books</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t222">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t222"><data value='analyze_content_quality'>analyze_content_quality</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t263">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t263"><data value='extract_keywords'>extract_keywords</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t292">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t292"><data value='research_market_trends'>research_market_trends</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t333">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t333"><data value='validate_book_concept'>validate_book_concept</data></a></td>
                <td>15</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>130</td>
                <td>54</td>
                <td>0</td>
                <td class="right" data-ratio="76 130">58%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
