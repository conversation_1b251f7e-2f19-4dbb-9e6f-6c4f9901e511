{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "8971e77f23d730a36852d84f1e902b2e", "files": {"z_c78f3e75ce3283c5_pydantic_ai_tools_py": {"hash": "881bcbb5153385900922eabd1e4f42f1", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_tools_py.html", "file": "app/agents/pydantic_ai_tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 130, "n_excluded": 0, "n_missing": 54, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}