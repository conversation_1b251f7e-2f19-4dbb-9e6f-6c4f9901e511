<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">51%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html">app/agents/__init__.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t29">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t29"><data value='ResearchResult'>ResearchResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t38">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t38"><data value='PersonalizationProfile'>PersonalizationProfile</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t46">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t46"><data value='MultimodalContent'>MultimodalContent</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t59">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t59"><data value='ResearchDependencies'>ResearchDependencies</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t112">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t112"><data value='PersonalizationDependencies'>PersonalizationDependencies</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t165">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t165"><data value='MultimodalDependencies'>MultimodalDependencies</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>104</td>
                <td>39</td>
                <td>0</td>
                <td class="right" data-ratio="65 104">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t24">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t24"><data value='DatabaseDependencies'>DatabaseDependencies</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t40">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t40"><data value='AIModelDependencies'>AIModelDependencies</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t56">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t56"><data value='ScrapingDependencies'>ScrapingDependencies</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t69">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t69"><data value='ManuscriptDependencies'>ManuscriptDependencies</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t83">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t83"><data value='TrendAnalysisDependencies'>TrendAnalysisDependencies</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t99">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t99"><data value='SalesMonitorDependencies'>SalesMonitorDependencies</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t113">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t113"><data value='CoverDesignDependencies'>CoverDesignDependencies</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t121">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t121"><data value='KDPUploadDependencies'>KDPUploadDependencies</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t138">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t138"><data value='AgentExecutionResult'>AgentExecutionResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t148">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t148"><data value='BookOutlineResult'>BookOutlineResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t158">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t158"><data value='ChapterResult'>ChapterResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t166">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t166"><data value='TrendAnalysisResult'>TrendAnalysisResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t174">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t174"><data value='SalesAnalysisResult'>SalesAnalysisResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t183">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t183"><data value='CoverDesignResult'>CoverDesignResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t192">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t192"><data value='UploadResult'>UploadResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t258">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t258"><data value='AgentRegistry'>AgentRegistry</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>133</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="129 133">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t28">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t28"><data value='DesignConcept'>DesignConcept</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t37">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t37"><data value='ColorPalette'>ColorPalette</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t47">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t47"><data value='TypographySpec'>TypographySpec</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t56">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t56"><data value='LayoutComposition'>LayoutComposition</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t64">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t64"><data value='ComprehensiveCoverDesign'>ComprehensiveCoverDesign</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>124</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="61 124">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t31">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t31"><data value='BookMetadata'>BookMetadata</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t42">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t42"><data value='PricingStrategy'>PricingStrategy</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t50">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t50"><data value='UploadValidation'>UploadValidation</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t59">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t59"><data value='ComprehensiveUploadResult'>ComprehensiveUploadResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>169</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="55 169">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t26">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t26"><data value='PydanticAIAgentManager'>PydanticAIAgentManager</data></a></td>
                <td>70</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="50 70">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>34</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="34 34">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t32">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t32"><data value='ManuscriptGenerationResult'>ManuscriptGenerationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t42">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t42"><data value='ContentExpansionResult'>ContentExpansionResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>91</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="47 91">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t31">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t31"><data value='BookSalesData'>BookSalesData</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t43">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t43"><data value='PerformanceMetrics'>PerformanceMetrics</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t54">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t54"><data value='SalesInsights'>SalesInsights</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t63">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t63"><data value='ComprehensiveSalesReport'>ComprehensiveSalesReport</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>153</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="65 153">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>130</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="19 130">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t34">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t34"><data value='MarketOpportunity'>MarketOpportunity</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t46">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t46"><data value='ComprehensiveTrendAnalysis'>ComprehensiveTrendAnalysis</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t56">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t56"><data value='KeywordAnalysis'>KeywordAnalysis</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>153</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="61 153">40%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1197</td>
                <td>589</td>
                <td>0</td>
                <td class="right" data-ratio="608 1197">51%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
