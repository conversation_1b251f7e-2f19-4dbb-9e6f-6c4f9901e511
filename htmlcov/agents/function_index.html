<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">51%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html">app/agents/__init__.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t85">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t85"><data value='conduct_topic_research'>conduct_topic_research</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t137">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t137"><data value='analyze_user_preferences'>analyze_user_preferences</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t171">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t171"><data value='post_init__'>MultimodalDependencies.__post_init__</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t194">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t194"><data value='generate_image_prompts'>generate_image_prompts</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t217">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t217"><data value='research_topic'>research_topic</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t254">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t254"><data value='personalize_content'>personalize_content</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t290">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html#t290"><data value='generate_multimodal_content'>generate_multimodal_content</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t28">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t28"><data value='get_db'>DatabaseDependencies.get_db</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t32">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t32"><data value='get_user'>DatabaseDependencies.get_user</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t48">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t48"><data value='post_init__'>AIModelDependencies.__post_init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t62">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t62"><data value='post_init__'>ScrapingDependencies.__post_init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t78">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t78"><data value='post_init__'>ManuscriptDependencies.__post_init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t91">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t91"><data value='post_init__'>TrendAnalysisDependencies.__post_init__</data></a></td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="1 2">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t106">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t106"><data value='post_init__'>SalesMonitorDependencies.__post_init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t128">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t128"><data value='post_init__'>KDPUploadDependencies.__post_init__</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t205">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t205"><data value='create_standard_dependencies'>create_standard_dependencies</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t217">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t217"><data value='validate_agent_config'>validate_agent_config</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t222">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t222"><data value='format_execution_time'>format_execution_time</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t226">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t226"><data value='create_error_result'>create_error_result</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t239">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t239"><data value='create_success_result'>create_success_result</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t261">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t261"><data value='init__'>AgentRegistry.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t265">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t265"><data value='register_agent'>AgentRegistry.register_agent</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t270">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t270"><data value='get_agent'>AgentRegistry.get_agent</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t274">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t274"><data value='list_agents'>AgentRegistry.list_agents</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t278">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html#t278"><data value='remove_agent'>AgentRegistry.remove_agent</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html">app/agents/pydantic_ai_base.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>127</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="127 127">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t114">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t114"><data value='analyze_genre_conventions'>analyze_genre_conventions</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t186">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t186"><data value='generate_color_palettes'>generate_color_palettes</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t228">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t228"><data value='suggest_typography'>suggest_typography</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t303">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t303"><data value='create_image_prompts'>create_image_prompts</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t341">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t341"><data value='analyze_market_positioning'>analyze_market_positioning</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t386">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t386"><data value='generate_complementary_color'>_generate_complementary_color</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t399">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t399"><data value='generate_accent_color'>_generate_accent_color</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t416">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html#t416"><data value='design_book_cover'>design_book_cover</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>61</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="61 61">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t98">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t98"><data value='validate_book_metadata'>validate_book_metadata</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t161">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t161"><data value='optimize_pricing_strategy'>optimize_pricing_strategy</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t217">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t217"><data value='perform_kdp_upload'>perform_kdp_upload</data></a></td>
                <td>25</td>
                <td>25</td>
                <td>0</td>
                <td class="right" data-ratio="0 25">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t274">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t274"><data value='predict_performance'>predict_performance</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t340">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t340"><data value='login_to_kdp'>_login_to_kdp</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t361">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t361"><data value='fill_book_details'>_fill_book_details</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t393">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html#t393"><data value='upload_to_kdp'>upload_to_kdp</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t32">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t32"><data value='init__'>PydanticAIAgentManager.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t36">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t36"><data value='execute_agent'>PydanticAIAgentManager.execute_agent</data></a></td>
                <td>26</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="20 26">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t88">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t88"><data value='execute_workflow'>PydanticAIAgentManager.execute_workflow</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t128">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t128"><data value='execute_manuscript_generator'>PydanticAIAgentManager._execute_manuscript_generator</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t143">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t143"><data value='execute_trend_analyzer'>PydanticAIAgentManager._execute_trend_analyzer</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t165">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t165"><data value='execute_sales_monitor'>PydanticAIAgentManager._execute_sales_monitor</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t178">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t178"><data value='execute_cover_designer'>PydanticAIAgentManager._execute_cover_designer</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t193">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t193"><data value='execute_kdp_uploader'>PydanticAIAgentManager._execute_kdp_uploader</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t210">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t210"><data value='execute_research_assistant'>PydanticAIAgentManager._execute_research_assistant</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t222">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t222"><data value='execute_personalization_engine'>PydanticAIAgentManager._execute_personalization_engine</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t234">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t234"><data value='execute_multimodal_generator'>PydanticAIAgentManager._execute_multimodal_generator</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t246">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t246"><data value='log_execution'>PydanticAIAgentManager._log_execution</data></a></td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="4 5">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t270">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t270"><data value='get_execution_history'>PydanticAIAgentManager.get_execution_history</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t287">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t287"><data value='get_agent_status'>PydanticAIAgentManager.get_agent_status</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t297">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t297"><data value='calculate_success_rate'>PydanticAIAgentManager._calculate_success_rate</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t309">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t309"><data value='execute_agent'>execute_agent</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t317">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t317"><data value='execute_workflow'>execute_workflow</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t324">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html#t324"><data value='get_agent_status'>get_agent_status</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html">app/agents/pydantic_ai_manager.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t97">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t97"><data value='generate_book_outline'>generate_book_outline</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t136">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t136"><data value='generate_chapter_content'>generate_chapter_content</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t171">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t171"><data value='expand_content'>expand_content</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t225">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t225"><data value='assess_manuscript_quality'>assess_manuscript_quality</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t262">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t262"><data value='save_manuscript_draft'>save_manuscript_draft</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t287">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html#t287"><data value='generate_manuscript'>generate_manuscript</data></a></td>
                <td>15</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="10 15">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>37</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="37 37">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t110">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t110"><data value='collect_kdp_sales_data'>collect_kdp_sales_data</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t154">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t154"><data value='analyze_performance_trends'>analyze_performance_trends</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t204">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t204"><data value='generate_market_comparison'>generate_market_comparison</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t252">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t252"><data value='forecast_performance'>forecast_performance</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t318">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t318"><data value='login_to_kdp'>_login_to_kdp</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t339">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t339"><data value='set_date_range'>_set_date_range</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t345">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t345"><data value='extract_sales_data'>_extract_sales_data</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t379">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html#t379"><data value='monitor_sales_performance'>monitor_sales_performance</data></a></td>
                <td>10</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="6 10">60%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>59</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="59 59">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t27">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t27"><data value='get_user_books'>get_user_books</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t51">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t51"><data value='get_book_performance'>get_book_performance</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t82">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t82"><data value='save_book_draft'>save_book_draft</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t123">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t123"><data value='scrape_amazon_bestsellers'>scrape_amazon_bestsellers</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t148">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t148"><data value='scrape_reddit_trends'>scrape_reddit_trends</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t171">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t171"><data value='analyze_competitor_books'>analyze_competitor_books</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t222">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t222"><data value='analyze_content_quality'>analyze_content_quality</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t263">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t263"><data value='extract_keywords'>extract_keywords</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t292">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t292"><data value='research_market_trends'>research_market_trends</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t333">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html#t333"><data value='validate_book_concept'>validate_book_concept</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html">app/agents/pydantic_ai_tools.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t103">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t103"><data value='analyze_amazon_market'>analyze_amazon_market</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t147">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t147"><data value='analyze_social_trends'>analyze_social_trends</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t191">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t191"><data value='identify_market_gaps'>identify_market_gaps</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t247">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t247"><data value='generate_strategic_recommendations'>generate_strategic_recommendations</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t302">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t302"><data value='analyze_market_trends'>analyze_market_trends</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t373">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html#t373"><data value='analyze_keyword_opportunities'>analyze_keyword_opportunities</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1197</td>
                <td>589</td>
                <td>0</td>
                <td class="right" data-ratio="608 1197">51%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
