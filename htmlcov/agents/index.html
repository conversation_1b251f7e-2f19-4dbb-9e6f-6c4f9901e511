<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_81f8c14c.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">51%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5___init___py.html">app/agents/__init__.py</a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html">app/agents/pydantic_ai_additional_agents.py</a></td>
                <td>106</td>
                <td>40</td>
                <td>0</td>
                <td class="right" data-ratio="66 106">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_base_py.html">app/agents/pydantic_ai_base.py</a></td>
                <td>167</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="150 167">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html">app/agents/pydantic_ai_cover_designer.py</a></td>
                <td>124</td>
                <td>63</td>
                <td>0</td>
                <td class="right" data-ratio="61 124">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html">app/agents/pydantic_ai_kdp_uploader.py</a></td>
                <td>169</td>
                <td>114</td>
                <td>0</td>
                <td class="right" data-ratio="55 169">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manager_py.html">app/agents/pydantic_ai_manager.py</a></td>
                <td>104</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="84 104">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html">app/agents/pydantic_ai_manuscript_generator.py</a></td>
                <td>91</td>
                <td>44</td>
                <td>0</td>
                <td class="right" data-ratio="47 91">52%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html">app/agents/pydantic_ai_sales_monitor.py</a></td>
                <td>153</td>
                <td>88</td>
                <td>0</td>
                <td class="right" data-ratio="65 153">42%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_tools_py.html">app/agents/pydantic_ai_tools.py</a></td>
                <td>130</td>
                <td>111</td>
                <td>0</td>
                <td class="right" data-ratio="19 130">15%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html">app/agents/pydantic_ai_trend_analyzer.py</a></td>
                <td>153</td>
                <td>92</td>
                <td>0</td>
                <td class="right" data-ratio="61 153">40%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1197</td>
                <td>589</td>
                <td>0</td>
                <td class="right" data-ratio="608 1197">51%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.1">coverage.py v7.9.1</a>,
            created at 2025-06-23 13:31 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_c78f3e75ce3283c5___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
