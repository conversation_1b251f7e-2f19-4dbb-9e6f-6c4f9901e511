{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "4a0acf5e608e8a5ff691cdbaf4d2642e", "files": {"z_c78f3e75ce3283c5___init___py": {"hash": "e6baa73cda2916dad605215f937a92e1", "index": {"url": "z_c78f3e75ce3283c5___init___py.html", "file": "app/agents/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py": {"hash": "75bf5ba8b6d39f777a476848fa5c0a3d", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_additional_agents_py.html", "file": "app/agents/pydantic_ai_additional_agents.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 106, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_base_py": {"hash": "596cb6ab86f3006ac09f58b86e4dc456", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_base_py.html", "file": "app/agents/pydantic_ai_base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 167, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py": {"hash": "ef99e6b49d0e6e17e81ec0302fbbc0fe", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_cover_designer_py.html", "file": "app/agents/pydantic_ai_cover_designer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 124, "n_excluded": 0, "n_missing": 63, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py": {"hash": "c874b0b8bc865895aff3adb9895e1603", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_kdp_uploader_py.html", "file": "app/agents/pydantic_ai_kdp_uploader.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 169, "n_excluded": 0, "n_missing": 114, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_manager_py": {"hash": "be995fcd74e3f3556fb3cf2c804d698f", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_manager_py.html", "file": "app/agents/pydantic_ai_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 104, "n_excluded": 0, "n_missing": 20, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py": {"hash": "edb17e0b7afef1c66119f0434c011c75", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_manuscript_generator_py.html", "file": "app/agents/pydantic_ai_manuscript_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py": {"hash": "46e0d5e04d9d45765dfdebc742ace7d4", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_sales_monitor_py.html", "file": "app/agents/pydantic_ai_sales_monitor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 88, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_tools_py": {"hash": "57915406d64e0bb766c9b80f04441996", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_tools_py.html", "file": "app/agents/pydantic_ai_tools.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 130, "n_excluded": 0, "n_missing": 111, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py": {"hash": "23c0214e86f61ed44900491a3ec50dfc", "index": {"url": "z_c78f3e75ce3283c5_pydantic_ai_trend_analyzer_py.html", "file": "app/agents/pydantic_ai_trend_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 153, "n_excluded": 0, "n_missing": 92, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}