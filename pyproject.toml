[tool.poetry]
name = "publish-ai"
version = "0.1.0"
description = "AI-powered e-book generation and publishing system"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.115.0"
uvicorn = {extras = ["standard"], version = "^0.32.0"}
pydantic = {extras = ["email"], version = "^2.5.0"}
sqlalchemy = "^2.0.23"
sqlmodel = "^0.0.14"
alembic = "^1.13.0"
redis = "^5.0.1"
celery = "^5.3.4"
httpx = "^0.28.1"
beautifulsoup4 = "^4.12.2"
selenium = "^4.15.2"
openai = "^1.30.0"
anthropic = "^0.52.0"
python-multipart = "^0.0.9"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-docx = "^1.1.0"
markdown = "^3.5.1"
pillow = "^10.1.0"
matplotlib = "^3.8.2"
requests = "^2.31.0"
pandas = "^2.1.4"
numpy = "^1.26.2"
pytrends = "^4.9.2"
supabase = "^2.0.2"
python-dotenv = "^1.0.0"
pdfkit = "^1.0.0"
ebooklib = "^0.18"
pydantic-settings = "^2.1.0"
pydantic-ai = "^0.3.2"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pytest-cov = "^6.2.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
