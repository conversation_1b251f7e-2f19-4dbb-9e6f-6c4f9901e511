<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="6" skipped="0" tests="13" time="3.568" timestamp="2025-06-23T13:31:50.660880" hostname="<PERSON><PERSON>sies-Mac-Pro-2.local"><testcase classname="tests.test_agents.test_performance_load.TestStressTests" name="test_error_recovery_under_load" time="0.088" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManuscriptGenerator" name="test_manuscript_generation_error_handling" time="0.081"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:260: in test_manuscript_generation_error_handling
    assert 'error' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestTrendAnalyzer" name="test_trend_analysis_error_handling" time="0.117"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:325: in test_trend_analysis_error_handling
    assert 'error' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_agent_execution_timeout" time="0.096"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:580: in test_agent_execution_timeout
    assert 'timeout' in result.error_message.lower() or 'error' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_invalid_agent_name" time="0.137"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:591: in test_invalid_agent_name
    assert 'unknown agent' in result.error_message.lower() or 'not found' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_malformed_workflow" time="0.767" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestMonitoringAndLogging" name="test_error_logging" time="0.107" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_get_user_books_database_error" time="0.114"><failure message="TypeError: get_user_books() got an unexpected keyword argument 'user_id'">tests/test_agents/test_pydantic_ai_tools.py:104: in test_get_user_books_database_error
    result = await get_user_books(mock_ctx, user_id=123)
E   TypeError: get_user_books() got an unexpected keyword argument 'user_id'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_analyze_competitor_books_error_handling" time="0.108" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_network_timeout_handling" time="0.099"><failure message="TypeError: ScrapingDependencies.__init__() got an unexpected keyword argument 'timeout'">tests/test_agents/test_pydantic_ai_tools.py:413: in test_network_timeout_handling
    mock_deps = ScrapingDependencies(timeout=1)  # Very short timeout
E   TypeError: ScrapingDependencies.__init__() got an unexpected keyword argument 'timeout'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_invalid_input_handling" time="0.106" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_large_data_processing" time="0.119" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_concurrent_tool_execution" time="0.110" /></testsuite></testsuites>