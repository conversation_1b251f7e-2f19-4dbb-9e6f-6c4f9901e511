<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="16" skipped="0" tests="33" time="7.974" timestamp="2025-06-23T13:31:31.522375" hostname="Marckensies-Mac-Pro-2.local"><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIBase" name="test_agent_registry_initialization" time="0.088" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIBase" name="test_all_agents_registered" time="0.085" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIBase" name="test_dependency_classes" time="0.087" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_manager_initialization" time="0.086" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_get_agent_status" time="0.116"><failure message="AssertionError: assert 'last_execution' in {'recent_executions': 0, 'registered_agents': ['manuscript_generator', 'outline_generator', 'chapter_generator', 'trend_analyzer', 'keyword_analyzer', 'sales_monitor', ...], 'success_rate': 0.0, 'total_executions': 0}">tests/test_agents/test_pydantic_ai_agents.py:139: in test_get_agent_status
    assert 'last_execution' in status
E   AssertionError: assert 'last_execution' in {'recent_executions': 0, 'registered_agents': ['manuscript_generator', 'outline_generator', 'chapter_generator', 'trend_analyzer', 'keyword_analyzer', 'sales_monitor', ...], 'success_rate': 0.0, 'total_executions': 0}</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_execute_agent_manuscript_generator" time="0.083"><failure message="AssertionError: assert False is True&#10; +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 34, 457368), agent_name='pydantic_ai_manuscript_generator', execution_time=2.3e-05, metadata=None).success">tests/test_agents/test_pydantic_ai_agents.py:166: in test_execute_agent_manuscript_generator
    assert result.success is True
E   AssertionError: assert False is True
E    +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 34, 457368), agent_name='pydantic_ai_manuscript_generator', execution_time=2.3e-05, metadata=None).success</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPydanticAIManager" name="test_execute_workflow" time="0.085" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManuscriptGenerator" name="test_generate_manuscript_success" time="0.085"><failure message="AssertionError: assert False is True&#10; +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 34, 643476), agent_name='pydantic_ai_manuscript_generator', execution_time=2.8e-05, metadata=None).success">tests/test_agents/test_pydantic_ai_agents.py:223: in test_generate_manuscript_success
    assert result.success is True
E   AssertionError: assert False is True
E    +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 34, 643476), agent_name='pydantic_ai_manuscript_generator', execution_time=2.8e-05, metadata=None).success</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManuscriptGenerator" name="test_generate_manuscript_with_custom_params" time="0.082"><failure message="AssertionError: assert False is True&#10; +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 34, 746506), agent_name='pydantic_ai_manuscript_generator', execution_time=2.8e-05, metadata=None).success">tests/test_agents/test_pydantic_ai_agents.py:245: in test_generate_manuscript_with_custom_params
    assert result.success is True
E   AssertionError: assert False is True
E    +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 34, 746506), agent_name='pydantic_ai_manuscript_generator', execution_time=2.8e-05, metadata=None).success</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestManuscriptGenerator" name="test_manuscript_generation_error_handling" time="0.079"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:260: in test_manuscript_generation_error_handling
    assert 'error' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestTrendAnalyzer" name="test_analyze_market_trends_success" time="0.079" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestTrendAnalyzer" name="test_trend_analysis_with_depth" time="0.087"><failure message="TypeError: analyze_market_trends() got an unexpected keyword argument 'depth'">tests/test_agents/test_pydantic_ai_agents.py:307: in test_trend_analysis_with_depth
    result = await analyze_market_trends(
E   TypeError: analyze_market_trends() got an unexpected keyword argument 'depth'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestTrendAnalyzer" name="test_trend_analysis_error_handling" time="0.094"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:325: in test_trend_analysis_error_handling
    assert 'error' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestSalesMonitor" name="test_monitor_sales_performance_success" time="0.095" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestSalesMonitor" name="test_sales_monitoring_custom_params" time="0.104" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestCoverDesigner" name="test_design_book_cover_success" time="0.097"><failure message="TypeError: design_book_cover() missing 1 required positional argument: 'author'">tests/test_agents/test_pydantic_ai_agents.py:417: in test_design_book_cover_success
    result = await design_book_cover(
E   TypeError: design_book_cover() missing 1 required positional argument: 'author'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestKDPUploader" name="test_upload_to_kdp_success" time="0.091"><failure message="TypeError: upload_to_kdp() missing 2 required positional arguments: 'manuscript_file' and 'cover_file'">tests/test_agents/test_pydantic_ai_agents.py:457: in test_upload_to_kdp_success
    result = await upload_to_kdp(
E   TypeError: upload_to_kdp() missing 2 required positional arguments: 'manuscript_file' and 'cover_file'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAdditionalAgents" name="test_research_assistant_success" time="0.092"><failure message="TypeError: research_topic() got an unexpected keyword argument 'research_scope'">tests/test_agents/test_pydantic_ai_agents.py:495: in test_research_assistant_success
    result = await research_topic(
E   TypeError: research_topic() got an unexpected keyword argument 'research_scope'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAdditionalAgents" name="test_personalization_engine_success" time="0.172"><failure message="TypeError: personalize_content() got an unexpected keyword argument 'content_brief'">tests/test_agents/test_pydantic_ai_agents.py:525: in test_personalization_engine_success
    result = await personalize_content(
E   TypeError: personalize_content() got an unexpected keyword argument 'content_brief'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestAdditionalAgents" name="test_multimodal_generator_success" time="0.091" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_agent_execution_timeout" time="0.096"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:580: in test_agent_execution_timeout
    assert 'timeout' in result.error_message.lower() or 'error' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_invalid_agent_name" time="0.106"><failure message="AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'">tests/test_agents/test_pydantic_ai_agents.py:591: in test_invalid_agent_name
    assert 'unknown agent' in result.error_message.lower() or 'not found' in result.error_message.lower()
/Users/<USER>/.local/share/virtualenvs/OpenDevin-eyq9MYyR/lib/python3.11/site-packages/pydantic/main.py:991: in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
E   AttributeError: 'AgentExecutionResult' object has no attribute 'error_message'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestErrorHandling" name="test_malformed_workflow" time="1.037" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestIntegration" name="test_full_book_creation_workflow" time="0.101" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestIntegration" name="test_agent_registry_consistency" time="0.097" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestPerformanceAndScaling" name="test_memory_usage_optimization" time="0.096" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDataValidation" name="test_input_sanitization" time="0.097"><failure message="TypeError: upload_to_kdp() missing 3 required positional arguments: 'author', 'manuscript_file', and 'cover_file'">tests/test_agents/test_pydantic_ai_agents.py:727: in test_input_sanitization
    result = await upload_to_kdp(**malicious_input, genre='test')
E   TypeError: upload_to_kdp() missing 3 required positional arguments: 'author', 'manuscript_file', and 'cover_file'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDataValidation" name="test_data_type_validation" time="0.098" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestDataValidation" name="test_empty_input_handling" time="0.112" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestConfigurationAndSettings" name="test_agent_configuration" time="0.101"><failure message="AssertionError: assert False&#10; +  where False = hasattr(Agent(model=OpenAIModel(), name=None, end_strategy='early', model_settings=None, output_type=&lt;class 'app.agents.pydantic_ai_manuscript_generator.ManuscriptGenerationResult'&gt;, instrument=None), 'deps_type')">tests/test_agents/test_pydantic_ai_agents.py:781: in test_agent_configuration
    assert hasattr(agent, 'deps_type')
E   AssertionError: assert False
E    +  where False = hasattr(Agent(model=OpenAIModel(), name=None, end_strategy='early', model_settings=None, output_type=&lt;class 'app.agents.pydantic_ai_manuscript_generator.ManuscriptGenerationResult'&gt;, instrument=None), 'deps_type')</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestConfigurationAndSettings" name="test_dependency_injection" time="0.104"><failure message="TypeError: ScrapingDependencies.__init__() got an unexpected keyword argument 'timeout'">tests/test_agents/test_pydantic_ai_agents.py:792: in test_dependency_injection
    scraping_deps = ScrapingDependencies(headless=True, timeout=30)
E   TypeError: ScrapingDependencies.__init__() got an unexpected keyword argument 'timeout'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestMonitoringAndLogging" name="test_execution_tracking" time="0.108" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestMonitoringAndLogging" name="test_error_logging" time="0.110" /></testsuite></testsuites>