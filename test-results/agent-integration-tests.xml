<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="0" skipped="0" tests="2" time="1.143" timestamp="2025-06-23T13:31:48.030023" hostname="Marckensies-Mac-Pro-2.local"><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestIntegration" name="test_full_book_creation_workflow" time="0.077" /><testcase classname="tests.test_agents.test_pydantic_ai_agents.TestIntegration" name="test_agent_registry_consistency" time="0.075" /></testsuite></testsuites>