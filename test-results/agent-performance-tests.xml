<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="1" skipped="0" tests="13" time="17.316" timestamp="2025-06-23T13:31:56.332057" hostname="<PERSON><PERSON>sies-Mac-Pro-2.local"><testcase classname="tests.test_agents.test_performance_load.TestPerformanceMetrics" name="test_single_agent_execution_time" time="0.092" /><testcase classname="tests.test_agents.test_performance_load.TestPerformanceMetrics" name="test_concurrent_agent_performance" time="0.097" /><testcase classname="tests.test_agents.test_performance_load.TestPerformanceMetrics" name="test_workflow_execution_performance" time="0.091" /><testcase classname="tests.test_agents.test_performance_load.TestMemoryUsage" name="test_memory_usage_single_execution" time="0.176" /><testcase classname="tests.test_agents.test_performance_load.TestMemoryUsage" name="test_memory_usage_multiple_executions" time="0.459" /><testcase classname="tests.test_agents.test_performance_load.TestMemoryUsage" name="test_memory_leak_detection" time="0.699" /><testcase classname="tests.test_agents.test_performance_load.TestScalabilityLimits" name="test_high_concurrency_limits" time="0.125" /><testcase classname="tests.test_agents.test_performance_load.TestScalabilityLimits" name="test_large_data_processing" time="0.098"><failure message="AssertionError: assert False is True&#10; +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 59, 568258), agent_name='pydantic_ai_manuscript_generator', execution_time=3e-05, metadata=None).success">tests/test_agents/test_performance_load.py:246: in test_large_data_processing
    assert result.success is True
E   AssertionError: assert False is True
E    +  where False = AgentExecutionResult(success=False, data=None, error='No opportunities found in trend data', timestamp=datetime.datetime(2025, 6, 23, 13, 31, 59, 568258), agent_name='pydantic_ai_manuscript_generator', execution_time=3e-05, metadata=None).success</failure></testcase><testcase classname="tests.test_agents.test_performance_load.TestScalabilityLimits" name="test_rapid_sequential_requests" time="0.109" /><testcase classname="tests.test_agents.test_performance_load.TestResourceUtilization" name="test_cpu_utilization" time="3.151" /><testcase classname="tests.test_agents.test_performance_load.TestResourceUtilization" name="test_thread_pool_efficiency" time="0.313" /><testcase classname="tests.test_agents.test_performance_load.TestStressTests" name="test_sustained_load" time="10.099" /><testcase classname="tests.test_agents.test_performance_load.TestStressTests" name="test_error_recovery_under_load" time="0.084" /></testsuite></testsuites>