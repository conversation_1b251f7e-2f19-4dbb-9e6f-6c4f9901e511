<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="8" skipped="0" tests="23" time="3.178" timestamp="2025-06-23T13:31:42.891601" hostname="Marc<PERSON>sies-Mac-Pro-2.local"><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_get_user_books_success" time="0.094"><failure message="TypeError: get_user_books() got an unexpected keyword argument 'user_id'">tests/test_agents/test_pydantic_ai_tools.py:70: in test_get_user_books_success
    result = await get_user_books(mock_ctx, user_id=123)
E   TypeError: get_user_books() got an unexpected keyword argument 'user_id'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_get_user_books_empty_result" time="0.084"><failure message="TypeError: get_user_books() got an unexpected keyword argument 'user_id'">tests/test_agents/test_pydantic_ai_tools.py:89: in test_get_user_books_empty_result
    result = await get_user_books(mock_ctx, user_id=999)
E   TypeError: get_user_books() got an unexpected keyword argument 'user_id'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestDatabaseTools" name="test_get_user_books_database_error" time="0.096"><failure message="TypeError: get_user_books() got an unexpected keyword argument 'user_id'">tests/test_agents/test_pydantic_ai_tools.py:104: in test_get_user_books_database_error
    result = await get_user_books(mock_ctx, user_id=123)
E   TypeError: get_user_books() got an unexpected keyword argument 'user_id'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_amazon_bestsellers_success" time="0.087" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_amazon_bestsellers_large_limit" time="0.075" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_reddit_trends_success" time="0.078" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_scrape_reddit_trends_empty_subreddits" time="0.074" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_analyze_competitor_books_success" time="0.118"><failure message="AssertionError: assert 'total_books' in {'average_price': 16.49, 'average_rating': 5.4, 'average_review_count': 0, 'market_saturation': 'low', ...}">tests/test_agents/test_pydantic_ai_tools.py:187: in test_analyze_competitor_books_success
    assert 'total_books' in result
E   AssertionError: assert 'total_books' in {'average_price': 16.49, 'average_rating': 5.4, 'average_review_count': 0, 'market_saturation': 'low', ...}</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestScrapingTools" name="test_analyze_competitor_books_error_handling" time="0.119" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_analyze_content_quality_success" time="0.094" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_analyze_content_quality_with_criteria" time="0.080"><failure message="AssertionError: assert 'originality' in {'engagement': 75, 'readability': 99.96, 'structure': 85, 'value': 80}">tests/test_agents/test_pydantic_ai_tools.py:250: in test_analyze_content_quality_with_criteria
    assert criterion in result['criteria_scores']
E   AssertionError: assert 'originality' in {'engagement': 75, 'readability': 99.96, 'structure': 85, 'value': 80}</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_analyze_content_quality_empty_content" time="0.073" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_extract_keywords_success" time="0.079" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_extract_keywords_custom_limit" time="0.096" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestContentAnalysisTools" name="test_extract_keywords_short_content" time="0.069" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_research_market_trends_success" time="0.071"><failure message="AssertionError: assert 'competition_level' in {'category': 'business', 'growth_rate': 15.5, 'insights': ['business market is showing positive growth', 'High demand for practical guides', 'Price point sweet spot is $2.99-$4.99'], 'market_size': 50, ...}">tests/test_agents/test_pydantic_ai_tools.py:335: in test_research_market_trends_success
    assert 'competition_level' in result
E   AssertionError: assert 'competition_level' in {'category': 'business', 'growth_rate': 15.5, 'insights': ['business market is showing positive growth', 'High demand for practical guides', 'Price point sweet spot is $2.99-$4.99'], 'market_size': 50, ...}</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_research_market_trends_different_timeframes" time="0.085" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_validate_book_concept_success" time="0.065"><failure message="AssertionError: assert 'market_potential' in {'category': 'business', 'competition_level': 'low', 'market_saturation': 'medium', 'recommendations': ['Good market opportunity', 'Proceed with development', 'Monitor competitor releases'], ...}">tests/test_agents/test_pydantic_ai_tools.py:380: in test_validate_book_concept_success
    assert 'market_potential' in result
E   AssertionError: assert 'market_potential' in {'category': 'business', 'competition_level': 'low', 'market_saturation': 'medium', 'recommendations': ['Good market opportunity', 'Proceed with development', 'Monitor competitor releases'], ...}</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestMarketResearchTools" name="test_validate_book_concept_edge_cases" time="0.081" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_network_timeout_handling" time="0.086"><failure message="TypeError: ScrapingDependencies.__init__() got an unexpected keyword argument 'timeout'">tests/test_agents/test_pydantic_ai_tools.py:413: in test_network_timeout_handling
    mock_deps = ScrapingDependencies(timeout=1)  # Very short timeout
E   TypeError: ScrapingDependencies.__init__() got an unexpected keyword argument 'timeout'</failure></testcase><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_invalid_input_handling" time="0.076" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_large_data_processing" time="0.089" /><testcase classname="tests.test_agents.test_pydantic_ai_tools.TestErrorHandlingAndEdgeCases" name="test_concurrent_tool_execution" time="0.094" /></testsuite></testsuites>