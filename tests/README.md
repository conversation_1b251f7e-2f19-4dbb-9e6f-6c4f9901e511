# PydanticAI Agents Test Suite

Comprehensive test coverage for all 13 PydanticAI agents with full functionality, performance, and error handling testing.

## 📋 Test Overview

### Test Coverage Statistics

- **Total Agents Tested**: 13
- **Test Files**: 4 main test files
- **Test Categories**: Unit, Integration, Performance, Load, Error Handling
- **Coverage Target**: >95% code coverage
- **Performance Benchmarks**: Response time, memory usage, concurrency

### Agents Under Test

1. **Manuscript Generation** (3 agents):

   - `manuscript_generator` - Complete manuscript generation
   - `outline_generator` - Book outline creation
   - `chapter_generator` - Individual chapter writing

2. **Market Analysis** (2 agents):

   - `trend_analyzer` - Market trend analysis
   - `keyword_analyzer` - Keyword research and optimization

3. **Sales & Analytics** (2 agents):

   - `sales_monitor` - Sales performance monitoring
   - `sales_insights_generator` - AI-powered sales insights

4. **Design & Publishing** (2 agents):

   - `cover_designer` - Book cover design
   - `cover_market_analyzer` - Cover market analysis

5. **Publishing & Distribution** (1 agent):

   - `kdp_uploader` - Amazon KDP upload automation

6. **Advanced Features** (3 agents):
   - `research_assistant` - Topic research and validation
   - `personalization_engine` - Content personalization
   - `multimodal_generator` - Multi-format content generation

## 🚀 Quick Start

### Installation

```bash
# Install test dependencies
pip install -r tests/requirements-test.txt

# Install project dependencies
pip install -r requirements.txt
```

### Running Tests

```bash
# Run agent tests specifically
python tests/test_agents/run_agent_tests.py

# Run all tests with coverage
python tests/run_tests.py

# Run specific test suite
python tests/run_tests.py --suite unit
python tests/run_tests.py --suite performance
python tests/run_tests.py --suite integration

# Run tests directly with pytest
pytest tests/ -v --cov=app.agents
```

## 📁 Test Structure

```text
tests/
├── README.md                    # This documentation
├── conftest.py                  # Global pytest configuration and fixtures
├── requirements-test.txt        # Test dependencies
├── run_tests.py                # Comprehensive test runner
├── test_agents/                # PydanticAI agent tests (moved here)
│   ├── README.md               # Agent-specific test documentation
│   ├── __init__.py             # Package initialization
│   ├── conftest.py             # Agent-specific fixtures
│   ├── run_agent_tests.py      # Dedicated agent test runner
│   ├── test_pydantic_ai_agents.py  # Main agent functionality tests
│   ├── test_pydantic_ai_tools.py   # Agent tools and utilities tests
│   └── test_performance_load.py    # Agent performance and load tests
├── test_api/                   # API tests
├── test_services/              # Service tests
└── ...                         # Other test categories
```

## 🧪 Test Categories

### 1. Unit Tests (`test_pydantic_ai_agents.py`)

**Coverage**: Individual agent functionality, success/failure scenarios, data validation

**Test Classes**:

- `TestPydanticAIBase` - Base infrastructure and registry
- `TestPydanticAIManager` - Agent orchestration and management
- `TestManuscriptGenerator` - Manuscript generation agents
- `TestTrendAnalyzer` - Market trend analysis agents
- `TestSalesMonitor` - Sales monitoring agents
- `TestCoverDesigner` - Cover design agent
- `TestKDPUploader` - KDP upload agent
- `TestAdditionalAgents` - Research, personalization, multimodal agents
- `TestErrorHandling` - Error scenarios and edge cases
- `TestIntegration` - Agent interaction workflows

**Key Test Scenarios**:

```python
# Example: Testing manuscript generation
async def test_generate_manuscript_success():
    result = await generate_manuscript(
        trend_data={'category': 'self-help', 'keywords': ['productivity']},
        style='engaging',
        target_length=8000
    )
    assert result.success is True
    assert 'manuscript' in result.data
    assert result.data['word_count'] == 8500
```

### 2. Tools Tests (`test_pydantic_ai_tools.py`)

**Coverage**: Database operations, web scraping, content analysis, market research

**Test Classes**:

- `TestDatabaseTools` - Database operations and queries
- `TestScrapingTools` - Web scraping functionality
- `TestContentAnalysisTools` - AI-powered content analysis
- `TestMarketResearchTools` - Market research and validation
- `TestErrorHandlingAndEdgeCases` - Tool error scenarios

**Key Test Scenarios**:

```python
# Example: Testing content quality analysis
async def test_analyze_content_quality_success():
    result = await analyze_content_quality(mock_ctx, content)
    assert isinstance(result, dict)
    assert 'overall_score' in result
    assert 0 <= result['overall_score'] <= 100
```

### 3. Performance Tests (`test_performance_load.py`)

**Coverage**: Response times, memory usage, concurrency, scalability limits

**Test Classes**:

- `TestPerformanceMetrics` - Execution time benchmarks
- `TestMemoryUsage` - Memory consumption and leak detection
- `TestScalabilityLimits` - Concurrency and data processing limits
- `TestResourceUtilization` - CPU and system resource usage
- `TestStressTests` - System behavior under extreme load

**Performance Benchmarks**:

- Single agent execution: < 5 seconds
- Complex workflow: < 10 seconds
- Memory usage: < 100MB increase per execution
- Concurrency: Support 50+ concurrent agents
- CPU usage: < 90% peak utilization

## 🔧 Test Configuration

### Fixtures (`conftest.py`)

**Mock Dependencies**:

```python
@pytest.fixture
def mock_ai_deps() -> AIModelDependencies:
    return AIModelDependencies(
        api_key="test_api_key",
        model_name="gpt-4",
        temperature=0.7,
        max_tokens=2000
    )
```

**Sample Data**:

```python
@pytest.fixture
def sample_trend_data() -> Dict[str, Any]:
    return {
        'category': 'business',
        'keywords': ['productivity', 'success'],
        'opportunity_score': 85
    }
```

### Test Markers

- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.performance` - Performance tests
- `@pytest.mark.load` - Load tests
- `@pytest.mark.slow` - Long-running tests

## 📊 Coverage Reports

### HTML Coverage Report

```bash
pytest --cov=app.agents --cov-report=html
# View at htmlcov/index.html
```

### Terminal Coverage Report

```bash
pytest --cov=app.agents --cov-report=term-missing
```

### XML Coverage Report (for CI/CD)

```bash
pytest --cov=app.agents --cov-report=xml
```

## 🚨 Error Testing

### Error Scenarios Covered

1. **Network Failures**: Timeout, connection errors
2. **API Failures**: Invalid responses, rate limiting
3. **Data Validation**: Malformed inputs, type errors
4. **Resource Exhaustion**: Memory limits, CPU overload
5. **Dependency Failures**: Database unavailable, service down

### Example Error Test

```python
async def test_agent_execution_timeout():
    with patch('agent.run') as mock_run:
        mock_run.side_effect = asyncio.TimeoutError("Timeout")
        result = await execute_agent('test_agent', {})
        assert result.success is False
        assert 'timeout' in result.error_message.lower()
```

## 🔄 Continuous Integration

### GitHub Actions Example

```yaml
name: Test PydanticAI Agents
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install -r tests/requirements-test.txt
      - name: Run tests
        run: python tests/run_tests.py
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 📈 Performance Monitoring

### Benchmarking

```python
# Example performance test
@pytest.mark.performance
async def test_concurrent_agent_performance():
    start_time = time.time()
    tasks = [execute_agent('agent', {}) for _ in range(20)]
    await asyncio.gather(*tasks)
    execution_time = time.time() - start_time
    assert execution_time < 5.0  # Should complete within 5 seconds
```

### Memory Profiling

```python
def test_memory_usage():
    initial_memory = get_memory_usage()
    # Execute agent operations
    final_memory = get_memory_usage()
    memory_increase = final_memory - initial_memory
    assert memory_increase < 100  # Less than 100MB increase
```

## 🐛 Debugging Tests

### Running Individual Tests

```bash
# Run specific test
pytest tests/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_generate_manuscript_success -v

# Run with debugging
pytest tests/test_pydantic_ai_agents.py -v -s --tb=long

# Run with coverage for specific module
pytest tests/test_pydantic_ai_tools.py --cov=app.agents.pydantic_ai_tools --cov-report=term-missing
```

### Test Output Analysis

```bash
# Generate detailed HTML report
pytest --html=report.html --self-contained-html

# Generate JSON report for analysis
pytest --json-report --json-report-file=report.json
```

## 📝 Writing New Tests

### Test Template

```python
class TestNewAgent:
    """Test new agent functionality"""

    @pytest.mark.asyncio
    async def test_agent_success(self, mock_ai_deps):
        """Test successful agent execution"""
        with patch('agent.run') as mock_run:
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {'result': 'success'}
            mock_run.return_value = mock_result

            result = await execute_agent('new_agent', {'param': 'value'})

            assert result.success is True
            assert 'result' in result.data

    @pytest.mark.asyncio
    async def test_agent_error_handling(self):
        """Test agent error handling"""
        with patch('agent.run') as mock_run:
            mock_run.side_effect = Exception("Test error")

            result = await execute_agent('new_agent', {})

            assert result.success is False
            assert 'error' in result.error_message.lower()
```

## 🎯 Best Practices

1. **Mock External Dependencies**: Use mocks for API calls, database operations
2. **Test Both Success and Failure**: Cover happy path and error scenarios
3. **Use Realistic Test Data**: Provide meaningful sample data
4. **Performance Awareness**: Include performance assertions
5. **Async Testing**: Properly handle async operations with pytest-asyncio
6. **Clean Fixtures**: Use fixtures for reusable test data and mocks
7. **Clear Assertions**: Write descriptive assertion messages
8. **Test Isolation**: Ensure tests don't depend on each other

## 📞 Support

For test-related issues:

1. Check test output and error messages
2. Review coverage reports for missing test areas
3. Run individual tests for debugging
4. Check fixture setup and mock configurations
5. Verify test dependencies are installed correctly

The test suite provides comprehensive coverage of all PydanticAI agents with robust error handling, performance monitoring, and detailed reporting capabilities.
