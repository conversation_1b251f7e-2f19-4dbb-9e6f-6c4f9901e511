# Testing Requirements for PydanticAI Agents
# Install with: pip install -r tests/requirements-test.txt

# Core testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-xdist>=3.3.0  # For parallel test execution
pytest-html>=3.2.0   # For HTML test reports
pytest-json-report>=1.5.0  # For JSON test reports

# Performance and load testing
psutil>=5.9.0         # For system resource monitoring
memory-profiler>=0.60.0  # For memory usage profiling
pytest-benchmark>=4.0.0  # For performance benchmarking

# Mocking and test utilities
pytest-mock>=3.11.0
responses>=0.23.0     # For HTTP request mocking
freezegun>=1.2.0      # For time mocking
factory-boy>=3.3.0    # For test data generation

# Code quality and linting (for test code)
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0

# Coverage reporting
coverage[toml]>=7.3.0

# Database testing
pytest-postgresql>=5.0.0  # For PostgreSQL testing
pytest-redis>=3.0.0       # For Redis testing

# Async testing utilities
aioresponses>=0.7.0   # For async HTTP mocking
pytest-timeout>=2.1.0 # For test timeouts

# Documentation testing
pytest-doctestplus>=1.0.0

# Test data and fixtures
Faker>=19.0.0         # For generating fake test data
hypothesis>=6.82.0    # For property-based testing

# Reporting and visualization
pytest-html>=3.2.0
pytest-json-report>=1.5.0
allure-pytest>=2.13.0  # For advanced test reporting

# Environment and configuration
python-dotenv>=1.0.0  # For environment variable management
pydantic-settings>=2.0.0  # For settings management in tests

# Additional utilities
click>=8.1.0          # For CLI test utilities
rich>=13.0.0          # For rich console output
tabulate>=0.9.0       # For table formatting in reports
