#!/usr/bin/env python3
"""
Comprehensive Test Runner for PydanticAI Agents
Runs all test suites with detailed reporting and coverage analysis.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any


class TestRunner:
    """Comprehensive test runner for PydanticAI agents"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_dir = Path(__file__).parent
        self.results = {}
        
    def run_command(self, command: List[str], description: str) -> Dict[str, Any]:
        """Run a command and capture results"""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            execution_time = time.time() - start_time
            
            success = result.returncode == 0
            
            print(f"Exit code: {result.returncode}")
            print(f"Execution time: {execution_time:.2f}s")
            
            if result.stdout:
                print(f"\nSTDOUT:\n{result.stdout}")
            
            if result.stderr:
                print(f"\nSTDERR:\n{result.stderr}")
            
            return {
                'success': success,
                'exit_code': result.returncode,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            print(f"❌ Test timed out after 5 minutes")
            return {
                'success': False,
                'exit_code': -1,
                'execution_time': 300,
                'stdout': '',
                'stderr': 'Test timed out'
            }
        except Exception as e:
            print(f"❌ Error running command: {e}")
            return {
                'success': False,
                'exit_code': -1,
                'execution_time': 0,
                'stdout': '',
                'stderr': str(e)
            }
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests for all agents"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_pydantic_ai_agents.py',
            '-v',
            '--tb=short',
            '--cov=app.agents',
            '--cov-report=html:htmlcov/unit',
            '--cov-report=term-missing',
            '--junit-xml=test-results/unit-tests.xml',
            '-m', 'not slow'
        ]
        
        return self.run_command(command, "Unit Tests for PydanticAI Agents")
    
    def run_tools_tests(self) -> Dict[str, Any]:
        """Run tests for PydanticAI tools"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_pydantic_ai_tools.py',
            '-v',
            '--tb=short',
            '--cov=app.agents.pydantic_ai_tools',
            '--cov-report=html:htmlcov/tools',
            '--cov-report=term-missing',
            '--junit-xml=test-results/tools-tests.xml'
        ]
        
        return self.run_command(command, "Tools Tests for PydanticAI")
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_pydantic_ai_agents.py::TestIntegration',
            '-v',
            '--tb=short',
            '--junit-xml=test-results/integration-tests.xml'
        ]
        
        return self.run_command(command, "Integration Tests")
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """Run performance and load tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_performance_load.py',
            '-v',
            '--tb=short',
            '--junit-xml=test-results/performance-tests.xml',
            '-s'  # Don't capture output for performance metrics
        ]
        
        return self.run_command(command, "Performance and Load Tests")
    
    def run_error_handling_tests(self) -> Dict[str, Any]:
        """Run error handling and edge case tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_pydantic_ai_agents.py::TestErrorHandling',
            'tests/test_pydantic_ai_tools.py::TestErrorHandlingAndEdgeCases',
            '-v',
            '--tb=short',
            '--junit-xml=test-results/error-handling-tests.xml'
        ]
        
        return self.run_command(command, "Error Handling Tests")
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests with comprehensive coverage"""
        command = [
            'python', '-m', 'pytest',
            'tests/',
            '-v',
            '--tb=short',
            '--cov=app.agents',
            '--cov-report=html:htmlcov/complete',
            '--cov-report=term-missing',
            '--cov-report=xml:coverage.xml',
            '--junit-xml=test-results/all-tests.xml',
            '--maxfail=10'  # Stop after 10 failures
        ]
        
        return self.run_command(command, "Complete Test Suite")
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are installed"""
        print("Checking test dependencies...")
        
        required_packages = [
            'pytest',
            'pytest-cov',
            'pytest-asyncio',
            'psutil'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} - MISSING")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\nMissing packages: {', '.join(missing_packages)}")
            print("Install with: pip install " + " ".join(missing_packages))
            return False
        
        return True
    
    def setup_test_environment(self):
        """Setup test environment"""
        # Create test results directory
        results_dir = self.project_root / 'test-results'
        results_dir.mkdir(exist_ok=True)
        
        # Create coverage directory
        coverage_dir = self.project_root / 'htmlcov'
        coverage_dir.mkdir(exist_ok=True)
        
        # Set environment variables
        os.environ['PYTHONPATH'] = str(self.project_root)
        os.environ['TESTING'] = 'true'
    
    def generate_summary_report(self):
        """Generate summary report of all test results"""
        print(f"\n{'='*80}")
        print("TEST EXECUTION SUMMARY")
        print(f"{'='*80}")
        
        total_time = 0
        total_tests = 0
        failed_suites = []
        
        for suite_name, result in self.results.items():
            status = "✅ PASSED" if result['success'] else "❌ FAILED"
            print(f"{suite_name:<30} {status:<10} ({result['execution_time']:.2f}s)")
            
            total_time += result['execution_time']
            
            if not result['success']:
                failed_suites.append(suite_name)
        
        print(f"\nTotal execution time: {total_time:.2f}s")
        
        if failed_suites:
            print(f"\nFailed test suites: {', '.join(failed_suites)}")
            print("\nCheck the detailed output above for error information.")
        else:
            print("\n🎉 All test suites passed successfully!")
        
        # Coverage information
        coverage_file = self.project_root / 'htmlcov' / 'complete' / 'index.html'
        if coverage_file.exists():
            print(f"\n📊 Coverage report available at: {coverage_file}")
        
        print(f"{'='*80}")
    
    def main(self):
        """Main test execution"""
        parser = argparse.ArgumentParser(description='Run PydanticAI agent tests')
        parser.add_argument('--suite', choices=[
            'unit', 'tools', 'integration', 'performance', 'error-handling', 'all'
        ], default='all', help='Test suite to run')
        parser.add_argument('--skip-deps-check', action='store_true',
                          help='Skip dependency check')
        
        args = parser.parse_args()
        
        print("PydanticAI Agent Test Runner")
        print(f"Project root: {self.project_root}")
        print(f"Test directory: {self.test_dir}")
        
        # Check dependencies
        if not args.skip_deps_check and not self.check_dependencies():
            sys.exit(1)
        
        # Setup environment
        self.setup_test_environment()
        
        # Run selected test suite
        if args.suite == 'unit':
            self.results['Unit Tests'] = self.run_unit_tests()
        elif args.suite == 'tools':
            self.results['Tools Tests'] = self.run_tools_tests()
        elif args.suite == 'integration':
            self.results['Integration Tests'] = self.run_integration_tests()
        elif args.suite == 'performance':
            self.results['Performance Tests'] = self.run_performance_tests()
        elif args.suite == 'error-handling':
            self.results['Error Handling Tests'] = self.run_error_handling_tests()
        elif args.suite == 'all':
            self.results['Unit Tests'] = self.run_unit_tests()
            self.results['Tools Tests'] = self.run_tools_tests()
            self.results['Integration Tests'] = self.run_integration_tests()
            self.results['Error Handling Tests'] = self.run_error_handling_tests()
            self.results['Performance Tests'] = self.run_performance_tests()
        
        # Generate summary
        self.generate_summary_report()
        
        # Exit with error code if any tests failed
        if any(not result['success'] for result in self.results.values()):
            sys.exit(1)


if __name__ == '__main__':
    runner = TestRunner()
    runner.main()
