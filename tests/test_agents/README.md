# PydanticAI Agents Test Suite

Comprehensive test coverage for all 13 PydanticAI agents with full functionality, performance, and error handling testing.

## 📋 Test Overview

This dedicated test suite provides complete coverage for all PydanticAI agents in the system:

### 🤖 Agents Under Test (13 Total)

1. **Manuscript Generation** (3 agents):
   - `manuscript_generator` - Complete manuscript generation
   - `outline_generator` - Book outline creation  
   - `chapter_generator` - Individual chapter writing

2. **Market Analysis** (2 agents):
   - `trend_analyzer` - Market trend analysis
   - `keyword_analyzer` - Keyword research and optimization

3. **Sales & Analytics** (2 agents):
   - `sales_monitor` - Sales performance monitoring
   - `sales_insights_generator` - AI-powered sales insights

4. **Design & Publishing** (2 agents):
   - `cover_designer` - Book cover design
   - `cover_market_analyzer` - Cover market analysis

5. **Publishing & Distribution** (1 agent):
   - `kdp_uploader` - Amazon KDP upload automation

6. **Advanced Features** (3 agents):
   - `research_assistant` - Topic research and validation
   - `personalization_engine` - Content personalization
   - `multimodal_generator` - Multi-format content generation

## 🚀 Quick Start

### Running Agent Tests

```bash
# Run all agent tests
python tests/test_agents/run_agent_tests.py

# Run specific test suite
python tests/test_agents/run_agent_tests.py --suite functionality
python tests/test_agents/run_agent_tests.py --suite performance
python tests/test_agents/run_agent_tests.py --suite integration

# Run tests for specific agent
python tests/test_agents/run_agent_tests.py --agent manuscript_generator

# Run with pytest directly
pytest tests/test_agents/ -v --cov=app.agents
```

## 📁 Test Structure

```
tests/test_agents/
├── README.md                        # This documentation
├── __init__.py                      # Package initialization
├── conftest.py                      # Agent-specific test fixtures
├── run_agent_tests.py              # Dedicated agent test runner
├── test_pydantic_ai_agents.py      # Main agent functionality tests
├── test_pydantic_ai_tools.py       # Agent tools and utilities tests
└── test_performance_load.py        # Performance and load tests
```

## 🧪 Test Categories

### 1. Agent Functionality Tests (`test_pydantic_ai_agents.py`)

**Coverage**: Core agent functionality, success/failure scenarios, data validation

**Test Classes**:
- `TestPydanticAIBase` - Base infrastructure and registry
- `TestPydanticAIManager` - Agent orchestration and management
- `TestManuscriptGenerator` - Manuscript generation agents
- `TestTrendAnalyzer` - Market trend analysis agents
- `TestSalesMonitor` - Sales monitoring agents
- `TestCoverDesigner` - Cover design agent
- `TestKDPUploader` - KDP upload agent
- `TestAdditionalAgents` - Research, personalization, multimodal agents
- `TestErrorHandling` - Error scenarios and edge cases
- `TestIntegration` - Agent interaction workflows

### 2. Agent Tools Tests (`test_pydantic_ai_tools.py`)

**Coverage**: Database operations, web scraping, content analysis, market research

**Test Classes**:
- `TestDatabaseTools` - Database operations and queries
- `TestScrapingTools` - Web scraping functionality
- `TestContentAnalysisTools` - AI-powered content analysis
- `TestMarketResearchTools` - Market research and validation
- `TestErrorHandlingAndEdgeCases` - Tool error scenarios

### 3. Performance Tests (`test_performance_load.py`)

**Coverage**: Response times, memory usage, concurrency, scalability limits

**Test Classes**:
- `TestPerformanceMetrics` - Execution time benchmarks
- `TestMemoryUsage` - Memory consumption and leak detection
- `TestScalabilityLimits` - Concurrency and data processing limits
- `TestResourceUtilization` - CPU and system resource usage
- `TestStressTests` - System behavior under extreme load

## 📊 Performance Benchmarks

- **Single agent execution**: < 5 seconds
- **Complex workflow**: < 10 seconds
- **Memory usage**: < 100MB increase per execution
- **Concurrency**: Support 50+ concurrent agents
- **CPU usage**: < 90% peak utilization

## 🔧 Test Configuration

### Agent-Specific Fixtures (`conftest.py`)

```python
@pytest.fixture
def mock_ai_deps() -> AIModelDependencies:
    return AIModelDependencies(
        api_key="test_api_key",
        model_name="gpt-4",
        temperature=0.7,
        max_tokens=2000
    )

@pytest.fixture
def sample_trend_data() -> Dict[str, Any]:
    return {
        'category': 'business',
        'keywords': ['productivity', 'success'],
        'opportunity_score': 85
    }
```

## 🚨 Error Testing

### Error Scenarios Covered

1. **Network Failures**: Timeout, connection errors
2. **API Failures**: Invalid responses, rate limiting
3. **Data Validation**: Malformed inputs, type errors
4. **Resource Exhaustion**: Memory limits, CPU overload
5. **Dependency Failures**: Database unavailable, service down

## 📈 Coverage Reports

```bash
# Generate HTML coverage report for agents
pytest tests/test_agents/ --cov=app.agents --cov-report=html:htmlcov/agents

# View coverage report
open htmlcov/agents/index.html
```

## 🔄 Running Individual Agent Tests

```bash
# Test specific agent
pytest tests/test_agents/ -k "manuscript_generator" -v

# Test specific functionality
pytest tests/test_agents/ -k "trend_analyzer" -v

# Test error handling
pytest tests/test_agents/ -k "error" -v

# Test performance
pytest tests/test_agents/test_performance_load.py -v
```

## 🎯 Test Examples

### Testing Agent Execution
```python
@pytest.mark.asyncio
async def test_manuscript_generator_success():
    result = await generate_manuscript(
        trend_data={'category': 'self-help'},
        style='engaging',
        target_length=8000
    )
    assert result.success is True
    assert 'manuscript' in result.data
```

### Testing Error Handling
```python
@pytest.mark.asyncio
async def test_agent_timeout_handling():
    with patch('agent.run') as mock_run:
        mock_run.side_effect = asyncio.TimeoutError("Timeout")
        result = await execute_agent('test_agent', {})
        assert result.success is False
```

### Testing Performance
```python
@pytest.mark.performance
async def test_concurrent_execution():
    start_time = time.time()
    tasks = [execute_agent('agent', {}) for _ in range(20)]
    await asyncio.gather(*tasks)
    execution_time = time.time() - start_time
    assert execution_time < 5.0
```

## 🐛 Debugging Agent Tests

```bash
# Run with detailed output
pytest tests/test_agents/ -v -s --tb=long

# Run specific test with debugging
pytest tests/test_agents/test_pydantic_ai_agents.py::TestManuscriptGenerator::test_generate_manuscript_success -v -s

# Generate test report
pytest tests/test_agents/ --html=agent-test-report.html
```

## 📞 Support

For agent test issues:
1. Check agent test output and error messages
2. Review agent-specific coverage reports
3. Run individual agent tests for debugging
4. Verify agent dependencies and configurations
5. Check mock setups and fixture configurations

The agent test suite provides comprehensive coverage with robust error handling, performance monitoring, and detailed reporting specifically tailored for PydanticAI agents.
