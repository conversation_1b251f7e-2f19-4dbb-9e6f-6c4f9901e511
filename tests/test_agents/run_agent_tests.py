#!/usr/bin/env python3
"""
Dedicated Test Runner for PydanticAI Agents
Runs all agent test suites with detailed reporting and coverage analysis.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any


class AgentTestRunner:
    """Dedicated test runner for PydanticAI agents"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.test_agents_dir = Path(__file__).parent
        self.results = {}
        
    def run_command(self, command: List[str], description: str) -> Dict[str, Any]:
        """Run a command and capture results"""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(command)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            execution_time = time.time() - start_time
            
            success = result.returncode == 0
            
            print(f"Exit code: {result.returncode}")
            print(f"Execution time: {execution_time:.2f}s")
            
            if result.stdout:
                print(f"\nSTDOUT:\n{result.stdout}")
            
            if result.stderr:
                print(f"\nSTDERR:\n{result.stderr}")
            
            return {
                'success': success,
                'exit_code': result.returncode,
                'execution_time': execution_time,
                'stdout': result.stdout,
                'stderr': result.stderr
            }
            
        except subprocess.TimeoutExpired:
            print(f"❌ Test timed out after 5 minutes")
            return {
                'success': False,
                'exit_code': -1,
                'execution_time': 300,
                'stdout': '',
                'stderr': 'Test timed out'
            }
        except Exception as e:
            print(f"❌ Error running command: {e}")
            return {
                'success': False,
                'exit_code': -1,
                'execution_time': 0,
                'stdout': '',
                'stderr': str(e)
            }
    
    def run_agent_functionality_tests(self) -> Dict[str, Any]:
        """Run core agent functionality tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_agents/test_pydantic_ai_agents.py',
            '-v',
            '--tb=short',
            '--cov=app.agents',
            '--cov-report=html:htmlcov/agents',
            '--cov-report=term-missing',
            '--junit-xml=test-results/agent-functionality-tests.xml',
            '-m', 'not slow'
        ]
        
        return self.run_command(command, "Agent Functionality Tests")
    
    def run_agent_tools_tests(self) -> Dict[str, Any]:
        """Run agent tools tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_agents/test_pydantic_ai_tools.py',
            '-v',
            '--tb=short',
            '--cov=app.agents.pydantic_ai_tools',
            '--cov-report=html:htmlcov/agent-tools',
            '--cov-report=term-missing',
            '--junit-xml=test-results/agent-tools-tests.xml'
        ]
        
        return self.run_command(command, "Agent Tools Tests")
    
    def run_agent_performance_tests(self) -> Dict[str, Any]:
        """Run agent performance and load tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_agents/test_performance_load.py',
            '-v',
            '--tb=short',
            '--junit-xml=test-results/agent-performance-tests.xml',
            '-s'  # Don't capture output for performance metrics
        ]
        
        return self.run_command(command, "Agent Performance Tests")
    
    def run_individual_agent_tests(self, agent_name: str) -> Dict[str, Any]:
        """Run tests for a specific agent"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_agents/',
            '-v',
            '--tb=short',
            '-k', agent_name,
            '--junit-xml=f','test-results/{agent_name}-tests.xml'
        ]
        
        return self.run_command(command, f"Tests for {agent_name} Agent")
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """Run agent integration tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_agents/test_pydantic_ai_agents.py::TestIntegration',
            '-v',
            '--tb=short',
            '--junit-xml=test-results/agent-integration-tests.xml'
        ]
        
        return self.run_command(command, "Agent Integration Tests")
    
    def run_error_handling_tests(self) -> Dict[str, Any]:
        """Run agent error handling tests"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_agents/',
            '-v',
            '--tb=short',
            '-k', 'error or Error or exception or Exception',
            '--junit-xml=test-results/agent-error-handling-tests.xml'
        ]
        
        return self.run_command(command, "Agent Error Handling Tests")
    
    def run_all_agent_tests(self) -> Dict[str, Any]:
        """Run all agent tests with comprehensive coverage"""
        command = [
            'python', '-m', 'pytest',
            'tests/test_agents/',
            '-v',
            '--tb=short',
            '--cov=app.agents',
            '--cov-report=html:htmlcov/agents-complete',
            '--cov-report=term-missing',
            '--cov-report=xml:coverage-agents.xml',
            '--junit-xml=test-results/all-agent-tests.xml',
            '--maxfail=10'  # Stop after 10 failures
        ]
        
        return self.run_command(command, "Complete Agent Test Suite")
    
    def check_agent_dependencies(self) -> bool:
        """Check if all required dependencies for agent testing are installed"""
        print("Checking agent test dependencies...")
        
        required_packages = [
            'pytest',
            'pytest-cov',
            'pytest-asyncio',
            'psutil',
            'pydantic_ai'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                print(f"❌ {package} - MISSING")
                missing_packages.append(package)
        
        if missing_packages:
            print(f"\nMissing packages: {', '.join(missing_packages)}")
            print("Install with: pip install " + " ".join(missing_packages))
            return False
        
        return True
    
    def setup_agent_test_environment(self):
        """Setup test environment for agent testing"""
        # Create test results directory
        results_dir = self.project_root / 'test-results'
        results_dir.mkdir(exist_ok=True)
        
        # Create coverage directory
        coverage_dir = self.project_root / 'htmlcov'
        coverage_dir.mkdir(exist_ok=True)
        
        # Set environment variables
        os.environ['PYTHONPATH'] = str(self.project_root)
        os.environ['TESTING'] = 'true'
        os.environ['AGENT_TESTING'] = 'true'
    
    def generate_agent_test_summary(self):
        """Generate summary report of all agent test results"""
        print(f"\n{'='*80}")
        print("AGENT TEST EXECUTION SUMMARY")
        print(f"{'='*80}")
        
        total_time = 0
        total_tests = 0
        failed_suites = []
        
        for suite_name, result in self.results.items():
            status = "✅ PASSED" if result['success'] else "❌ FAILED"
            print(f"{suite_name:<40} {status:<10} ({result['execution_time']:.2f}s)")
            
            total_time += result['execution_time']
            
            if not result['success']:
                failed_suites.append(suite_name)
        
        print(f"\nTotal execution time: {total_time:.2f}s")
        
        if failed_suites:
            print(f"\nFailed test suites: {', '.join(failed_suites)}")
            print("\nCheck the detailed output above for error information.")
        else:
            print("\n🎉 All agent test suites passed successfully!")
        
        # Coverage information
        coverage_file = self.project_root / 'htmlcov' / 'agents-complete' / 'index.html'
        if coverage_file.exists():
            print(f"\n📊 Agent coverage report available at: {coverage_file}")
        
        print(f"{'='*80}")
    
    def main(self):
        """Main test execution for agents"""
        parser = argparse.ArgumentParser(description='Run PydanticAI agent tests')
        parser.add_argument('--suite', choices=[
            'functionality', 'tools', 'performance', 'integration', 
            'error-handling', 'all'
        ], default='all', help='Agent test suite to run')
        parser.add_argument('--agent', type=str, 
                          help='Run tests for specific agent (e.g., manuscript_generator)')
        parser.add_argument('--skip-deps-check', action='store_true',
                          help='Skip dependency check')
        
        args = parser.parse_args()
        
        print("PydanticAI Agent Test Runner")
        print(f"Project root: {self.project_root}")
        print(f"Agent tests directory: {self.test_agents_dir}")
        
        # Check dependencies
        if not args.skip_deps_check and not self.check_agent_dependencies():
            sys.exit(1)
        
        # Setup environment
        self.setup_agent_test_environment()
        
        # Run specific agent tests if requested
        if args.agent:
            self.results[f'{args.agent} Tests'] = self.run_individual_agent_tests(args.agent)
        # Run selected test suite
        elif args.suite == 'functionality':
            self.results['Agent Functionality Tests'] = self.run_agent_functionality_tests()
        elif args.suite == 'tools':
            self.results['Agent Tools Tests'] = self.run_agent_tools_tests()
        elif args.suite == 'performance':
            self.results['Agent Performance Tests'] = self.run_agent_performance_tests()
        elif args.suite == 'integration':
            self.results['Agent Integration Tests'] = self.run_integration_tests()
        elif args.suite == 'error-handling':
            self.results['Agent Error Handling Tests'] = self.run_error_handling_tests()
        elif args.suite == 'all':
            self.results['Agent Functionality Tests'] = self.run_agent_functionality_tests()
            self.results['Agent Tools Tests'] = self.run_agent_tools_tests()
            self.results['Agent Integration Tests'] = self.run_integration_tests()
            self.results['Agent Error Handling Tests'] = self.run_error_handling_tests()
            self.results['Agent Performance Tests'] = self.run_agent_performance_tests()
        
        # Generate summary
        self.generate_agent_test_summary()
        
        # Exit with error code if any tests failed
        if any(not result['success'] for result in self.results.values()):
            sys.exit(1)


if __name__ == '__main__':
    runner = AgentTestRunner()
    runner.main()
