"""
Performance and Load Testing for PydanticAI Agents
Tests system performance, memory usage, and scalability under various load conditions.
"""

import pytest
import asyncio
import time
import psutil
import gc
from unittest.mock import Mock, patch
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List, Dict, Any

# Import agents for testing
from app.agents.pydantic_ai_manager import (
    execute_agent,
    execute_workflow,
    get_agent_status
)

from app.agents.pydantic_ai_manuscript_generator import generate_manuscript
from app.agents.pydantic_ai_trend_analyzer import analyze_market_trends
from app.agents.pydantic_ai_sales_monitor import monitor_sales_performance


class TestPerformanceMetrics:
    """Test performance metrics and benchmarks"""
    
    @pytest.mark.asyncio
    async def test_single_agent_execution_time(self):
        """Test execution time for single agent"""
        with patch('app.agents.pydantic_ai_trend_analyzer.trend_analyzer.run') as mock_run:
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {'analysis': 'test'}
            mock_run.return_value = mock_result
            
            start_time = time.time()
            result = await analyze_market_trends(categories=['business'])
            execution_time = time.time() - start_time
            
            assert result.success is True
            assert execution_time < 5.0  # Should complete within 5 seconds
            print(f"Single agent execution time: {execution_time:.3f}s")
    
    @pytest.mark.asyncio
    async def test_concurrent_agent_performance(self):
        """Test performance with concurrent agent execution"""
        with patch('app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent') as mock_execute:
            mock_execute.return_value = Mock(success=True, data={'result': 'test'})
            
            # Test with increasing concurrency levels
            concurrency_levels = [1, 5, 10, 20]
            results = {}
            
            for level in concurrency_levels:
                start_time = time.time()
                
                tasks = [
                    execute_agent('trend_analyzer', {'categories': ['business']})
                    for _ in range(level)
                ]
                
                await asyncio.gather(*tasks)
                execution_time = time.time() - start_time
                
                results[level] = execution_time
                print(f"Concurrency {level}: {execution_time:.3f}s")
            
            # Performance should scale reasonably
            assert results[1] < 2.0  # Single execution should be fast
            assert results[20] < results[1] * 25  # 20x concurrency shouldn't be 25x slower
    
    @pytest.mark.asyncio
    async def test_workflow_execution_performance(self):
        """Test performance of complex workflows"""
        with patch('app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent') as mock_execute:
            mock_execute.return_value = Mock(success=True, data={'result': 'test'})
            
            # Complex workflow with multiple steps
            workflow = [
                {'agent_name': 'trend_analyzer', 'task_data': {'categories': ['business']}},
                {'agent_name': 'manuscript_generator', 'task_data': {'style': 'professional'}},
                {'agent_name': 'cover_designer', 'task_data': {'genre': 'business'}},
                {'agent_name': 'sales_monitor', 'task_data': {'date_range': 'last_30_days'}},
                {'agent_name': 'kdp_uploader', 'task_data': {'title': 'Test Book'}}
            ]
            
            start_time = time.time()
            results = await execute_workflow(workflow)
            execution_time = time.time() - start_time
            
            assert len(results) == 5
            assert all(result.success for result in results)
            assert execution_time < 10.0  # Complex workflow should complete within 10 seconds
            print(f"Complex workflow execution time: {execution_time:.3f}s")


class TestMemoryUsage:
    """Test memory usage and optimization"""
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    
    @pytest.mark.asyncio
    async def test_memory_usage_single_execution(self):
        """Test memory usage for single agent execution"""
        initial_memory = self.get_memory_usage()
        
        with patch('app.agents.pydantic_ai_manuscript_generator.manuscript_agent.run') as mock_run:
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {'manuscript': {'content': 'test'}}
            mock_run.return_value = mock_result
            
            await generate_manuscript(trend_data={'category': 'business'})
            
            peak_memory = self.get_memory_usage()
            
            # Force garbage collection
            gc.collect()
            
            final_memory = self.get_memory_usage()
            
            memory_increase = peak_memory - initial_memory
            memory_cleanup = peak_memory - final_memory
            
            print(f"Memory usage - Initial: {initial_memory:.1f}MB, "
                  f"Peak: {peak_memory:.1f}MB, Final: {final_memory:.1f}MB")
            print(f"Memory increase: {memory_increase:.1f}MB, Cleanup: {memory_cleanup:.1f}MB")
            
            # Memory increase should be reasonable
            assert memory_increase < 100  # Should not use more than 100MB for single execution
    
    @pytest.mark.asyncio
    async def test_memory_usage_multiple_executions(self):
        """Test memory usage with multiple sequential executions"""
        initial_memory = self.get_memory_usage()
        memory_readings = [initial_memory]
        
        with patch('app.agents.pydantic_ai_trend_analyzer.trend_analyzer.run') as mock_run:
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {'analysis': 'test'}
            mock_run.return_value = mock_result
            
            # Execute multiple times
            for i in range(10):
                await analyze_market_trends(categories=['business'])
                memory_readings.append(self.get_memory_usage())
                
                # Periodic garbage collection
                if i % 3 == 0:
                    gc.collect()
            
            final_memory = self.get_memory_usage()
            
            print(f"Memory progression: {[f'{m:.1f}' for m in memory_readings]}")
            
            # Memory should not grow indefinitely
            memory_growth = final_memory - initial_memory
            assert memory_growth < 200  # Should not grow more than 200MB
    
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self):
        """Test for potential memory leaks"""
        initial_memory = self.get_memory_usage()
        
        with patch('app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent') as mock_execute:
            mock_execute.return_value = Mock(success=True, data={'result': 'test'})
            
            # Execute many operations
            for batch in range(5):
                tasks = [
                    execute_agent('trend_analyzer', {'categories': ['business']})
                    for _ in range(20)
                ]
                await asyncio.gather(*tasks)
                
                # Force cleanup
                gc.collect()
                
                current_memory = self.get_memory_usage()
                memory_growth = current_memory - initial_memory
                
                print(f"Batch {batch + 1}: Memory growth: {memory_growth:.1f}MB")
                
                # Memory growth should stabilize
                if batch > 2:  # After warmup
                    assert memory_growth < 300  # Should not exceed 300MB growth


class TestScalabilityLimits:
    """Test system scalability and limits"""
    
    @pytest.mark.asyncio
    async def test_high_concurrency_limits(self):
        """Test system behavior under high concurrency"""
        with patch('app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent') as mock_execute:
            mock_execute.return_value = Mock(success=True, data={'result': 'test'})
            
            # Test with very high concurrency
            high_concurrency = 100
            
            start_time = time.time()
            
            tasks = [
                execute_agent('trend_analyzer', {'categories': ['business']})
                for _ in range(high_concurrency)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            execution_time = time.time() - start_time
            
            # Count successful executions
            successful = sum(1 for r in results if hasattr(r, 'success') and r.success)
            failed = len(results) - successful
            
            print(f"High concurrency test - Successful: {successful}, Failed: {failed}, "
                  f"Time: {execution_time:.3f}s")
            
            # Should handle high concurrency gracefully
            assert successful >= high_concurrency * 0.8  # At least 80% success rate
            assert execution_time < 30.0  # Should complete within 30 seconds
    
    @pytest.mark.asyncio
    async def test_large_data_processing(self):
        """Test processing of large data sets"""
        with patch('app.agents.pydantic_ai_manuscript_generator.manuscript_agent.run') as mock_run:
            # Mock large data response
            large_content = 'word ' * 50000  # 50,000 words
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {
                'manuscript': {'content': large_content},
                'word_count': 50000
            }
            mock_run.return_value = mock_result
            
            start_time = time.time()
            result = await generate_manuscript(
                trend_data={'category': 'business'},
                target_length=50000
            )
            execution_time = time.time() - start_time
            
            assert result.success is True
            assert execution_time < 15.0  # Should handle large data within 15 seconds
            print(f"Large data processing time: {execution_time:.3f}s")
    
    @pytest.mark.asyncio
    async def test_rapid_sequential_requests(self):
        """Test rapid sequential request handling"""
        with patch('app.agents.pydantic_ai_sales_monitor.sales_monitor.run') as mock_run:
            mock_result = Mock()
            mock_result.output.model_dump.return_value = {'report': 'test'}
            mock_run.return_value = mock_result
            
            start_time = time.time()
            
            # Rapid sequential requests
            for i in range(50):
                result = await monitor_sales_performance(date_range='last_7_days')
                assert result.success is True
            
            execution_time = time.time() - start_time
            avg_time_per_request = execution_time / 50
            
            print(f"Rapid sequential requests - Total: {execution_time:.3f}s, "
                  f"Avg per request: {avg_time_per_request:.3f}s")
            
            # Should maintain reasonable performance
            assert avg_time_per_request < 0.5  # Less than 500ms per request on average


class TestResourceUtilization:
    """Test CPU and resource utilization"""
    
    @pytest.mark.asyncio
    async def test_cpu_utilization(self):
        """Test CPU utilization during agent execution"""
        initial_cpu = psutil.cpu_percent(interval=1)
        
        with patch('app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent') as mock_execute:
            mock_execute.return_value = Mock(success=True, data={'result': 'test'})
            
            # Start CPU monitoring
            cpu_readings = []
            
            async def monitor_cpu():
                for _ in range(10):
                    cpu_readings.append(psutil.cpu_percent(interval=0.1))
                    await asyncio.sleep(0.1)
            
            # Execute agents while monitoring CPU
            monitor_task = asyncio.create_task(monitor_cpu())
            
            tasks = [
                execute_agent('trend_analyzer', {'categories': ['business']})
                for _ in range(20)
            ]
            
            await asyncio.gather(*tasks)
            await monitor_task
            
            avg_cpu = sum(cpu_readings) / len(cpu_readings)
            max_cpu = max(cpu_readings)
            
            print(f"CPU utilization - Initial: {initial_cpu:.1f}%, "
                  f"Average: {avg_cpu:.1f}%, Peak: {max_cpu:.1f}%")
            
            # CPU usage should be reasonable
            assert max_cpu < 90  # Should not max out CPU
    
    def test_thread_pool_efficiency(self):
        """Test thread pool efficiency for blocking operations"""
        def blocking_operation():
            time.sleep(0.1)  # Simulate blocking I/O
            return "completed"
        
        start_time = time.time()
        
        # Test with thread pool
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(blocking_operation) for _ in range(20)]
            results = [future.result() for future in futures]
        
        execution_time = time.time() - start_time
        
        assert len(results) == 20
        assert all(result == "completed" for result in results)
        # Should complete much faster than sequential execution (20 * 0.1 = 2s)
        assert execution_time < 1.0
        print(f"Thread pool execution time: {execution_time:.3f}s")


class TestStressTests:
    """Stress tests for system limits"""
    
    @pytest.mark.asyncio
    async def test_sustained_load(self):
        """Test system behavior under sustained load"""
        with patch('app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent') as mock_execute:
            mock_execute.return_value = Mock(success=True, data={'result': 'test'})
            
            duration = 10  # 10 seconds of sustained load
            start_time = time.time()
            completed_requests = 0
            
            async def sustained_requests():
                nonlocal completed_requests
                while time.time() - start_time < duration:
                    await execute_agent('trend_analyzer', {'categories': ['business']})
                    completed_requests += 1
                    await asyncio.sleep(0.01)  # Small delay to prevent overwhelming
            
            # Run multiple concurrent sustained request streams
            tasks = [sustained_requests() for _ in range(5)]
            await asyncio.gather(*tasks)
            
            actual_duration = time.time() - start_time
            requests_per_second = completed_requests / actual_duration
            
            print(f"Sustained load test - Duration: {actual_duration:.1f}s, "
                  f"Requests: {completed_requests}, RPS: {requests_per_second:.1f}")
            
            # Should maintain reasonable throughput
            assert requests_per_second > 10  # At least 10 requests per second
            assert completed_requests > 100  # Should complete significant number of requests
    
    @pytest.mark.asyncio
    async def test_error_recovery_under_load(self):
        """Test error recovery under high load conditions"""
        with patch('app.agents.pydantic_ai_manager.PydanticAIAgentManager.execute_agent') as mock_execute:
            # Simulate intermittent failures
            call_count = 0
            
            def side_effect(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count % 5 == 0:  # Every 5th call fails
                    raise Exception("Simulated failure")
                return Mock(success=True, data={'result': 'test'})
            
            mock_execute.side_effect = side_effect
            
            # Execute many requests with some failures
            tasks = [
                execute_agent('trend_analyzer', {'categories': ['business']})
                for _ in range(50)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            successful = sum(1 for r in results if hasattr(r, 'success') and r.success)
            failed = len(results) - successful
            
            print(f"Error recovery test - Successful: {successful}, Failed: {failed}")
            
            # Should handle failures gracefully
            assert successful > 0  # Some should succeed
            assert failed > 0  # Some should fail (as expected)
            assert successful >= failed  # More should succeed than fail


if __name__ == '__main__':
    pytest.main([__file__, '-v', '--tb=short', '-s'])
