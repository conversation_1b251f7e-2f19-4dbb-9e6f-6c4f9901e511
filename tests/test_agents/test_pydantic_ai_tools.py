"""
Comprehensive Test Suite for PydanticAI Tools
Tests all tool functions with full coverage of functionality, error handling, and edge cases.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from typing import Dict, Any, List

# Import all tools
from app.agents.pydantic_ai_tools import (
    get_user_books,
    scrape_amazon_bestsellers,
    scrape_reddit_trends,
    analyze_competitor_books,
    analyze_content_quality,
    extract_keywords,
    research_market_trends,
    validate_book_concept
)

from app.agents.pydantic_ai_base import (
    DatabaseDependencies,
    ScrapingDependencies,
    AIModelDependencies,
    TrendAnalysisDependencies
)

from pydantic_ai import RunContext


class TestDatabaseTools:
    """Test database-related tools"""
    
    @pytest.mark.asyncio
    async def test_get_user_books_success(self):
        """Test successful retrieval of user books"""
        mock_deps = DatabaseDependencies(user_id=123)
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Mock database query
        with patch('app.agents.pydantic_ai_tools.Book') as mock_book_model:
            mock_books = [
                Mock(
                    id=1,
                    title='Test Book 1',
                    author='Author 1',
                    status='published',
                    category='business',
                    word_count=5000,
                    created_at=datetime.now()
                ),
                Mock(
                    id=2,
                    title='Test Book 2',
                    author='Author 2',
                    status='draft',
                    category='fiction',
                    word_count=3000,
                    created_at=datetime.now()
                )
            ]
            
            mock_query = Mock()
            mock_query.filter.return_value.all.return_value = mock_books
            mock_book_model.query = mock_query
            
            result = await get_user_books(mock_ctx, user_id=123)
            
            assert isinstance(result, list)
            assert len(result) == 2
            assert result[0]['title'] == 'Test Book 1'
            assert result[1]['status'] == 'draft'
    
    @pytest.mark.asyncio
    async def test_get_user_books_empty_result(self):
        """Test retrieval when user has no books"""
        mock_deps = DatabaseDependencies(user_id=999)
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        with patch('app.agents.pydantic_ai_tools.Book') as mock_book_model:
            mock_query = Mock()
            mock_query.filter.return_value.all.return_value = []
            mock_book_model.query = mock_query
            
            result = await get_user_books(mock_ctx, user_id=999)
            
            assert isinstance(result, list)
            assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_get_user_books_database_error(self):
        """Test handling of database errors"""
        mock_deps = DatabaseDependencies(user_id=123)
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        with patch('app.agents.pydantic_ai_tools.Book') as mock_book_model:
            mock_book_model.query.filter.side_effect = Exception("Database connection failed")
            
            result = await get_user_books(mock_ctx, user_id=123)
            
            assert isinstance(result, list)
            assert len(result) == 0  # Should return empty list on error


class TestScrapingTools:
    """Test web scraping tools"""
    
    @pytest.mark.asyncio
    async def test_scrape_amazon_bestsellers_success(self):
        """Test successful Amazon bestsellers scraping"""
        mock_deps = ScrapingDependencies(headless=True)
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await scrape_amazon_bestsellers(mock_ctx, 'business', limit=10)
        
        assert isinstance(result, list)
        assert len(result) <= 10
        
        if result:  # If mock data is returned
            book = result[0]
            assert 'title' in book
            assert 'author' in book
            assert 'rank' in book
            assert 'price' in book
            assert 'category' in book
    
    @pytest.mark.asyncio
    async def test_scrape_amazon_bestsellers_large_limit(self):
        """Test scraping with large limit"""
        mock_deps = ScrapingDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await scrape_amazon_bestsellers(mock_ctx, 'fiction', limit=100)
        
        assert isinstance(result, list)
        # Should respect maximum limit (20 in mock implementation)
        assert len(result) <= 20
    
    @pytest.mark.asyncio
    async def test_scrape_reddit_trends_success(self):
        """Test successful Reddit trends scraping"""
        mock_deps = ScrapingDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        subreddits = ['productivity', 'business', 'selfimprovement']
        result = await scrape_reddit_trends(mock_ctx, subreddits, limit=5)
        
        assert isinstance(result, list)
        
        if result:  # If mock data is returned
            post = result[0]
            assert 'title' in post
            assert 'subreddit' in post
            assert 'score' in post
            assert 'num_comments' in post
    
    @pytest.mark.asyncio
    async def test_scrape_reddit_trends_empty_subreddits(self):
        """Test Reddit scraping with empty subreddit list"""
        mock_deps = ScrapingDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await scrape_reddit_trends(mock_ctx, [], limit=10)
        
        assert isinstance(result, list)
        assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_analyze_competitor_books_success(self):
        """Test successful competitor analysis"""
        mock_deps = ScrapingDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await analyze_competitor_books(mock_ctx, 'business', ['productivity'])
        
        assert isinstance(result, dict)
        assert 'total_books' in result
        assert 'avg_price' in result
        assert 'competition_level' in result
        assert 'market_saturation' in result
    
    @pytest.mark.asyncio
    async def test_analyze_competitor_books_error_handling(self):
        """Test error handling in competitor analysis"""
        mock_deps = ScrapingDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Test with invalid category
        result = await analyze_competitor_books(mock_ctx, '', [])
        
        # Should handle gracefully
        assert isinstance(result, dict)


class TestContentAnalysisTools:
    """Test content analysis tools"""
    
    @pytest.mark.asyncio
    async def test_analyze_content_quality_success(self):
        """Test successful content quality analysis"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        content = """
        This is a sample piece of content for testing quality analysis.
        It contains multiple sentences and paragraphs to evaluate.
        The content should be analyzed for readability, engagement, and value.
        """
        
        result = await analyze_content_quality(mock_ctx, content)
        
        assert isinstance(result, dict)
        assert 'overall_score' in result
        assert 'criteria_scores' in result
        assert 'word_count' in result
        assert 'recommendations' in result
        
        # Check score ranges
        assert 0 <= result['overall_score'] <= 100
    
    @pytest.mark.asyncio
    async def test_analyze_content_quality_with_criteria(self):
        """Test content analysis with custom criteria"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        content = "Short content for testing."
        criteria = ['readability', 'engagement', 'originality']
        
        result = await analyze_content_quality(mock_ctx, content, criteria)
        
        assert isinstance(result, dict)
        assert 'criteria_scores' in result
        
        # Should include all specified criteria
        for criterion in criteria:
            assert criterion in result['criteria_scores']
    
    @pytest.mark.asyncio
    async def test_analyze_content_quality_empty_content(self):
        """Test content analysis with empty content"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await analyze_content_quality(mock_ctx, "")
        
        assert isinstance(result, dict)
        # Should handle empty content gracefully
        assert 'error' in result or 'overall_score' in result
    
    @pytest.mark.asyncio
    async def test_extract_keywords_success(self):
        """Test successful keyword extraction"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        content = """
        Productivity and time management are essential skills for success.
        Effective productivity techniques include goal setting, prioritization,
        and time blocking. These productivity methods help improve efficiency
        and achieve better results in both personal and professional settings.
        """
        
        result = await extract_keywords(mock_ctx, content, max_keywords=5)
        
        assert isinstance(result, list)
        assert len(result) <= 5
        
        # Should extract relevant keywords
        if result:
            assert all(isinstance(keyword, str) for keyword in result)
            # Should include 'productivity' as it appears frequently
            assert any('productivity' in keyword.lower() for keyword in result)
    
    @pytest.mark.asyncio
    async def test_extract_keywords_custom_limit(self):
        """Test keyword extraction with custom limit"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        content = "business success productivity efficiency management leadership"
        
        result = await extract_keywords(mock_ctx, content, max_keywords=3)
        
        assert isinstance(result, list)
        assert len(result) <= 3
    
    @pytest.mark.asyncio
    async def test_extract_keywords_short_content(self):
        """Test keyword extraction with very short content"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await extract_keywords(mock_ctx, "test", max_keywords=10)
        
        assert isinstance(result, list)
        # Should handle short content gracefully


class TestMarketResearchTools:
    """Test market research and analysis tools"""
    
    @pytest.mark.asyncio
    async def test_research_market_trends_success(self):
        """Test successful market trends research"""
        mock_deps = TrendAnalysisDependencies(
            db_deps=DatabaseDependencies(),
            scraping_deps=ScrapingDependencies()
        )
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await research_market_trends(mock_ctx, 'business', timeframe_days=30)
        
        assert isinstance(result, dict)
        assert 'trend_score' in result
        assert 'market_size' in result
        assert 'competition_level' in result
        assert 'growth_rate' in result
        assert 'recommendations' in result
        
        # Check data types
        assert isinstance(result['trend_score'], (int, float))
        assert isinstance(result['recommendations'], list)
    
    @pytest.mark.asyncio
    async def test_research_market_trends_different_timeframes(self):
        """Test market research with different timeframes"""
        mock_deps = TrendAnalysisDependencies(
            db_deps=DatabaseDependencies(),
            scraping_deps=ScrapingDependencies()
        )
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Test short timeframe
        result_short = await research_market_trends(mock_ctx, 'fiction', timeframe_days=7)
        assert isinstance(result_short, dict)
        
        # Test long timeframe
        result_long = await research_market_trends(mock_ctx, 'fiction', timeframe_days=90)
        assert isinstance(result_long, dict)
    
    @pytest.mark.asyncio
    async def test_validate_book_concept_success(self):
        """Test successful book concept validation"""
        mock_deps = TrendAnalysisDependencies(
            db_deps=DatabaseDependencies(),
            scraping_deps=ScrapingDependencies()
        )
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        result = await validate_book_concept(
            mock_ctx,
            'The Ultimate Productivity Guide',
            'business',
            'professionals'
        )
        
        assert isinstance(result, dict)
        assert 'viability_score' in result
        assert 'market_potential' in result
        assert 'competition_analysis' in result
        assert 'recommendations' in result
        
        # Check score range
        assert 0 <= result['viability_score'] <= 100
    
    @pytest.mark.asyncio
    async def test_validate_book_concept_edge_cases(self):
        """Test book concept validation with edge cases"""
        mock_deps = TrendAnalysisDependencies(
            db_deps=DatabaseDependencies(),
            scraping_deps=ScrapingDependencies()
        )
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Test with empty title
        result_empty = await validate_book_concept(mock_ctx, '', 'business', 'general')
        assert isinstance(result_empty, dict)
        
        # Test with very long title
        long_title = 'A' * 500
        result_long = await validate_book_concept(mock_ctx, long_title, 'fiction', 'adults')
        assert isinstance(result_long, dict)


class TestErrorHandlingAndEdgeCases:
    """Test error handling and edge cases across all tools"""
    
    @pytest.mark.asyncio
    async def test_network_timeout_handling(self):
        """Test handling of network timeouts"""
        mock_deps = ScrapingDependencies(timeout=1)  # Very short timeout
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Should handle timeout gracefully
        result = await scrape_amazon_bestsellers(mock_ctx, 'business')
        assert isinstance(result, list)
    
    @pytest.mark.asyncio
    async def test_invalid_input_handling(self):
        """Test handling of invalid inputs"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Test with None content
        result = await analyze_content_quality(mock_ctx, None)
        assert isinstance(result, dict)
        assert 'error' in result or 'overall_score' in result
    
    @pytest.mark.asyncio
    async def test_large_data_processing(self):
        """Test processing of large data sets"""
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Create large content
        large_content = "word " * 10000  # 10,000 words
        
        result = await extract_keywords(mock_ctx, large_content, max_keywords=50)
        
        assert isinstance(result, list)
        assert len(result) <= 50
    
    @pytest.mark.asyncio
    async def test_concurrent_tool_execution(self):
        """Test concurrent execution of multiple tools"""
        import asyncio
        
        mock_deps = AIModelDependencies()
        mock_ctx = Mock()
        mock_ctx.deps = mock_deps
        
        # Execute multiple tools concurrently
        tasks = [
            analyze_content_quality(mock_ctx, "test content 1"),
            analyze_content_quality(mock_ctx, "test content 2"),
            extract_keywords(mock_ctx, "keyword test content"),
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        assert len(results) == 3
        # All should complete without exceptions
        assert all(not isinstance(result, Exception) for result in results)


if __name__ == '__main__':
    pytest.main([__file__, '-v', '--tb=short', '--cov=app.agents.pydantic_ai_tools'])
